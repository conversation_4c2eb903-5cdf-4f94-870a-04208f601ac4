# Design Document

## Overview

The Automated 3D Model & Bill of Materials Generator is a web-based application that transforms 2D engineering drawings into digital assets including Bills of Materials, weight calculations, and interactive 3D models. The system employs a microservices architecture with a React frontend, Python backend, and specialized processing modules for computer vision, OCR, and 3D model generation.

The application supports multi-user functionality with authentication, user dashboards, design sharing, and administrative controls. The system processes uploaded engineering drawings through a pipeline of analysis modules and presents results through an intuitive web interface.

## Architecture

### System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React Web Application]
        Auth[Authentication Module]
        Dashboard[User Dashboard]
        Viewer[3D Viewer - Three.js]
    end
    
    subgraph "API Gateway"
        Gateway[FastAPI Gateway]
    end
    
    subgraph "Backend Services"
        UserService[User Management Service]
        FileService[File Processing Service]
        AnalysisService[Drawing Analysis Service]
        BOMService[BOM Generation Service]
        WeightService[Weight Calculation Service]
        ModelService[3D Model Generation Service]
        ShareService[Sharing Service]
    end
    
    subgraph "Processing Engines"
        CVEngine[Computer Vision Engine - OpenCV]
        OCREngine[OCR Engine - Tesseract]
        CADEngine[3D Generation Engine - OpenSCAD]
    end
    
    subgraph "Data Layer"
        UserDB[(User Database - PostgreSQL)]
        FileDB[(File Storage - MinIO/S3)]
        MaterialDB[(Material Properties DB)]
        SessionStore[(Session Store - Redis)]
    end
    
    UI --> Gateway
    Auth --> Gateway
    Dashboard --> Gateway
    Viewer --> Gateway
    
    Gateway --> UserService
    Gateway --> FileService
    Gateway --> AnalysisService
    Gateway --> BOMService
    Gateway --> WeightService
    Gateway --> ModelService
    Gateway --> ShareService
    
    AnalysisService --> CVEngine
    AnalysisService --> OCREngine
    ModelService --> CADEngine
    
    UserService --> UserDB
    FileService --> FileDB
    WeightService --> MaterialDB
    UserService --> SessionStore
```

### Technology Stack

**Frontend:**
- React 18 with TypeScript for the main application
- Three.js for 3D model visualization
- Material-UI for consistent component design
- React Router for navigation
- Axios for API communication

**Backend:**
- FastAPI (Python) for the main API gateway
- Microservices architecture with individual Python services
- Celery for asynchronous task processing
- Redis for session management and task queuing

**Processing Engines:**
- OpenCV for computer vision and image processing
- Tesseract OCR for text recognition
- OpenSCAD for 3D model generation
- NumPy/SciPy for mathematical computations

**Data Storage:**
- PostgreSQL for user data and metadata
- MinIO/S3 for file storage
- Redis for caching and sessions

## Components and Interfaces

### Frontend Components

#### 1. Authentication System
- **Login Component**: User authentication with JWT tokens
- **Registration Component**: New user account creation
- **Password Reset Component**: Secure password recovery

#### 2. Main Application Layout
- **Navigation Bar**: User menu, logout, admin access
- **Sidebar**: Quick access to dashboard, recent designs
- **Main Content Area**: Dynamic content based on current view

#### 3. File Upload Interface
- **Drag & Drop Zone**: Visual file upload area
- **File Validation**: Real-time format and size checking
- **Progress Indicator**: Upload and processing status
- **Preview Panel**: Display uploaded drawing

#### 4. Processing Status Dashboard
- **Analysis Progress**: Real-time updates on drawing analysis
- **Stage Indicators**: Visual progress through processing pipeline
- **Error Display**: Clear error messages with recovery suggestions
- **Confidence Metrics**: Quality assessment of extracted data

#### 5. Results Display Interface
- **BOM Table**: Sortable, filterable parts list
- **Weight Summary**: Individual and total weight calculations
- **3D Viewer**: Interactive model with controls
- **Export Controls**: Download buttons for various formats

#### 6. User Dashboard
- **Design Gallery**: Grid view of user's designs with thumbnails
- **Search & Filter**: Find designs by name, date, material
- **Sharing Status**: Visual indicators for shared designs
- **Quick Actions**: Rename, delete, duplicate designs

#### 7. Sharing Interface
- **Permission Manager**: Grant/revoke access to other users
- **Shared Designs View**: Designs shared with current user
- **Collaboration Tools**: Comments and version tracking

#### 8. Admin Panel
- **User Management**: Add, remove, modify user accounts
- **System Monitoring**: Usage statistics and performance metrics
- **Material Database**: Manage material properties and densities

#### 9. Fabrication Progress Tracking
- **Progress Dashboard**: Visual overview of all parts and their fabrication stages
- **Stage Management**: Define and customize fabrication stages for different project types
- **Timeline View**: Gantt chart showing project timeline and critical path
- **Assignment Interface**: Assign parts to fabricators or teams
- **Progress Reports**: Generate charts and statistics for project status
- **Notification System**: Alerts for overdue parts or stage completions

### Backend Service Interfaces

#### 1. User Management Service API
```python
# User authentication and management
POST /api/auth/login
POST /api/auth/register
POST /api/auth/logout
GET /api/users/profile
PUT /api/users/profile
GET /api/users/designs
DELETE /api/users/{user_id}  # Admin only
```

#### 2. File Processing Service API
```python
# File upload and management
POST /api/files/upload
GET /api/files/{file_id}
DELETE /api/files/{file_id}
GET /api/files/{file_id}/status
```

#### 3. Drawing Analysis Service API
```python
# Core analysis functionality
POST /api/analysis/process
GET /api/analysis/{analysis_id}/status
GET /api/analysis/{analysis_id}/results
GET /api/analysis/{analysis_id}/confidence
```

#### 4. BOM Generation Service API
```python
# Bill of Materials generation
POST /api/bom/generate
GET /api/bom/{bom_id}
GET /api/bom/{bom_id}/export/csv
```

#### 5. 3D Model Service API
```python
# 3D model generation and export
POST /api/models/generate
GET /api/models/{model_id}
GET /api/models/{model_id}/export/stl
GET /api/models/{model_id}/export/step
```

#### 6. Sharing Service API
```python
# Design sharing and collaboration
POST /api/sharing/grant
DELETE /api/sharing/revoke
GET /api/sharing/shared-with-me
PUT /api/sharing/{share_id}/permissions
```

#### 7. Fabrication Progress Service API
```python
# Fabrication progress tracking
GET /api/fabrication/{design_id}/progress
PUT /api/fabrication/{design_id}/parts/{part_id}/status
POST /api/fabrication/{design_id}/stages
GET /api/fabrication/{design_id}/timeline
POST /api/fabrication/{design_id}/assignments
GET /api/fabrication/{design_id}/reports
GET /api/fabrication/dashboard  # Overview of all projects
```

## Data Models

### User Management Schema

```sql
-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- User sessions
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Design and Analysis Schema

```sql
-- Design projects
CREATE TABLE designs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    name VARCHAR(200) NOT NULL,
    original_filename VARCHAR(255),
    file_path VARCHAR(500),
    status VARCHAR(50) DEFAULT 'uploaded',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Analysis results
CREATE TABLE analysis_results (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    analysis_data JSONB,
    confidence_score DECIMAL(3,2),
    processing_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bill of Materials
CREATE TABLE bom_items (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    part_number VARCHAR(100),
    description TEXT,
    quantity INTEGER,
    material VARCHAR(100),
    volume DECIMAL(10,4),
    weight DECIMAL(10,4),
    unit_weight DECIMAL(10,4)
);

-- 3D Models
CREATE TABLE models_3d (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    model_file_path VARCHAR(500),
    openscad_script TEXT,
    generation_time INTEGER,
    file_size INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Sharing and Collaboration Schema

```sql
-- Design sharing
CREATE TABLE design_shares (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    owner_id INTEGER REFERENCES users(id),
    shared_with_id INTEGER REFERENCES users(id),
    permission_level VARCHAR(20) DEFAULT 'view', -- 'view' or 'edit'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(design_id, shared_with_id)
);
```

### Fabrication Progress Tracking Schema

```sql
-- Fabrication stages (customizable per project type)
CREATE TABLE fabrication_stages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Default fabrication stages
INSERT INTO fabrication_stages (name, description, order_index, is_default) VALUES
('Not Started', 'Part has not begun fabrication', 1, TRUE),
('Material Prep', 'Material cutting and preparation', 2, TRUE),
('Machining', 'CNC machining or manual machining operations', 3, TRUE),
('Welding/Assembly', 'Welding and assembly operations', 4, TRUE),
('Quality Check', 'Quality inspection and testing', 5, TRUE),
('Finishing', 'Surface treatment, painting, or coating', 6, TRUE),
('Completed', 'Part fabrication completed', 7, TRUE);

-- Part fabrication progress
CREATE TABLE part_fabrication_progress (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    part_id INTEGER REFERENCES bom_items(id),
    current_stage_id INTEGER REFERENCES fabrication_stages(id),
    assigned_to INTEGER REFERENCES users(id),
    started_at TIMESTAMP,
    estimated_completion TIMESTAMP,
    actual_completion TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Stage history for tracking progress over time
CREATE TABLE fabrication_stage_history (
    id SERIAL PRIMARY KEY,
    part_progress_id INTEGER REFERENCES part_fabrication_progress(id),
    stage_id INTEGER REFERENCES fabrication_stages(id),
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    duration_minutes INTEGER,
    notes TEXT,
    updated_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Team/fabricator assignments
CREATE TABLE fabrication_teams (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    lead_user_id INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Team member assignments
CREATE TABLE team_members (
    id SERIAL PRIMARY KEY,
    team_id INTEGER REFERENCES fabrication_teams(id),
    user_id INTEGER REFERENCES users(id),
    role VARCHAR(50),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(team_id, user_id)
);
```

### Material Properties Schema

```sql
-- Material database
CREATE TABLE materials (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    density DECIMAL(8,4) NOT NULL, -- kg/m³
    category VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Default materials with comprehensive metal fabrication grades
INSERT INTO materials (name, density, category, description) VALUES
-- Steel grades
('Carbon Steel A36', 7850.0, 'Steel', 'Structural steel for construction and fabrication'),
('Carbon Steel 1018', 7870.0, 'Steel', 'Low carbon steel for machining and welding'),
('Carbon Steel 1045', 7870.0, 'Steel', 'Medium carbon steel for shafts and gears'),
('Stainless Steel 304', 8000.0, 'Steel', 'Austenitic stainless steel, corrosion resistant'),
('Stainless Steel 316', 8000.0, 'Steel', 'Marine grade stainless steel'),
('Stainless Steel 410', 7750.0, 'Steel', 'Martensitic stainless steel, hardenable'),
('Tool Steel O1', 7850.0, 'Steel', 'Oil hardening tool steel'),
('Tool Steel A2', 7860.0, 'Steel', 'Air hardening tool steel'),
('Alloy Steel 4140', 7850.0, 'Steel', 'Chromium-molybdenum alloy steel'),
('Alloy Steel 4340', 7850.0, 'Steel', 'Nickel-chromium-molybdenum alloy steel'),

-- Iron grades
('Cast Iron Gray', 7200.0, 'Iron', 'Gray cast iron for engine blocks and housings'),
('Cast Iron Ductile', 7100.0, 'Iron', 'Ductile iron with improved toughness'),
('Wrought Iron', 7700.0, 'Iron', 'Traditional wrought iron for decorative work'),

-- Aluminum grades
('Aluminum 1100', 2710.0, 'Aluminum', 'Pure aluminum, excellent corrosion resistance'),
('Aluminum 2024', 2780.0, 'Aluminum', 'High strength aluminum for aerospace'),
('Aluminum 3003', 2730.0, 'Aluminum', 'General purpose aluminum with manganese'),
('Aluminum 5052', 2680.0, 'Aluminum', 'Marine grade aluminum with magnesium'),
('Aluminum 6061', 2700.0, 'Aluminum', 'Structural aluminum, heat treatable'),
('Aluminum 6063', 2700.0, 'Aluminum', 'Architectural aluminum for extrusions'),
('Aluminum 7075', 2810.0, 'Aluminum', 'High strength aluminum for aircraft'),

-- Other common fabrication metals
('Brass 360', 8500.0, 'Brass', 'Free machining brass'),
('Brass 260', 8530.0, 'Brass', 'Cartridge brass, 70% copper'),
('Bronze 932', 8800.0, 'Bronze', 'Bearing bronze with tin and lead'),
('Copper 110', 8960.0, 'Copper', 'Electrolytic tough pitch copper'),
('Titanium Grade 2', 4510.0, 'Titanium', 'Commercially pure titanium'),
('Inconel 625', 8440.0, 'Superalloy', 'Nickel-chromium superalloy'),

-- Plastics for comparison
('ABS Plastic', 1050.0, 'Plastic', 'General purpose thermoplastic'),
('PLA Plastic', 1240.0, 'Plastic', '3D printing filament'),
('HDPE', 950.0, 'Plastic', 'High density polyethylene'),
('Nylon 6', 1140.0, 'Plastic', 'Engineering thermoplastic');
```

## Error Handling

### Error Classification System

#### 1. Input Validation Errors
- **File Format Errors**: Unsupported file types
- **File Size Errors**: Files exceeding size limits
- **Authentication Errors**: Invalid credentials or expired sessions

#### 2. Processing Errors
- **Image Quality Errors**: Poor drawing quality affecting analysis
- **OCR Errors**: Text recognition failures
- **Computer Vision Errors**: Feature detection failures
- **3D Generation Errors**: OpenSCAD script execution failures

#### 3. System Errors
- **Resource Errors**: Memory or processing time limits exceeded
- **Storage Errors**: File system or database issues
- **Network Errors**: Service communication failures

### Error Response Format

```json
{
    "error": {
        "code": "ANALYSIS_FAILED",
        "message": "Unable to extract geometric features from drawing",
        "details": {
            "confidence_score": 0.23,
            "failed_sections": ["title_block", "dimensions"],
            "suggestions": [
                "Try uploading a higher resolution image",
                "Ensure drawing has clear, dark lines"
            ]
        },
        "timestamp": "2025-01-18T10:30:00Z"
    }
}
```

### Recovery Strategies

#### 1. Graceful Degradation
- Partial results when some analysis fails
- Manual input options for failed OCR sections
- Default material properties when specifications unclear

#### 2. User Guidance
- Clear error messages with actionable suggestions
- Visual indicators for low-confidence areas
- Help documentation for common issues

#### 3. Retry Mechanisms
- Automatic retry for transient failures
- User-initiated reprocessing options
- Progressive enhancement of results

## Testing Strategy

### Unit Testing
- **Frontend Components**: React component testing with Jest and React Testing Library
- **Backend Services**: Python unit tests with pytest
- **Processing Engines**: Isolated testing of OpenCV, OCR, and OpenSCAD modules
- **Database Operations**: Repository pattern testing with test databases

### Integration Testing
- **API Integration**: End-to-end API testing with test clients
- **Service Communication**: Inter-service communication testing
- **File Processing Pipeline**: Complete workflow testing with sample drawings
- **Authentication Flow**: User registration, login, and session management

### Performance Testing
- **Load Testing**: Concurrent user simulation
- **Processing Performance**: Large file handling and processing time limits
- **Database Performance**: Query optimization and indexing validation
- **3D Generation Performance**: OpenSCAD execution time monitoring

### User Acceptance Testing
- **Workflow Testing**: Complete user journeys from upload to export
- **Cross-browser Testing**: Compatibility across major browsers
- **Mobile Responsiveness**: Touch interface and responsive design
- **Accessibility Testing**: WCAG compliance for inclusive design

### Test Data Strategy
- **Sample Drawings**: Curated set of engineering drawings with known outputs
- **Edge Cases**: Poor quality images, complex geometries, missing information
- **Performance Benchmarks**: Large files and complex assemblies
- **Security Testing**: Authentication bypass attempts and data access validation