FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    awscli \
    cron \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy backup scripts
COPY . .

# Make scripts executable
RUN chmod +x backup.sh restore.sh

# Create backup directory
RUN mkdir -p /backups

# Set up cron job for automated backups
RUN echo "0 2 * * * /app/backup.sh" | crontab -

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start cron and backup service
CMD ["python", "backup_service.py"]