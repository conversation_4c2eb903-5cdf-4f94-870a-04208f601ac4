#!/usr/bin/env python3
"""
Automated Backup Service for BOM Generator
Handles database and file storage backups with retention policies
"""

import os
import sys
import time
import logging
import schedule
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

import boto3
from minio import Minio
from minio.error import S3Error
import psycopg2
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/backup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
DATABASE_URL = os.getenv('DATABASE_URL')
MINIO_ENDPOINT = os.getenv('MINIO_ENDPOINT', 'minio:9000')
MINIO_ACCESS_KEY = os.getenv('MINIO_ACCESS_KEY')
MINIO_SECRET_KEY = os.getenv('MINIO_SECRET_KEY')
BACKUP_S3_BUCKET = os.getenv('BACKUP_S3_BUCKET')
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
BACKUP_RETENTION_DAYS = int(os.getenv('BACKUP_RETENTION_DAYS', '30'))

app = FastAPI(title="Backup Service")

class BackupService:
    def __init__(self):
        self.backup_dir = Path('/backups')
        self.backup_dir.mkdir(exist_ok=True)
        
        # Initialize MinIO client
        self.minio_client = Minio(
            MINIO_ENDPOINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False
        )
        
        # Initialize S3 client for remote backups
        if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=AWS_ACCESS_KEY_ID,
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY
            )
        else:
            self.s3_client = None
            logger.warning("AWS credentials not provided, remote backup disabled")

    def backup_database(self) -> Optional[str]:
        """Create database backup using pg_dump"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'database_backup_{timestamp}.sql'
            backup_path = self.backup_dir / backup_filename
            
            # Extract database connection details
            db_url = DATABASE_URL.replace('postgresql://', '')
            user_pass, host_db = db_url.split('@')
            user, password = user_pass.split(':')
            host_port, database = host_db.split('/')
            host, port = host_port.split(':')
            
            # Set environment variables for pg_dump
            env = os.environ.copy()
            env['PGPASSWORD'] = password
            
            # Run pg_dump
            cmd = [
                'pg_dump',
                '-h', host,
                '-p', port,
                '-U', user,
                '-d', database,
                '--verbose',
                '--no-password',
                '--format=custom',
                '--compress=9',
                '--file', str(backup_path)
            ]
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Database backup created: {backup_filename}")
                return str(backup_path)
            else:
                logger.error(f"Database backup failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Database backup error: {str(e)}")
            return None

    def backup_files(self) -> Optional[str]:
        """Create backup of MinIO file storage"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'files_backup_{timestamp}.tar.gz'
            backup_path = self.backup_dir / backup_filename
            
            # Create temporary directory for file downloads
            temp_dir = self.backup_dir / f'temp_{timestamp}'
            temp_dir.mkdir(exist_ok=True)
            
            try:
                # List all buckets and objects
                buckets = self.minio_client.list_buckets()
                
                for bucket in buckets:
                    bucket_dir = temp_dir / bucket.name
                    bucket_dir.mkdir(exist_ok=True)
                    
                    objects = self.minio_client.list_objects(bucket.name, recursive=True)
                    
                    for obj in objects:
                        object_path = bucket_dir / obj.object_name
                        object_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        # Download object
                        self.minio_client.fget_object(
                            bucket.name,
                            obj.object_name,
                            str(object_path)
                        )
                
                # Create tar.gz archive
                cmd = ['tar', '-czf', str(backup_path), '-C', str(temp_dir), '.']
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"Files backup created: {backup_filename}")
                    return str(backup_path)
                else:
                    logger.error(f"Files backup failed: {result.stderr}")
                    return None
                    
            finally:
                # Clean up temporary directory
                subprocess.run(['rm', '-rf', str(temp_dir)])
                
        except Exception as e:
            logger.error(f"Files backup error: {str(e)}")
            return None

    def upload_to_s3(self, backup_path: str, backup_type: str) -> bool:
        """Upload backup to S3 for remote storage"""
        if not self.s3_client or not BACKUP_S3_BUCKET:
            return False
            
        try:
            backup_filename = Path(backup_path).name
            s3_key = f"{backup_type}/{backup_filename}"
            
            self.s3_client.upload_file(backup_path, BACKUP_S3_BUCKET, s3_key)
            logger.info(f"Backup uploaded to S3: {s3_key}")
            return True
            
        except Exception as e:
            logger.error(f"S3 upload error: {str(e)}")
            return False

    def cleanup_old_backups(self):
        """Remove backups older than retention period"""
        try:
            cutoff_date = datetime.now() - timedelta(days=BACKUP_RETENTION_DAYS)
            
            # Clean up local backups
            for backup_file in self.backup_dir.glob('*.sql'):
                if backup_file.stat().st_mtime < cutoff_date.timestamp():
                    backup_file.unlink()
                    logger.info(f"Removed old backup: {backup_file.name}")
            
            for backup_file in self.backup_dir.glob('*.tar.gz'):
                if backup_file.stat().st_mtime < cutoff_date.timestamp():
                    backup_file.unlink()
                    logger.info(f"Removed old backup: {backup_file.name}")
            
            # Clean up S3 backups if configured
            if self.s3_client and BACKUP_S3_BUCKET:
                try:
                    response = self.s3_client.list_objects_v2(Bucket=BACKUP_S3_BUCKET)
                    
                    if 'Contents' in response:
                        for obj in response['Contents']:
                            if obj['LastModified'].replace(tzinfo=None) < cutoff_date:
                                self.s3_client.delete_object(
                                    Bucket=BACKUP_S3_BUCKET,
                                    Key=obj['Key']
                                )
                                logger.info(f"Removed old S3 backup: {obj['Key']}")
                except Exception as e:
                    logger.error(f"S3 cleanup error: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Backup cleanup error: {str(e)}")

    def run_full_backup(self):
        """Run complete backup process"""
        logger.info("Starting full backup process")
        
        # Backup database
        db_backup_path = self.backup_database()
        if db_backup_path:
            self.upload_to_s3(db_backup_path, 'database')
        
        # Backup files
        files_backup_path = self.backup_files()
        if files_backup_path:
            self.upload_to_s3(files_backup_path, 'files')
        
        # Cleanup old backups
        self.cleanup_old_backups()
        
        logger.info("Full backup process completed")

    def restore_database(self, backup_path: str) -> bool:
        """Restore database from backup"""
        try:
            # Extract database connection details
            db_url = DATABASE_URL.replace('postgresql://', '')
            user_pass, host_db = db_url.split('@')
            user, password = user_pass.split(':')
            host_port, database = host_db.split('/')
            host, port = host_port.split(':')
            
            # Set environment variables for pg_restore
            env = os.environ.copy()
            env['PGPASSWORD'] = password
            
            # Run pg_restore
            cmd = [
                'pg_restore',
                '-h', host,
                '-p', port,
                '-U', user,
                '-d', database,
                '--verbose',
                '--no-password',
                '--clean',
                '--if-exists',
                backup_path
            ]
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Database restored from: {backup_path}")
                return True
            else:
                logger.error(f"Database restore failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Database restore error: {str(e)}")
            return False

# Initialize backup service
backup_service = BackupService()

# Schedule backups
schedule.every().day.at("02:00").do(backup_service.run_full_backup)
schedule.every().sunday.at("01:00").do(backup_service.cleanup_old_backups)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return JSONResponse({"status": "healthy", "service": "backup"})

@app.post("/backup/database")
async def trigger_database_backup():
    """Manually trigger database backup"""
    backup_path = backup_service.backup_database()
    if backup_path:
        return JSONResponse({"status": "success", "backup_path": backup_path})
    else:
        return JSONResponse({"status": "error", "message": "Database backup failed"}, status_code=500)

@app.post("/backup/files")
async def trigger_files_backup():
    """Manually trigger files backup"""
    backup_path = backup_service.backup_files()
    if backup_path:
        return JSONResponse({"status": "success", "backup_path": backup_path})
    else:
        return JSONResponse({"status": "error", "message": "Files backup failed"}, status_code=500)

@app.post("/backup/full")
async def trigger_full_backup():
    """Manually trigger full backup"""
    try:
        backup_service.run_full_backup()
        return JSONResponse({"status": "success", "message": "Full backup completed"})
    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)

@app.get("/backup/status")
async def backup_status():
    """Get backup status and recent backups"""
    try:
        backups = []
        for backup_file in backup_service.backup_dir.glob('*'):
            if backup_file.is_file():
                stat = backup_file.stat()
                backups.append({
                    "filename": backup_file.name,
                    "size": stat.st_size,
                    "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "type": "database" if backup_file.suffix == '.sql' else "files"
                })
        
        return JSONResponse({
            "status": "success",
            "backups": sorted(backups, key=lambda x: x['created'], reverse=True)[:10]
        })
    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)

def run_scheduler():
    """Run the backup scheduler"""
    while True:
        schedule.run_pending()
        time.sleep(60)

if __name__ == "__main__":
    import threading
    
    # Start scheduler in background thread
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    
    # Start FastAPI server
    uvicorn.run(app, host="0.0.0.0", port=8080)