"""
Main FastAPI application entry point for BOM Generator
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
from dotenv import load_dotenv

# Import API routers
from api.auth import router as auth_router
from api.files import router as files_router
from api.ocr import router as ocr_router
from api.analysis import router as analysis_router
from api.bom import router as bom_router
from api.weight import router as weight_router
from api.models import router as models_router
from api.sharing import router as sharing_router
from api.admin import router as admin_router
from api.fabrication import router as fabrication_router

# Load environment variables
load_dotenv()

# Create FastAPI application
app = FastAPI(
    title="BOM Generator API",
    description="Automated 3D Model & Bill of Materials Generator API",
    version="1.0.0"
)

# Configure CORS
origins = [
    "http://localhost:3000",  # React development server
    "http://127.0.0.1:3000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(auth_router)
app.include_router(files_router)
app.include_router(ocr_router)
app.include_router(analysis_router)
app.include_router(bom_router)
app.include_router(weight_router)
app.include_router(models_router)
app.include_router(sharing_router)
app.include_router(admin_router)
app.include_router(fabrication_router)

@app.get("/")
async def root():
    """Root endpoint for health check"""
    return {"message": "BOM Generator API is running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "BOM Generator API",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)