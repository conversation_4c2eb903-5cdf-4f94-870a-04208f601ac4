"""
Weight Calculation Service

This service calculates volumes and weights for BOM items based on 2D views,
dimensional data, and material properties. It provides individual part weights
and total assembly weights with detailed breakdowns.
"""

import logging
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session

from models.design import Design, AnalysisResult, BOMItem
from models.material import Material
from database.connection import get_database as get_db

logger = logging.getLogger(__name__)


class VolumeCalculationMethod(Enum):
    LINEAR_EXTRUSION = "linear_extrusion"
    CYLINDRICAL = "cylindrical"
    RECTANGULAR = "rectangular"
    COMPLEX_GEOMETRY = "complex_geometry"
    MANUAL_INPUT = "manual_input"


@dataclass
class GeometricDimensions:
    """Container for geometric dimensions extracted from drawings"""
    length: Optional[float] = None
    width: Optional[float] = None
    height: Optional[float] = None
    diameter: Optional[float] = None
    radius: Optional[float] = None
    thickness: Optional[float] = None
    area: Optional[float] = None
    perimeter: Optional[float] = None


@dataclass
class VolumeCalculationResult:
    """Result of volume calculation for a part"""
    volume_cm3: float
    calculation_method: VolumeCalculationMethod
    confidence: float
    dimensions_used: GeometricDimensions
    notes: List[str]
    assumptions: List[str]


@dataclass
class WeightCalculationResult:
    """Result of weight calculation for a part"""
    part_number: str
    material_name: str
    material_density: float  # kg/m³
    volume_cm3: float
    unit_weight_kg: float
    quantity: int
    total_weight_kg: float
    confidence: float
    calculation_notes: List[str]


@dataclass
class AssemblyWeightSummary:
    """Summary of total assembly weight calculation"""
    total_weight_kg: float
    part_count: int
    unique_materials: List[str]
    weight_breakdown: List[WeightCalculationResult]
    heaviest_part: Optional[WeightCalculationResult]
    lightest_part: Optional[WeightCalculationResult]
    material_distribution: Dict[str, float]  # Material -> total weight
    confidence_score: float
    missing_data_flags: List[str]


class VolumeCalculator:
    """Calculates volumes from 2D views and dimensional data"""
    
    def __init__(self):
        self.default_thickness = 5.0  # mm - default thickness for sheet metal parts
        self.min_dimension = 0.1  # mm - minimum dimension to prevent zero volumes
        
    def calculate_volume_from_analysis(self, analysis_data: Dict[str, Any], 
                                     part_number: str) -> VolumeCalculationResult:
        """
        Calculate volume for a specific part from analysis data
        
        Args:
            analysis_data: Complete analysis results
            part_number: Part number to calculate volume for
            
        Returns:
            Volume calculation result
        """
        try:
            # Extract geometric data for this part
            dimensions = self._extract_part_dimensions(analysis_data, part_number)
            
            # Determine best calculation method
            method = self._determine_calculation_method(dimensions, analysis_data)
            
            # Calculate volume based on method
            if method == VolumeCalculationMethod.RECTANGULAR:
                return self._calculate_rectangular_volume(dimensions)
            elif method == VolumeCalculationMethod.CYLINDRICAL:
                return self._calculate_cylindrical_volume(dimensions)
            elif method == VolumeCalculationMethod.LINEAR_EXTRUSION:
                return self._calculate_extrusion_volume(dimensions, analysis_data, part_number)
            else:
                return self._calculate_complex_volume(dimensions, analysis_data, part_number)
                
        except Exception as e:
            logger.error(f"Volume calculation failed for part {part_number}: {str(e)}")
            return self._create_fallback_volume_result(part_number)
    
    def _extract_part_dimensions(self, analysis_data: Dict[str, Any], 
                               part_number: str) -> GeometricDimensions:
        """Extract dimensional data for a specific part"""
        dimensions = GeometricDimensions()
        
        try:
            cv_results = analysis_data.get('cv_results', {})
            ocr_results = analysis_data.get('ocr_results', {})
            
            # Look for dimensions in OCR results
            extracted_texts = ocr_results.get('extracted_texts', [])
            dimensions_found = []
            
            for text_item in extracted_texts:
                text_content = text_item.get('text', '').strip()
                text_type = text_item.get('text_type', '')
                
                # Look for dimensional annotations near this part
                if text_type == 'dimension' or 'dimension' in text_item.get('context', ''):
                    dimension_value = self._parse_dimension_text(text_content)
                    if dimension_value:
                        dimensions_found.append(dimension_value)
            
            # Extract from geometric features
            features = cv_results.get('features', [])
            geometric_dims = self._extract_from_geometric_features(features, part_number)
            
            # Combine and assign dimensions
            all_dimensions = dimensions_found + geometric_dims
            self._assign_dimensions_to_structure(dimensions, all_dimensions)
            
        except Exception as e:
            logger.error(f"Dimension extraction failed for part {part_number}: {str(e)}")
        
        return dimensions
    
    def _parse_dimension_text(self, text: str) -> Optional[float]:
        """Parse dimension value from text"""
        import re
        
        # Common dimension patterns
        patterns = [
            r'(\d+\.?\d*)\s*mm',  # 25.4 mm
            r'(\d+\.?\d*)\s*cm',  # 2.54 cm
            r'(\d+\.?\d*)\s*in',  # 1.0 in
            r'(\d+\.?\d*)\s*"',   # 1.0"
            r'(\d+\.?\d*)',       # Just numbers
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    value = float(match.group(1))
                    
                    # Convert to mm if needed
                    if 'cm' in text.lower():
                        value *= 10
                    elif 'in' in text.lower() or '"' in text:
                        value *= 25.4
                    
                    return value
                except ValueError:
                    continue
        
        return None
    
    def _extract_from_geometric_features(self, features: List[Dict], 
                                       part_number: str) -> List[float]:
        """Extract dimensions from geometric features"""
        dimensions = []
        
        try:
            for feature in features:
                feature_type = feature.get('feature_type', '')
                
                if feature_type == 'rectangle':
                    width = feature.get('width')
                    height = feature.get('height')
                    if width:
                        dimensions.append(width)
                    if height:
                        dimensions.append(height)
                
                elif feature_type == 'circle':
                    diameter = feature.get('diameter')
                    radius = feature.get('radius')
                    if diameter:
                        dimensions.append(diameter)
                    elif radius:
                        dimensions.append(radius * 2)  # Convert to diameter
                
                elif feature_type == 'line':
                    length = feature.get('length')
                    if length:
                        dimensions.append(length)
        
        except Exception as e:
            logger.error(f"Geometric feature extraction failed: {str(e)}")
        
        return dimensions
    
    def _assign_dimensions_to_structure(self, dimensions: GeometricDimensions, 
                                      values: List[float]) -> None:
        """Assign dimension values to the structure"""
        if not values:
            return
        
        # Sort dimensions by size (largest first)
        sorted_values = sorted(values, reverse=True)
        
        # Assign based on typical part conventions
        if len(sorted_values) >= 1:
            dimensions.length = sorted_values[0]
        
        if len(sorted_values) >= 2:
            dimensions.width = sorted_values[1]
        
        if len(sorted_values) >= 3:
            dimensions.height = sorted_values[2]
        else:
            # Use default thickness if no third dimension
            dimensions.thickness = self.default_thickness
        
        # Check if this might be a cylindrical part
        if len(sorted_values) >= 2:
            ratio = sorted_values[0] / sorted_values[1]
            if 0.8 <= ratio <= 1.2:  # Nearly equal dimensions suggest circular
                dimensions.diameter = sorted_values[0]
                dimensions.radius = sorted_values[0] / 2
    
    def _determine_calculation_method(self, dimensions: GeometricDimensions, 
                                    analysis_data: Dict) -> VolumeCalculationMethod:
        """Determine the best volume calculation method"""
        
        # Check for cylindrical geometry
        if dimensions.diameter or dimensions.radius:
            return VolumeCalculationMethod.CYLINDRICAL
        
        # Check for rectangular geometry
        if dimensions.length and dimensions.width:
            return VolumeCalculationMethod.RECTANGULAR
        
        # Check for 2D profile that can be extruded
        cv_results = analysis_data.get('cv_results', {})
        if cv_results.get('profiles') or cv_results.get('closed_contours'):
            return VolumeCalculationMethod.LINEAR_EXTRUSION
        
        return VolumeCalculationMethod.COMPLEX_GEOMETRY
    
    def _calculate_rectangular_volume(self, dimensions: GeometricDimensions) -> VolumeCalculationResult:
        """Calculate volume for rectangular parts"""
        length = dimensions.length or 0
        width = dimensions.width or 0
        height = dimensions.height or dimensions.thickness or self.default_thickness
        
        # Ensure minimum dimensions
        length = max(length, self.min_dimension)
        width = max(width, self.min_dimension)
        height = max(height, self.min_dimension)
        
        # Convert mm to cm for volume calculation
        volume_cm3 = (length * width * height) / 1000  # mm³ to cm³
        
        confidence = 0.8 if all([dimensions.length, dimensions.width]) else 0.4
        
        notes = []
        assumptions = []
        
        if not dimensions.height and not dimensions.thickness:
            assumptions.append(f"Used default thickness of {self.default_thickness}mm")
            confidence *= 0.7
        
        if length < 1 or width < 1:
            notes.append("Very small dimensions detected - verify accuracy")
            confidence *= 0.8
        
        return VolumeCalculationResult(
            volume_cm3=volume_cm3,
            calculation_method=VolumeCalculationMethod.RECTANGULAR,
            confidence=confidence,
            dimensions_used=dimensions,
            notes=notes,
            assumptions=assumptions
        )
    
    def _calculate_cylindrical_volume(self, dimensions: GeometricDimensions) -> VolumeCalculationResult:
        """Calculate volume for cylindrical parts"""
        radius = dimensions.radius or (dimensions.diameter / 2 if dimensions.diameter else 0)
        height = dimensions.height or dimensions.length or self.default_thickness
        
        # Ensure minimum dimensions
        radius = max(radius, self.min_dimension / 2)
        height = max(height, self.min_dimension)
        
        # Calculate volume: π * r² * h
        volume_mm3 = math.pi * (radius ** 2) * height
        volume_cm3 = volume_mm3 / 1000  # mm³ to cm³
        
        confidence = 0.8 if (dimensions.radius or dimensions.diameter) else 0.4
        
        notes = []
        assumptions = []
        
        if not dimensions.height and not dimensions.length:
            assumptions.append(f"Used default height of {self.default_thickness}mm")
            confidence *= 0.7
        
        return VolumeCalculationResult(
            volume_cm3=volume_cm3,
            calculation_method=VolumeCalculationMethod.CYLINDRICAL,
            confidence=confidence,
            dimensions_used=dimensions,
            notes=notes,
            assumptions=assumptions
        )
    
    def _calculate_extrusion_volume(self, dimensions: GeometricDimensions, 
                                  analysis_data: Dict, part_number: str) -> VolumeCalculationResult:
        """Calculate volume using linear extrusion method"""
        try:
            cv_results = analysis_data.get('cv_results', {})
            
            # Try to get profile area from CV results
            profiles = cv_results.get('profiles', [])
            area_mm2 = 0
            
            for profile in profiles:
                if profile.get('part_number') == part_number or not profile.get('part_number'):
                    area_mm2 = profile.get('area', 0)
                    break
            
            # Fallback to estimated area from dimensions
            if area_mm2 == 0 and dimensions.length and dimensions.width:
                area_mm2 = dimensions.length * dimensions.width
            
            # Use thickness or default
            thickness = dimensions.thickness or dimensions.height or self.default_thickness
            
            # Calculate volume
            volume_mm3 = area_mm2 * thickness
            volume_cm3 = volume_mm3 / 1000
            
            confidence = 0.6 if area_mm2 > 0 else 0.3
            
            notes = []
            assumptions = []
            
            if area_mm2 == 0:
                assumptions.append("Could not determine profile area - used estimated dimensions")
                confidence *= 0.5
            
            if not dimensions.thickness:
                assumptions.append(f"Used default thickness of {self.default_thickness}mm")
                confidence *= 0.8
            
            return VolumeCalculationResult(
                volume_cm3=volume_cm3,
                calculation_method=VolumeCalculationMethod.LINEAR_EXTRUSION,
                confidence=confidence,
                dimensions_used=dimensions,
                notes=notes,
                assumptions=assumptions
            )
            
        except Exception as e:
            logger.error(f"Extrusion volume calculation failed: {str(e)}")
            return self._create_fallback_volume_result(part_number)
    
    def _calculate_complex_volume(self, dimensions: GeometricDimensions, 
                                analysis_data: Dict, part_number: str) -> VolumeCalculationResult:
        """Calculate volume for complex geometries"""
        # For complex parts, use best available dimensions
        if dimensions.length and dimensions.width:
            return self._calculate_rectangular_volume(dimensions)
        elif dimensions.diameter or dimensions.radius:
            return self._calculate_cylindrical_volume(dimensions)
        else:
            return self._create_fallback_volume_result(part_number)
    
    def _create_fallback_volume_result(self, part_number: str) -> VolumeCalculationResult:
        """Create a fallback volume result when calculation fails"""
        # Use a small default volume
        default_volume = 10.0  # cm³
        
        return VolumeCalculationResult(
            volume_cm3=default_volume,
            calculation_method=VolumeCalculationMethod.MANUAL_INPUT,
            confidence=0.1,
            dimensions_used=GeometricDimensions(),
            notes=[f"Could not calculate volume for part {part_number}"],
            assumptions=[f"Used default volume of {default_volume} cm³ - manual verification required"]
        )


class MaterialMatcher:
    """Matches extracted material names to database materials"""
    
    def __init__(self):
        self.material_cache = {}
        self.default_material = "Carbon Steel A36"
    
    def get_material_for_part(self, material_name: str, db: Session) -> Material:
        """
        Get material object for a part, with fuzzy matching
        
        Args:
            material_name: Material name from BOM
            db: Database session
            
        Returns:
            Material object
        """
        if not material_name:
            return self._get_default_material(db)
        
        # Check cache first
        cache_key = material_name.lower().strip()
        if cache_key in self.material_cache:
            return self.material_cache[cache_key]
        
        # Try exact match first
        material = db.query(Material).filter(
            Material.name.ilike(f"%{material_name}%")
        ).first()
        
        if material:
            self.material_cache[cache_key] = material
            return material
        
        # Try fuzzy matching
        material = self._fuzzy_match_material(material_name, db)
        if material:
            self.material_cache[cache_key] = material
            return material
        
        # Return default material
        return self._get_default_material(db)
    
    def _fuzzy_match_material(self, material_name: str, db: Session) -> Optional[Material]:
        """Perform fuzzy matching on material names"""
        material_lower = material_name.lower()
        
        # Get all materials
        all_materials = db.query(Material).all()
        
        # Simple keyword matching
        keywords = material_lower.split()
        best_match = None
        best_score = 0
        
        for material in all_materials:
            material_name_lower = material.name.lower()
            score = 0
            
            # Count matching keywords
            for keyword in keywords:
                if keyword in material_name_lower:
                    score += 1
            
            # Bonus for category match
            if material.category and material.category.lower() in material_lower:
                score += 0.5
            
            if score > best_score:
                best_score = score
                best_match = material
        
        # Only return if we have a reasonable match
        return best_match if best_score >= 1 else None
    
    def _get_default_material(self, db: Session) -> Material:
        """Get the default material"""
        material = db.query(Material).filter(
            Material.name == self.default_material
        ).first()
        
        if not material:
            # Create default material if it doesn't exist
            material = Material(
                name=self.default_material,
                density=7850.0,
                category="Steel",
                description="Default structural steel"
            )
            db.add(material)
            db.commit()
            db.refresh(material)
        
        return material


class WeightCalculationService:
    """Main service for weight calculations"""
    
    def __init__(self):
        self.volume_calculator = VolumeCalculator()
        self.material_matcher = MaterialMatcher()
    
    def calculate_weights_for_design(self, design_id: int) -> AssemblyWeightSummary:
        """
        Calculate weights for all parts in a design
        
        Args:
            design_id: Design ID to calculate weights for
            
        Returns:
            Complete assembly weight summary
        """
        db = next(get_db())
        
        try:
            # Get BOM items
            bom_items = db.query(BOMItem).filter(
                BOMItem.design_id == design_id
            ).all()
            
            if not bom_items:
                raise ValueError(f"No BOM items found for design {design_id}")
            
            # Get analysis results
            analysis_result = db.query(AnalysisResult).filter(
                AnalysisResult.design_id == design_id
            ).order_by(AnalysisResult.created_at.desc()).first()
            
            if not analysis_result:
                raise ValueError(f"No analysis results found for design {design_id}")
            
            # Calculate weights for each part
            weight_results = []
            total_weight = 0.0
            confidence_scores = []
            missing_data_flags = []
            material_weights = {}
            
            for bom_item in bom_items:
                try:
                    weight_result = self._calculate_part_weight(
                        bom_item, analysis_result.analysis_data, db
                    )
                    weight_results.append(weight_result)
                    total_weight += weight_result.total_weight_kg
                    confidence_scores.append(weight_result.confidence)
                    
                    # Track material distribution
                    material = weight_result.material_name
                    if material not in material_weights:
                        material_weights[material] = 0
                    material_weights[material] += weight_result.total_weight_kg
                    
                    # Update BOM item with calculated values
                    bom_item.volume = weight_result.volume_cm3
                    bom_item.unit_weight = weight_result.unit_weight_kg
                    bom_item.weight = weight_result.total_weight_kg
                    bom_item.material = weight_result.material_name
                    
                except Exception as e:
                    logger.error(f"Weight calculation failed for part {bom_item.part_number}: {str(e)}")
                    missing_data_flags.append(f"Failed to calculate weight for {bom_item.part_number}")
            
            # Save updated BOM items
            db.commit()
            
            # Calculate summary statistics
            overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            
            heaviest_part = max(weight_results, key=lambda x: x.total_weight_kg) if weight_results else None
            lightest_part = min(weight_results, key=lambda x: x.total_weight_kg) if weight_results else None
            
            unique_materials = list(set(result.material_name for result in weight_results))
            
            return AssemblyWeightSummary(
                total_weight_kg=total_weight,
                part_count=len(weight_results),
                unique_materials=unique_materials,
                weight_breakdown=weight_results,
                heaviest_part=heaviest_part,
                lightest_part=lightest_part,
                material_distribution=material_weights,
                confidence_score=overall_confidence,
                missing_data_flags=missing_data_flags
            )
            
        except Exception as e:
            db.rollback()
            logger.error(f"Weight calculation failed for design {design_id}: {str(e)}")
            raise
        finally:
            db.close()
    
    def _calculate_part_weight(self, bom_item: BOMItem, analysis_data: Dict[str, Any], 
                             db: Session) -> WeightCalculationResult:
        """Calculate weight for a single part"""
        
        # Calculate volume
        volume_result = self.volume_calculator.calculate_volume_from_analysis(
            analysis_data, bom_item.part_number or "Unknown"
        )
        
        # Get material properties
        material = self.material_matcher.get_material_for_part(bom_item.material, db)
        
        # Calculate weight
        # Volume in cm³, density in kg/m³
        # Convert: cm³ to m³ (divide by 1,000,000), then multiply by density
        volume_m3 = volume_result.volume_cm3 / 1_000_000
        unit_weight_kg = volume_m3 * float(material.density)
        total_weight_kg = unit_weight_kg * (bom_item.quantity or 1)
        
        # Calculate overall confidence
        confidence = (volume_result.confidence + (100 if bom_item.material else 50)) / 2
        
        # Compile calculation notes
        calculation_notes = volume_result.notes + volume_result.assumptions
        if not bom_item.material:
            calculation_notes.append(f"Used default material: {material.name}")
        
        return WeightCalculationResult(
            part_number=bom_item.part_number or "Unknown",
            material_name=material.name,
            material_density=float(material.density),
            volume_cm3=volume_result.volume_cm3,
            unit_weight_kg=unit_weight_kg,
            quantity=bom_item.quantity or 1,
            total_weight_kg=total_weight_kg,
            confidence=confidence,
            calculation_notes=calculation_notes
        )
    
    def get_material_options(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get available material options for user selection
        
        Args:
            category: Optional material category filter
            
        Returns:
            List of material options
        """
        db = next(get_db())
        
        try:
            query = db.query(Material)
            
            if category:
                query = query.filter(Material.category == category)
            
            materials = query.order_by(Material.category, Material.name).all()
            
            return [
                {
                    'id': material.id,
                    'name': material.name,
                    'density': float(material.density),
                    'category': material.category,
                    'description': material.description
                }
                for material in materials
            ]
            
        finally:
            db.close()
    
    def update_part_material(self, design_id: int, part_number: str, 
                           material_name: str) -> WeightCalculationResult:
        """
        Update material for a specific part and recalculate weight
        
        Args:
            design_id: Design ID
            part_number: Part number to update
            material_name: New material name
            
        Returns:
            Updated weight calculation result
        """
        db = next(get_db())
        
        try:
            # Find the BOM item
            bom_item = db.query(BOMItem).filter(
                BOMItem.design_id == design_id,
                BOMItem.part_number == part_number
            ).first()
            
            if not bom_item:
                raise ValueError(f"Part {part_number} not found in design {design_id}")
            
            # Update material
            bom_item.material = material_name
            
            # Get analysis data
            analysis_result = db.query(AnalysisResult).filter(
                AnalysisResult.design_id == design_id
            ).order_by(AnalysisResult.created_at.desc()).first()
            
            if not analysis_result:
                raise ValueError(f"No analysis results found for design {design_id}")
            
            # Recalculate weight
            weight_result = self._calculate_part_weight(
                bom_item, analysis_result.analysis_data, db
            )
            
            # Update BOM item
            bom_item.volume = weight_result.volume_cm3
            bom_item.unit_weight = weight_result.unit_weight_kg
            bom_item.weight = weight_result.total_weight_kg
            bom_item.material = weight_result.material_name
            
            db.commit()
            
            return weight_result
            
        except Exception as e:
            db.rollback()
            logger.error(f"Material update failed for part {part_number}: {str(e)}")
            raise
        finally:
            db.close()
    
    def add_custom_material(self, name: str, density: float, category: str, 
                          description: str = "") -> Dict[str, Any]:
        """
        Add a custom material to the database
        
        Args:
            name: Material name
            density: Density in kg/m³
            category: Material category
            description: Optional description
            
        Returns:
            Created material data
        """
        db = next(get_db())
        
        try:
            # Check if material already exists
            existing = db.query(Material).filter(Material.name == name).first()
            if existing:
                raise ValueError(f"Material '{name}' already exists")
            
            # Create new material
            material = Material(
                name=name,
                density=density,
                category=category,
                description=description
            )
            
            db.add(material)
            db.commit()
            db.refresh(material)
            
            return {
                'id': material.id,
                'name': material.name,
                'density': float(material.density),
                'category': material.category,
                'description': material.description
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"Custom material creation failed: {str(e)}")
            raise
        finally:
            db.close()