"""
Fabrication progress tracking service
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc
from models.fabrication import (
    FabricationStage, FabricationTeam, TeamMember, 
    PartFabricationProgress, FabricationStageHistory
)
from models.design import Design, BOMItem
from models.user import User


class FabricationService:
    """Service for managing fabrication progress tracking"""
    
    def __init__(self, db: Session):
        self.db = db
    
    # Stage Management
    def get_fabrication_stages(self, include_custom: bool = True) -> List[Dict[str, Any]]:
        """Get all fabrication stages"""
        query = self.db.query(FabricationStage)
        if not include_custom:
            query = query.filter(FabricationStage.is_default == True)
        
        stages = query.order_by(FabricationStage.order_index).all()
        return [self._stage_to_dict(stage) for stage in stages]
    
    def create_fabrication_stage(self, name: str, description: str, order_index: int, 
                               created_by: int) -> Dict[str, Any]:
        """Create a new custom fabrication stage"""
        stage = FabricationStage(
            name=name,
            description=description,
            order_index=order_index,
            is_default=False,
            created_by=created_by
        )
        self.db.add(stage)
        self.db.commit()
        self.db.refresh(stage)
        return self._stage_to_dict(stage)
    
    def update_fabrication_stage(self, stage_id: int, name: str = None, 
                               description: str = None, order_index: int = None) -> Dict[str, Any]:
        """Update a fabrication stage"""
        stage = self.db.query(FabricationStage).filter(FabricationStage.id == stage_id).first()
        if not stage:
            raise ValueError("Stage not found")
        
        if name is not None:
            stage.name = name
        if description is not None:
            stage.description = description
        if order_index is not None:
            stage.order_index = order_index
        
        self.db.commit()
        self.db.refresh(stage)
        return self._stage_to_dict(stage)
    
    def delete_fabrication_stage(self, stage_id: int) -> bool:
        """Delete a custom fabrication stage"""
        stage = self.db.query(FabricationStage).filter(
            and_(FabricationStage.id == stage_id, FabricationStage.is_default == False)
        ).first()
        
        if not stage:
            return False
        
        self.db.delete(stage)
        self.db.commit()
        return True
    
    # Team Management
    def get_fabrication_teams(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all fabrication teams"""
        query = self.db.query(FabricationTeam)
        if active_only:
            query = query.filter(FabricationTeam.is_active == True)
        
        teams = query.all()
        return [self._team_to_dict(team) for team in teams]
    
    def create_fabrication_team(self, name: str, description: str, 
                              lead_user_id: int) -> Dict[str, Any]:
        """Create a new fabrication team"""
        team = FabricationTeam(
            name=name,
            description=description,
            lead_user_id=lead_user_id
        )
        self.db.add(team)
        self.db.commit()
        self.db.refresh(team)
        return self._team_to_dict(team)
    
    def add_team_member(self, team_id: int, user_id: int, role: str = None) -> bool:
        """Add a member to a fabrication team"""
        # Check if member already exists
        existing = self.db.query(TeamMember).filter(
            and_(TeamMember.team_id == team_id, TeamMember.user_id == user_id)
        ).first()
        
        if existing:
            return False
        
        member = TeamMember(team_id=team_id, user_id=user_id, role=role)
        self.db.add(member)
        self.db.commit()
        return True
    
    def remove_team_member(self, team_id: int, user_id: int) -> bool:
        """Remove a member from a fabrication team"""
        member = self.db.query(TeamMember).filter(
            and_(TeamMember.team_id == team_id, TeamMember.user_id == user_id)
        ).first()
        
        if not member:
            return False
        
        self.db.delete(member)
        self.db.commit()
        return True
    
    # Progress Tracking
    def get_design_progress(self, design_id: int) -> Dict[str, Any]:
        """Get fabrication progress for a design"""
        design = self.db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise ValueError("Design not found")
        
        # Get all BOM items for this design
        bom_items = self.db.query(BOMItem).filter(BOMItem.design_id == design_id).all()
        
        # Get progress for each part
        parts_progress = []
        for item in bom_items:
            progress = self.db.query(PartFabricationProgress).filter(
                PartFabricationProgress.part_id == item.id
            ).first()
            
            if not progress:
                # Create initial progress entry
                progress = self._create_initial_progress(design_id, item.id)
            
            parts_progress.append(self._progress_to_dict(progress))
        
        # Calculate overall statistics
        total_parts = len(parts_progress)
        completed_parts = len([p for p in parts_progress if p['current_stage']['name'] == 'Completed'])
        completion_percentage = (completed_parts / total_parts * 100) if total_parts > 0 else 0
        
        return {
            'design_id': design_id,
            'design_name': design.name,
            'total_parts': total_parts,
            'completed_parts': completed_parts,
            'completion_percentage': round(completion_percentage, 1),
            'parts_progress': parts_progress
        }
    
    def update_part_status(self, design_id: int, part_id: int, stage_id: int, 
                          updated_by: int, notes: str = None, 
                          estimated_completion: datetime = None) -> Dict[str, Any]:
        """Update the fabrication status of a part"""
        progress = self.db.query(PartFabricationProgress).filter(
            and_(
                PartFabricationProgress.design_id == design_id,
                PartFabricationProgress.part_id == part_id
            )
        ).first()
        
        if not progress:
            progress = self._create_initial_progress(design_id, part_id)
        
        # Record stage history
        if progress.current_stage_id != stage_id:
            self._record_stage_transition(progress, stage_id, updated_by, notes)
        
        # Update current progress
        progress.current_stage_id = stage_id
        progress.updated_at = datetime.utcnow()
        if notes:
            progress.notes = notes
        if estimated_completion:
            progress.estimated_completion = estimated_completion
        
        # Set completion time if moving to completed stage
        stage = self.db.query(FabricationStage).filter(FabricationStage.id == stage_id).first()
        if stage and stage.name == 'Completed':
            progress.actual_completion = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(progress)
        return self._progress_to_dict(progress)
    
    def assign_part(self, design_id: int, part_id: int, assigned_to: int = None, 
                   assigned_team_id: int = None) -> Dict[str, Any]:
        """Assign a part to a user or team"""
        progress = self.db.query(PartFabricationProgress).filter(
            and_(
                PartFabricationProgress.design_id == design_id,
                PartFabricationProgress.part_id == part_id
            )
        ).first()
        
        if not progress:
            progress = self._create_initial_progress(design_id, part_id)
        
        progress.assigned_to = assigned_to
        progress.assigned_team_id = assigned_team_id
        progress.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(progress)
        return self._progress_to_dict(progress)
    
    def get_timeline_data(self, design_id: int) -> Dict[str, Any]:
        """Get timeline data for Gantt chart visualization"""
        progress_items = self.db.query(PartFabricationProgress).filter(
            PartFabricationProgress.design_id == design_id
        ).all()
        
        timeline_data = []
        for progress in progress_items:
            part = progress.part
            current_stage = progress.current_stage
            
            # Get stage history for this part
            history = self.db.query(FabricationStageHistory).filter(
                FabricationStageHistory.part_progress_id == progress.id
            ).order_by(FabricationStageHistory.started_at).all()
            
            timeline_item = {
                'part_id': part.id,
                'part_number': part.part_number,
                'description': part.description,
                'current_stage': current_stage.name if current_stage else 'Not Started',
                'started_at': progress.started_at.isoformat() if progress.started_at else None,
                'estimated_completion': progress.estimated_completion.isoformat() if progress.estimated_completion else None,
                'actual_completion': progress.actual_completion.isoformat() if progress.actual_completion else None,
                'assigned_to': progress.assigned_user.username if progress.assigned_user else None,
                'assigned_team': progress.assigned_team.name if progress.assigned_team else None,
                'stage_history': [
                    {
                        'stage_name': h.stage.name,
                        'started_at': h.started_at.isoformat(),
                        'completed_at': h.completed_at.isoformat() if h.completed_at else None,
                        'duration_minutes': h.duration_minutes
                    } for h in history
                ]
            }
            timeline_data.append(timeline_item)
        
        return {
            'design_id': design_id,
            'timeline_data': timeline_data
        }
    
    def get_progress_reports(self, design_id: int = None) -> Dict[str, Any]:
        """Generate progress reports with charts and statistics"""
        query = self.db.query(PartFabricationProgress)
        if design_id:
            query = query.filter(PartFabricationProgress.design_id == design_id)
        
        all_progress = query.all()
        
        # Stage distribution
        stage_counts = {}
        for progress in all_progress:
            stage_name = progress.current_stage.name if progress.current_stage else 'Not Started'
            stage_counts[stage_name] = stage_counts.get(stage_name, 0) + 1
        
        # Overdue parts
        overdue_parts = []
        current_time = datetime.utcnow()
        for progress in all_progress:
            if (progress.estimated_completion and 
                progress.estimated_completion < current_time and 
                not progress.actual_completion):
                overdue_parts.append({
                    'part_number': progress.part.part_number,
                    'description': progress.part.description,
                    'estimated_completion': progress.estimated_completion.isoformat(),
                    'days_overdue': (current_time - progress.estimated_completion).days,
                    'assigned_to': progress.assigned_user.username if progress.assigned_user else None
                })
        
        # Completion rate over time (last 30 days)
        thirty_days_ago = current_time - timedelta(days=30)
        recent_completions = self.db.query(PartFabricationProgress).filter(
            and_(
                PartFabricationProgress.actual_completion >= thirty_days_ago,
                PartFabricationProgress.actual_completion <= current_time
            )
        ).all()
        
        completion_by_day = {}
        for progress in recent_completions:
            day = progress.actual_completion.date().isoformat()
            completion_by_day[day] = completion_by_day.get(day, 0) + 1
        
        return {
            'stage_distribution': stage_counts,
            'overdue_parts': overdue_parts,
            'completion_by_day': completion_by_day,
            'total_parts': len(all_progress),
            'completed_parts': len([p for p in all_progress if p.actual_completion]),
            'overdue_count': len(overdue_parts)
        }
    
    def get_dashboard_overview(self) -> Dict[str, Any]:
        """Get overview data for fabrication dashboard"""
        # Get all active designs with progress
        designs_with_progress = self.db.query(Design).join(PartFabricationProgress).distinct().all()
        
        dashboard_data = []
        for design in designs_with_progress:
            progress_summary = self.get_design_progress(design.id)
            dashboard_data.append({
                'design_id': design.id,
                'design_name': design.name,
                'completion_percentage': progress_summary['completion_percentage'],
                'total_parts': progress_summary['total_parts'],
                'completed_parts': progress_summary['completed_parts'],
                'created_at': design.created_at.isoformat() if design.created_at else None
            })
        
        # Overall statistics
        total_parts = self.db.query(PartFabricationProgress).count()
        completed_parts = self.db.query(PartFabricationProgress).filter(
            PartFabricationProgress.actual_completion.isnot(None)
        ).count()
        
        return {
            'projects': dashboard_data,
            'overall_stats': {
                'total_projects': len(designs_with_progress),
                'total_parts': total_parts,
                'completed_parts': completed_parts,
                'overall_completion': round((completed_parts / total_parts * 100) if total_parts > 0 else 0, 1)
            }
        }
    
    # Helper methods
    def _create_initial_progress(self, design_id: int, part_id: int) -> PartFabricationProgress:
        """Create initial progress entry for a part"""
        # Get the first stage (Not Started)
        first_stage = self.db.query(FabricationStage).filter(
            FabricationStage.name == 'Not Started'
        ).first()
        
        progress = PartFabricationProgress(
            design_id=design_id,
            part_id=part_id,
            current_stage_id=first_stage.id if first_stage else None,
            started_at=datetime.utcnow()
        )
        self.db.add(progress)
        self.db.commit()
        self.db.refresh(progress)
        return progress
    
    def _record_stage_transition(self, progress: PartFabricationProgress, 
                               new_stage_id: int, updated_by: int, notes: str = None):
        """Record a stage transition in history"""
        if progress.current_stage_id:
            # Complete the previous stage
            previous_history = self.db.query(FabricationStageHistory).filter(
                and_(
                    FabricationStageHistory.part_progress_id == progress.id,
                    FabricationStageHistory.stage_id == progress.current_stage_id,
                    FabricationStageHistory.completed_at.is_(None)
                )
            ).first()
            
            if previous_history:
                previous_history.completed_at = datetime.utcnow()
                if previous_history.started_at:
                    duration = datetime.utcnow() - previous_history.started_at
                    previous_history.duration_minutes = int(duration.total_seconds() / 60)
        
        # Create new stage history entry
        new_history = FabricationStageHistory(
            part_progress_id=progress.id,
            stage_id=new_stage_id,
            started_at=datetime.utcnow(),
            notes=notes,
            updated_by=updated_by
        )
        self.db.add(new_history)
    
    def _stage_to_dict(self, stage: FabricationStage) -> Dict[str, Any]:
        """Convert stage model to dictionary"""
        return {
            'id': stage.id,
            'name': stage.name,
            'description': stage.description,
            'order_index': stage.order_index,
            'is_default': stage.is_default,
            'created_by': stage.created_by,
            'created_at': stage.created_at.isoformat() if stage.created_at else None
        }
    
    def _team_to_dict(self, team: FabricationTeam) -> Dict[str, Any]:
        """Convert team model to dictionary"""
        return {
            'id': team.id,
            'name': team.name,
            'description': team.description,
            'lead_user_id': team.lead_user_id,
            'lead_user_name': team.lead_user.username if team.lead_user else None,
            'is_active': team.is_active,
            'member_count': len(team.members),
            'members': [
                {
                    'user_id': member.user_id,
                    'username': member.user.username,
                    'role': member.role,
                    'joined_at': member.joined_at.isoformat() if member.joined_at else None
                } for member in team.members
            ],
            'created_at': team.created_at.isoformat() if team.created_at else None
        }
    
    def _progress_to_dict(self, progress: PartFabricationProgress) -> Dict[str, Any]:
        """Convert progress model to dictionary"""
        return {
            'id': progress.id,
            'design_id': progress.design_id,
            'part_id': progress.part_id,
            'part_number': progress.part.part_number if progress.part else None,
            'part_description': progress.part.description if progress.part else None,
            'current_stage': self._stage_to_dict(progress.current_stage) if progress.current_stage else None,
            'assigned_to': progress.assigned_to,
            'assigned_user_name': progress.assigned_user.username if progress.assigned_user else None,
            'assigned_team_id': progress.assigned_team_id,
            'assigned_team_name': progress.assigned_team.name if progress.assigned_team else None,
            'started_at': progress.started_at.isoformat() if progress.started_at else None,
            'estimated_completion': progress.estimated_completion.isoformat() if progress.estimated_completion else None,
            'actual_completion': progress.actual_completion.isoformat() if progress.actual_completion else None,
            'notes': progress.notes,
            'created_at': progress.created_at.isoformat() if progress.created_at else None,
            'updated_at': progress.updated_at.isoformat() if progress.updated_at else None
        }