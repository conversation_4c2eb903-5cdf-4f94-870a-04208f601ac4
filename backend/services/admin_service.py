"""
Admin service layer for system management operations.
"""
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
from sqlalchemy.exc import IntegrityError
import psutil
import os

from models.user import User, UserSession
from models.design import Design
from models.material import Material
from models.admin import UserActivityLog, ErrorLog, SystemHealthMetric
from database.connection import Base


class AdminService:
    """Admin service for system management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        # User statistics
        total_users = self.db.query(User).count()
        active_users = self.db.query(User).filter(User.is_active == True).count()
        admin_users = self.db.query(User).filter(User.is_admin == True).count()
        
        # Design statistics
        total_designs = self.db.query(Design).count()
        
        # Designs created this month
        current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        designs_this_month = self.db.query(Design).filter(
            Design.created_at >= current_month_start
        ).count()
        
        # Analysis statistics (using design status as proxy)
        total_analyses = self.db.query(Design).filter(
            Design.status.in_(['completed', 'failed'])
        ).count()
        
        successful_analyses = self.db.query(Design).filter(
            Design.status == 'completed'
        ).count()
        
        failed_analyses = self.db.query(Design).filter(
            Design.status == 'failed'
        ).count()
        
        # Storage statistics (simplified - would need actual file system integration)
        total_storage_mb = 0.0  # Placeholder - would calculate actual storage usage
        
        # Processing time statistics (placeholder)
        avg_processing_time_seconds = 0.0  # Would calculate from analysis_results table
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "admin_users": admin_users,
            "total_designs": total_designs,
            "designs_this_month": designs_this_month,
            "total_analyses": total_analyses,
            "successful_analyses": successful_analyses,
            "failed_analyses": failed_analyses,
            "total_storage_mb": total_storage_mb,
            "avg_processing_time_seconds": avg_processing_time_seconds
        }
    
    def get_user_activity_logs(self, skip: int = 0, limit: int = 100, 
                             user_id: Optional[int] = None, 
                             action: Optional[str] = None,
                             days: int = 7) -> List[Dict[str, Any]]:
        """Get user activity logs with filtering."""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Query actual UserActivityLog records
        query = self.db.query(UserActivityLog).join(User).filter(
            UserActivityLog.timestamp >= cutoff_date
        )
        
        if user_id:
            query = query.filter(UserActivityLog.user_id == user_id)
        
        if action:
            query = query.filter(UserActivityLog.action == action)
        
        activity_logs = query.order_by(desc(UserActivityLog.timestamp)).offset(skip).limit(limit).all()
        
        result = []
        for log in activity_logs:
            result.append({
                "id": log.id,
                "user_id": log.user_id,
                "username": log.user.username,
                "action": log.action,
                "resource_type": log.resource_type,
                "resource_id": log.resource_id,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "timestamp": log.timestamp
            })
        
        return result
    
    def get_system_health_metrics(self) -> List[Dict[str, Any]]:
        """Get system health metrics."""
        metrics = []
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = "healthy" if cpu_percent < 70 else "warning" if cpu_percent < 90 else "critical"
            metrics.append({
                "metric_name": "CPU Usage",
                "value": cpu_percent,
                "unit": "%",
                "status": cpu_status,
                "last_updated": datetime.utcnow()
            })
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_status = "healthy" if memory.percent < 70 else "warning" if memory.percent < 90 else "critical"
            metrics.append({
                "metric_name": "Memory Usage",
                "value": memory.percent,
                "unit": "%",
                "status": memory_status,
                "last_updated": datetime.utcnow()
            })
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_status = "healthy" if disk_percent < 80 else "warning" if disk_percent < 95 else "critical"
            metrics.append({
                "metric_name": "Disk Usage",
                "value": disk_percent,
                "unit": "%",
                "status": disk_status,
                "last_updated": datetime.utcnow()
            })
            
        except Exception as e:
            # If psutil fails, return basic metrics
            metrics.append({
                "metric_name": "System Monitoring",
                "value": 0,
                "unit": "status",
                "status": "warning",
                "last_updated": datetime.utcnow()
            })
        
        # Database connection health
        try:
            self.db.execute("SELECT 1")
            db_status = "healthy"
        except Exception:
            db_status = "critical"
        
        metrics.append({
            "metric_name": "Database Connection",
            "value": 1 if db_status == "healthy" else 0,
            "unit": "status",
            "status": db_status,
            "last_updated": datetime.utcnow()
        })
        
        # Active user sessions
        active_sessions = self.db.query(UserSession).filter(
            UserSession.expires_at > datetime.utcnow()
        ).count()
        
        metrics.append({
            "metric_name": "Active Sessions",
            "value": active_sessions,
            "unit": "count",
            "status": "healthy",
            "last_updated": datetime.utcnow()
        })
        
        return metrics
    
    def get_error_logs(self, skip: int = 0, limit: int = 100,
                      error_type: Optional[str] = None,
                      resolved: Optional[bool] = None,
                      days: int = 7) -> List[Dict[str, Any]]:
        """Get system error logs with filtering."""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        query = self.db.query(ErrorLog).filter(
            ErrorLog.timestamp >= cutoff_date
        )
        
        if error_type:
            query = query.filter(ErrorLog.error_type == error_type)
        
        if resolved is not None:
            query = query.filter(ErrorLog.resolved == resolved)
        
        error_logs = query.order_by(desc(ErrorLog.timestamp)).offset(skip).limit(limit).all()
        
        result = []
        for error_log in error_logs:
            result.append({
                "id": error_log.id,
                "error_type": error_log.error_type,
                "error_message": error_log.error_message,
                "stack_trace": error_log.stack_trace,
                "user_id": error_log.user_id,
                "username": error_log.user.username if error_log.user else None,
                "endpoint": error_log.endpoint,
                "request_data": error_log.request_data,
                "timestamp": error_log.timestamp,
                "resolved": error_log.resolved
            })
        
        return result
    
    def resolve_error_log(self, error_id: int) -> bool:
        """Mark error log entry as resolved."""
        error_log = self.db.query(ErrorLog).filter(ErrorLog.id == error_id).first()
        if not error_log:
            return False
        
        error_log.resolved = True
        error_log.resolved_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def get_materials(self, skip: int = 0, limit: int = 100,
                     category: Optional[str] = None,
                     search: Optional[str] = None) -> List[Material]:
        """Get materials with filtering and search."""
        query = self.db.query(Material)
        
        if category:
            query = query.filter(Material.category == category)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Material.name.ilike(search_term),
                    Material.description.ilike(search_term)
                )
            )
        
        return query.order_by(Material.name).offset(skip).limit(limit).all()
    
    def create_material(self, name: str, density: float, category: str,
                       description: Optional[str] = None) -> Material:
        """Create a new material."""
        # Validate input
        if density <= 0:
            raise ValueError("Density must be positive")
        
        if len(name.strip()) == 0:
            raise ValueError("Material name cannot be empty")
        
        # Check if material already exists
        existing_material = self.db.query(Material).filter(Material.name == name).first()
        if existing_material:
            raise ValueError("Material with this name already exists")
        
        # Create new material
        material = Material(
            name=name.strip(),
            density=density,
            category=category.strip(),
            description=description.strip() if description else None
        )
        
        try:
            self.db.add(material)
            self.db.commit()
            self.db.refresh(material)
            return material
        except IntegrityError:
            self.db.rollback()
            raise ValueError("Material with this name already exists")
    
    def update_material(self, material_id: int, name: Optional[str] = None,
                       density: Optional[float] = None, category: Optional[str] = None,
                       description: Optional[str] = None) -> Optional[Material]:
        """Update an existing material."""
        material = self.db.query(Material).filter(Material.id == material_id).first()
        if not material:
            return None
        
        # Update fields if provided
        if name is not None:
            if len(name.strip()) == 0:
                raise ValueError("Material name cannot be empty")
            
            # Check if name is already taken by another material
            existing_material = self.db.query(Material).filter(
                Material.name == name.strip(),
                Material.id != material_id
            ).first()
            if existing_material:
                raise ValueError("Material with this name already exists")
            
            material.name = name.strip()
        
        if density is not None:
            if density <= 0:
                raise ValueError("Density must be positive")
            material.density = density
        
        if category is not None:
            material.category = category.strip()
        
        if description is not None:
            material.description = description.strip() if description else None
        
        try:
            self.db.commit()
            self.db.refresh(material)
            return material
        except IntegrityError:
            self.db.rollback()
            raise ValueError("Material with this name already exists")
    
    def delete_material(self, material_id: int) -> bool:
        """Delete a material."""
        material = self.db.query(Material).filter(Material.id == material_id).first()
        if not material:
            return False
        
        # Check if material is being used in any BOM items
        # This would require checking the bom_items table
        # For now, we'll allow deletion
        
        self.db.delete(material)
        self.db.commit()
        return True
    
    def cleanup_system_data(self) -> Dict[str, int]:
        """Cleanup expired sessions and old logs."""
        results = {}
        
        # Clean up expired sessions
        expired_sessions = self.db.query(UserSession).filter(
            UserSession.expires_at < datetime.utcnow()
        )
        expired_count = expired_sessions.count()
        expired_sessions.delete()
        results["expired_sessions_cleaned"] = expired_count
        
        # Clean up old designs with failed status (older than 30 days)
        old_failed_designs = self.db.query(Design).filter(
            and_(
                Design.status == 'failed',
                Design.created_at < datetime.utcnow() - timedelta(days=30)
            )
        )
        old_failed_count = old_failed_designs.count()
        old_failed_designs.delete()
        results["old_failed_designs_cleaned"] = old_failed_count
        
        self.db.commit()
        
        return results