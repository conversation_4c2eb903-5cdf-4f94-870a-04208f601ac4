"""
Drawing Analysis Orchestration Service

This service coordinates computer vision and OCR modules to perform
complete analysis of engineering drawings. It handles asynchronous
processing, progress tracking, and result aggregation.
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import cv2
import numpy as np
from pathlib import Path
import redis
from celery import Celery

from processing.computer_vision.feature_extractor import ComputerVisionEngine, CVResult
from processing.ocr.text_extractor import OCRTextExtractor, OCRResult
from database.connection import get_database as get_db
from models.design import Design, AnalysisResult
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class AnalysisStatus(Enum):
    PENDING = "pending"
    PREPROCESSING = "preprocessing"
    CV_ANALYSIS = "cv_analysis"
    OCR_ANALYSIS = "ocr_analysis"
    AGGREGATING = "aggregating"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class AnalysisProgress:
    status: AnalysisStatus
    progress_percentage: float
    current_stage: str
    estimated_time_remaining: Optional[float]
    errors: List[str]
    warnings: List[str]


@dataclass
class AnalysisRequest:
    design_id: int
    file_path: str
    user_id: int
    analysis_options: Dict[str, Any]


@dataclass
class AnalysisOutput:
    design_id: int
    cv_results: Dict[str, Any]
    ocr_results: Dict[str, Any]
    confidence_assessment: Dict[str, Any]
    quality_report: Dict[str, Any]
    processing_time: float
    errors: List[str]
    warnings: List[str]
    created_at: str


class ConfidenceAssessor:
    """Assesses confidence levels and quality of analysis results"""
    
    def __init__(self):
        pass
    
    def assess_overall_confidence(self, cv_results: CVResult, ocr_results: OCRResult) -> Dict[str, Any]:
        """
        Assess overall confidence of the analysis results
        
        Args:
            cv_results: Computer vision results
            ocr_results: OCR results
            
        Returns:
            Confidence assessment
        """
        assessment = {
            'overall_confidence': 0.0,
            'cv_confidence': cv_results.overall_confidence,
            'ocr_confidence': ocr_results.overall_confidence,
            'image_quality_score': cv_results.image_quality_score,
            'reliability_factors': {},
            'confidence_breakdown': {}
        }
        
        try:
            # Weight the confidences
            cv_weight = 0.6
            ocr_weight = 0.4
            
            # Calculate weighted overall confidence
            overall_confidence = (
                cv_results.overall_confidence * cv_weight +
                ocr_results.overall_confidence * ocr_weight
            )
            
            # Apply image quality penalty
            quality_factor = min(cv_results.image_quality_score / 100, 1.0)
            overall_confidence *= quality_factor
            
            assessment['overall_confidence'] = overall_confidence
            
            # Reliability factors
            assessment['reliability_factors'] = {
                'sufficient_features': len(cv_results.features) >= 5,
                'sufficient_text': len(ocr_results.extracted_texts) >= 3,
                'good_image_quality': cv_results.image_quality_score >= 60,
                'low_error_count': len(cv_results.errors) + len(ocr_results.errors) <= 2
            }
            
            # Detailed confidence breakdown
            assessment['confidence_breakdown'] = {
                'geometric_features': {
                    'confidence': cv_results.overall_confidence,
                    'feature_count': len(cv_results.features),
                    'section_count': len(cv_results.sections)
                },
                'text_extraction': {
                    'confidence': ocr_results.overall_confidence,
                    'text_count': len(ocr_results.extracted_texts),
                    'high_confidence_texts': len([t for t in ocr_results.extracted_texts if t.confidence > 70])
                },
                'image_quality': {
                    'score': cv_results.image_quality_score,
                    'assessment': self._get_quality_assessment(cv_results.image_quality_score)
                }
            }
            
        except Exception as e:
            logger.error(f"Confidence assessment failed: {str(e)}")
            assessment['errors'] = [f"Assessment error: {str(e)}"]
        
        return assessment
    
    def generate_quality_report(self, cv_results: CVResult, ocr_results: OCRResult, 
                              confidence_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a comprehensive quality report
        
        Args:
            cv_results: Computer vision results
            ocr_results: OCR results
            confidence_assessment: Confidence assessment
            
        Returns:
            Quality report
        """
        report = {
            'overall_quality': 'unknown',
            'quality_score': 0.0,
            'issues': [],
            'recommendations': [],
            'data_completeness': {},
            'processing_summary': {}
        }
        
        try:
            overall_confidence = confidence_assessment['overall_confidence']
            
            # Determine overall quality
            if overall_confidence >= 80:
                report['overall_quality'] = 'excellent'
            elif overall_confidence >= 60:
                report['overall_quality'] = 'good'
            elif overall_confidence >= 40:
                report['overall_quality'] = 'fair'
            else:
                report['overall_quality'] = 'poor'
            
            report['quality_score'] = overall_confidence
            
            # Identify issues and recommendations
            issues = []
            recommendations = []
            
            # CV-related issues
            if cv_results.image_quality_score < 50:
                issues.append("Low image quality affecting feature detection")
                recommendations.extend([
                    "Try uploading a higher resolution image",
                    "Ensure the drawing is properly scanned with good contrast"
                ])
            
            if len(cv_results.features) < 5:
                issues.append("Few geometric features detected")
                recommendations.append("Verify the drawing contains clear geometric elements")
            
            if len(cv_results.sections) < 2:
                issues.append("Drawing layout sections not clearly identified")
                recommendations.append("Ensure the drawing follows standard engineering drawing format")
            
            # OCR-related issues
            if ocr_results.overall_confidence < 50:
                issues.append("Low text recognition confidence")
                recommendations.extend([
                    "Ensure text in the drawing is clear and legible",
                    "Consider manual verification of extracted text"
                ])
            
            if len(ocr_results.extracted_texts) < 3:
                issues.append("Limited text content extracted")
                recommendations.append("Verify the drawing contains readable text annotations")
            
            # Data completeness assessment
            text_types = [t.text_type.value for t in ocr_results.extracted_texts]
            report['data_completeness'] = {
                'has_dimensions': 'dimension' in text_types,
                'has_part_numbers': 'part_number' in text_types,
                'has_materials': 'material' in text_types,
                'has_annotations': 'annotation' in text_types,
                'geometric_features_found': len(cv_results.features) > 0,
                'drawing_sections_identified': len(cv_results.sections) > 1
            }
            
            # Processing summary
            report['processing_summary'] = {
                'cv_processing_time': cv_results.processing_time,
                'ocr_processing_time': ocr_results.processing_time,
                'total_errors': len(cv_results.errors) + len(ocr_results.errors),
                'total_warnings': len(cv_results.warnings) + len(ocr_results.warnings)
            }
            
            report['issues'] = issues
            report['recommendations'] = recommendations
            
        except Exception as e:
            logger.error(f"Quality report generation failed: {str(e)}")
            report['issues'].append(f"Report generation error: {str(e)}")
        
        return report
    
    def _get_quality_assessment(self, quality_score: float) -> str:
        """Get textual assessment of image quality"""
        if quality_score >= 80:
            return "Excellent"
        elif quality_score >= 60:
            return "Good"
        elif quality_score >= 40:
            return "Fair"
        else:
            return "Poor"


class ErrorAggregator:
    """Aggregates and processes errors from different analysis modules"""
    
    def __init__(self):
        pass
    
    def aggregate_errors(self, cv_results: CVResult, ocr_results: OCRResult) -> Dict[str, Any]:
        """
        Aggregate errors from CV and OCR modules
        
        Args:
            cv_results: Computer vision results
            ocr_results: OCR results
            
        Returns:
            Aggregated error information
        """
        aggregated = {
            'total_errors': 0,
            'total_warnings': 0,
            'error_categories': {},
            'critical_errors': [],
            'recoverable_errors': [],
            'user_friendly_messages': []
        }
        
        try:
            all_errors = cv_results.errors + ocr_results.errors
            all_warnings = cv_results.warnings + ocr_results.warnings
            
            aggregated['total_errors'] = len(all_errors)
            aggregated['total_warnings'] = len(all_warnings)
            
            # Categorize errors
            error_categories = {
                'image_processing': [],
                'feature_extraction': [],
                'text_recognition': [],
                'system_errors': []
            }
            
            for error in all_errors:
                error_lower = error.lower()
                if any(keyword in error_lower for keyword in ['image', 'quality', 'preprocessing']):
                    error_categories['image_processing'].append(error)
                elif any(keyword in error_lower for keyword in ['feature', 'contour', 'line', 'circle']):
                    error_categories['feature_extraction'].append(error)
                elif any(keyword in error_lower for keyword in ['ocr', 'text', 'tesseract']):
                    error_categories['text_recognition'].append(error)
                else:
                    error_categories['system_errors'].append(error)
            
            aggregated['error_categories'] = error_categories
            
            # Classify error severity
            critical_errors = []
            recoverable_errors = []
            
            for error in all_errors:
                if any(keyword in error.lower() for keyword in ['failed', 'exception', 'critical']):
                    critical_errors.append(error)
                else:
                    recoverable_errors.append(error)
            
            aggregated['critical_errors'] = critical_errors
            aggregated['recoverable_errors'] = recoverable_errors
            
            # Generate user-friendly messages
            user_messages = self._generate_user_friendly_messages(error_categories, all_warnings)
            aggregated['user_friendly_messages'] = user_messages
            
        except Exception as e:
            logger.error(f"Error aggregation failed: {str(e)}")
            aggregated['system_errors'] = [f"Error aggregation failed: {str(e)}"]
        
        return aggregated
    
    def _generate_user_friendly_messages(self, error_categories: Dict[str, List[str]], 
                                       warnings: List[str]) -> List[Dict[str, str]]:
        """Generate user-friendly error messages with recovery suggestions"""
        messages = []
        
        # Image processing errors
        if error_categories['image_processing']:
            messages.append({
                'type': 'error',
                'title': 'Image Processing Issues',
                'message': 'There were problems processing your image.',
                'suggestions': [
                    'Try uploading a higher quality image',
                    'Ensure the image is clear and well-lit',
                    'Check that the file is not corrupted'
                ]
            })
        
        # Feature extraction errors
        if error_categories['feature_extraction']:
            messages.append({
                'type': 'error',
                'title': 'Feature Detection Issues',
                'message': 'Some geometric features could not be detected.',
                'suggestions': [
                    'Ensure the drawing has clear, dark lines',
                    'Check that the drawing is properly oriented',
                    'Verify the drawing follows standard conventions'
                ]
            })
        
        # Text recognition errors
        if error_categories['text_recognition']:
            messages.append({
                'type': 'error',
                'title': 'Text Recognition Issues',
                'message': 'Some text in the drawing could not be read.',
                'suggestions': [
                    'Ensure text is clear and legible',
                    'Try uploading a higher resolution image',
                    'Check that text is not too small or blurry'
                ]
            })
        
        # System errors
        if error_categories['system_errors']:
            messages.append({
                'type': 'error',
                'title': 'System Processing Error',
                'message': 'A system error occurred during processing.',
                'suggestions': [
                    'Try uploading the file again',
                    'Contact support if the problem persists'
                ]
            })
        
        # Warnings
        if warnings:
            messages.append({
                'type': 'warning',
                'title': 'Processing Warnings',
                'message': 'Some issues were detected that may affect results quality.',
                'suggestions': [
                    'Review the results carefully',
                    'Consider manual verification of uncertain data'
                ]
            })
        
        return messages


class ResultCacheManager:
    """Manages caching and retrieval of analysis results"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self.cache_ttl = 3600 * 24  # 24 hours
    
    def cache_result(self, design_id: int, analysis_output: AnalysisOutput) -> bool:
        """
        Cache analysis results
        
        Args:
            design_id: Design ID
            analysis_output: Analysis results to cache
            
        Returns:
            Success status
        """
        try:
            cache_key = f"analysis_result:{design_id}"
            result_json = json.dumps(asdict(analysis_output), default=str)
            
            self.redis_client.setex(cache_key, self.cache_ttl, result_json)
            
            # Also cache progress as completed
            progress_key = f"analysis_progress:{design_id}"
            progress = AnalysisProgress(
                status=AnalysisStatus.COMPLETED,
                progress_percentage=100.0,
                current_stage="Analysis completed",
                estimated_time_remaining=None,
                errors=[],
                warnings=[]
            )
            progress_json = json.dumps(asdict(progress), default=str)
            self.redis_client.setex(progress_key, self.cache_ttl, progress_json)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache result for design {design_id}: {str(e)}")
            return False
    
    def get_cached_result(self, design_id: int) -> Optional[AnalysisOutput]:
        """
        Retrieve cached analysis results
        
        Args:
            design_id: Design ID
            
        Returns:
            Cached results or None
        """
        try:
            cache_key = f"analysis_result:{design_id}"
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                result_dict = json.loads(cached_data)
                return AnalysisOutput(**result_dict)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve cached result for design {design_id}: {str(e)}")
            return None
    
    def update_progress(self, design_id: int, progress: AnalysisProgress) -> bool:
        """
        Update analysis progress
        
        Args:
            design_id: Design ID
            progress: Progress information
            
        Returns:
            Success status
        """
        try:
            progress_key = f"analysis_progress:{design_id}"
            progress_json = json.dumps(asdict(progress), default=str)
            self.redis_client.setex(progress_key, self.cache_ttl, progress_json)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update progress for design {design_id}: {str(e)}")
            return False
    
    def get_progress(self, design_id: int) -> Optional[AnalysisProgress]:
        """
        Get analysis progress
        
        Args:
            design_id: Design ID
            
        Returns:
            Progress information or None
        """
        try:
            progress_key = f"analysis_progress:{design_id}"
            cached_data = self.redis_client.get(progress_key)
            
            if cached_data:
                progress_dict = json.loads(cached_data)
                # Convert status string back to enum
                progress_dict['status'] = AnalysisStatus(progress_dict['status'])
                return AnalysisProgress(**progress_dict)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve progress for design {design_id}: {str(e)}")
            return None


class DrawingAnalysisService:
    """Main orchestration service for drawing analysis"""
    
    def __init__(self, redis_client: redis.Redis, celery_app: Celery):
        self.cv_engine = ComputerVisionEngine()
        self.ocr_extractor = OCRTextExtractor()
        self.confidence_assessor = ConfidenceAssessor()
        self.error_aggregator = ErrorAggregator()
        self.cache_manager = ResultCacheManager(redis_client)
        self.celery_app = celery_app
    
    async def start_analysis(self, analysis_request: AnalysisRequest) -> str:
        """
        Start asynchronous analysis of a drawing
        
        Args:
            analysis_request: Analysis request details
            
        Returns:
            Task ID for tracking progress
        """
        try:
            # Check if results are already cached
            cached_result = self.cache_manager.get_cached_result(analysis_request.design_id)
            if cached_result:
                logger.info(f"Using cached results for design {analysis_request.design_id}")
                return "cached"
            
            # Initialize progress tracking
            initial_progress = AnalysisProgress(
                status=AnalysisStatus.PENDING,
                progress_percentage=0.0,
                current_stage="Analysis queued",
                estimated_time_remaining=None,
                errors=[],
                warnings=[]
            )
            self.cache_manager.update_progress(analysis_request.design_id, initial_progress)
            
            # Queue the analysis task
            task = self.celery_app.send_task(
                'drawing_analysis.analyze_drawing',
                args=[asdict(analysis_request)],
                queue='analysis'
            )
            
            return task.id
            
        except Exception as e:
            error_msg = f"Failed to start analysis: {str(e)}"
            logger.error(error_msg)
            
            # Update progress with error
            error_progress = AnalysisProgress(
                status=AnalysisStatus.FAILED,
                progress_percentage=0.0,
                current_stage="Failed to start analysis",
                estimated_time_remaining=None,
                errors=[error_msg],
                warnings=[]
            )
            self.cache_manager.update_progress(analysis_request.design_id, error_progress)
            
            raise Exception(error_msg)
    
    async def perform_analysis(self, analysis_request: AnalysisRequest) -> AnalysisOutput:
        """
        Perform complete analysis of a drawing (called by Celery task)
        
        Args:
            analysis_request: Analysis request details
            
        Returns:
            Complete analysis results
        """
        start_time = time.time()
        design_id = analysis_request.design_id
        
        try:
            # Update progress: Starting preprocessing
            progress = AnalysisProgress(
                status=AnalysisStatus.PREPROCESSING,
                progress_percentage=10.0,
                current_stage="Preprocessing image",
                estimated_time_remaining=60.0,
                errors=[],
                warnings=[]
            )
            self.cache_manager.update_progress(design_id, progress)
            
            # Load image
            image = cv2.imread(analysis_request.file_path)
            if image is None:
                raise ValueError(f"Could not load image from {analysis_request.file_path}")
            
            # Update progress: Starting CV analysis
            progress.status = AnalysisStatus.CV_ANALYSIS
            progress.progress_percentage = 30.0
            progress.current_stage = "Analyzing geometric features"
            progress.estimated_time_remaining = 45.0
            self.cache_manager.update_progress(design_id, progress)
            
            # Perform computer vision analysis
            cv_results = self.cv_engine.analyze_drawing(image)
            
            # Update progress: Starting OCR analysis
            progress.status = AnalysisStatus.OCR_ANALYSIS
            progress.progress_percentage = 60.0
            progress.current_stage = "Extracting text information"
            progress.estimated_time_remaining = 20.0
            self.cache_manager.update_progress(design_id, progress)
            
            # Perform OCR analysis
            ocr_results = self.ocr_extractor.extract_text_from_image(image)
            
            # Update progress: Aggregating results
            progress.status = AnalysisStatus.AGGREGATING
            progress.progress_percentage = 85.0
            progress.current_stage = "Aggregating results"
            progress.estimated_time_remaining = 5.0
            self.cache_manager.update_progress(design_id, progress)
            
            # Assess confidence and generate quality report
            confidence_assessment = self.confidence_assessor.assess_overall_confidence(cv_results, ocr_results)
            quality_report = self.confidence_assessor.generate_quality_report(cv_results, ocr_results, confidence_assessment)
            
            # Aggregate errors
            error_aggregation = self.error_aggregator.aggregate_errors(cv_results, ocr_results)
            
            # Create analysis output
            analysis_output = AnalysisOutput(
                design_id=design_id,
                cv_results=self._cv_result_to_dict(cv_results),
                ocr_results=self._ocr_result_to_dict(ocr_results),
                confidence_assessment=confidence_assessment,
                quality_report=quality_report,
                processing_time=time.time() - start_time,
                errors=error_aggregation['critical_errors'] + error_aggregation['recoverable_errors'],
                warnings=cv_results.warnings + ocr_results.warnings,
                created_at=time.strftime('%Y-%m-%d %H:%M:%S')
            )
            
            # Cache the results
            self.cache_manager.cache_result(design_id, analysis_output)
            
            # Save to database
            await self._save_to_database(analysis_output)
            
            # Update progress: Completed
            progress.status = AnalysisStatus.COMPLETED
            progress.progress_percentage = 100.0
            progress.current_stage = "Analysis completed"
            progress.estimated_time_remaining = None
            self.cache_manager.update_progress(design_id, progress)
            
            return analysis_output
            
        except Exception as e:
            error_msg = f"Analysis failed for design {design_id}: {str(e)}"
            logger.error(error_msg)
            
            # Update progress with error
            error_progress = AnalysisProgress(
                status=AnalysisStatus.FAILED,
                progress_percentage=0.0,
                current_stage="Analysis failed",
                estimated_time_remaining=None,
                errors=[error_msg],
                warnings=[]
            )
            self.cache_manager.update_progress(design_id, error_progress)
            
            # Create error output
            error_output = AnalysisOutput(
                design_id=design_id,
                cv_results={},
                ocr_results={},
                confidence_assessment={'overall_confidence': 0.0},
                quality_report={'overall_quality': 'failed'},
                processing_time=time.time() - start_time,
                errors=[error_msg],
                warnings=[],
                created_at=time.strftime('%Y-%m-%d %H:%M:%S')
            )
            
            return error_output
    
    async def get_analysis_status(self, design_id: int) -> Optional[AnalysisProgress]:
        """
        Get current analysis status
        
        Args:
            design_id: Design ID
            
        Returns:
            Current progress or None
        """
        return self.cache_manager.get_progress(design_id)
    
    async def get_analysis_results(self, design_id: int) -> Optional[AnalysisOutput]:
        """
        Get analysis results
        
        Args:
            design_id: Design ID
            
        Returns:
            Analysis results or None
        """
        # Try cache first
        cached_result = self.cache_manager.get_cached_result(design_id)
        if cached_result:
            return cached_result
        
        # Try database
        return await self._load_from_database(design_id)
    
    def _cv_result_to_dict(self, cv_result: CVResult) -> Dict[str, Any]:
        """Convert CVResult to serializable dictionary"""
        return {
            'sections': [asdict(section) for section in cv_result.sections],
            'features': [asdict(feature) for feature in cv_result.features],
            'overall_confidence': cv_result.overall_confidence,
            'processing_time': cv_result.processing_time,
            'errors': cv_result.errors,
            'warnings': cv_result.warnings,
            'image_quality_score': cv_result.image_quality_score
        }
    
    def _ocr_result_to_dict(self, ocr_result: OCRResult) -> Dict[str, Any]:
        """Convert OCRResult to serializable dictionary"""
        return {
            'extracted_texts': [asdict(text) for text in ocr_result.extracted_texts],
            'overall_confidence': ocr_result.overall_confidence,
            'processing_time': ocr_result.processing_time,
            'errors': ocr_result.errors,
            'warnings': ocr_result.warnings
        }
    
    async def _save_to_database(self, analysis_output: AnalysisOutput):
        """Save analysis results to database"""
        try:
            db = next(get_db())
            
            # Create analysis result record
            analysis_result = AnalysisResult(
                design_id=analysis_output.design_id,
                analysis_data={
                    'cv_results': analysis_output.cv_results,
                    'ocr_results': analysis_output.ocr_results,
                    'confidence_assessment': analysis_output.confidence_assessment,
                    'quality_report': analysis_output.quality_report,
                    'errors': analysis_output.errors,
                    'warnings': analysis_output.warnings
                },
                confidence_score=analysis_output.confidence_assessment.get('overall_confidence', 0.0),
                processing_time=int(analysis_output.processing_time)
            )
            
            db.add(analysis_result)
            
            # Update design status
            design = db.query(Design).filter(Design.id == analysis_output.design_id).first()
            if design:
                if analysis_output.errors:
                    design.status = "analysis_failed"
                else:
                    design.status = "analysis_completed"
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to save analysis results to database: {str(e)}")
            db.rollback()
        finally:
            db.close()
    
    async def _load_from_database(self, design_id: int) -> Optional[AnalysisOutput]:
        """Load analysis results from database"""
        try:
            db = next(get_db())
            
            analysis_result = db.query(AnalysisResult).filter(
                AnalysisResult.design_id == design_id
            ).order_by(AnalysisResult.created_at.desc()).first()
            
            if analysis_result and analysis_result.analysis_data:
                data = analysis_result.analysis_data
                
                return AnalysisOutput(
                    design_id=design_id,
                    cv_results=data.get('cv_results', {}),
                    ocr_results=data.get('ocr_results', {}),
                    confidence_assessment=data.get('confidence_assessment', {}),
                    quality_report=data.get('quality_report', {}),
                    processing_time=float(analysis_result.processing_time or 0),
                    errors=data.get('errors', []),
                    warnings=data.get('warnings', []),
                    created_at=analysis_result.created_at.strftime('%Y-%m-%d %H:%M:%S')
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to load analysis results from database: {str(e)}")
            return None
        finally:
            db.close()