"""
Authentication service layer for user management operations.
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status

from models.user import User, UserSession
from auth.security import (
    get_password_hash, verify_password, validate_password, validate_username,
    create_access_token, create_refresh_token, create_reset_token, verify_token,
    generate_session_token, AuthenticationError, ValidationError, TokenData
)


class AuthService:
    """Authentication service for user management."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_user(self, username: str, email: str, password: str, is_admin: bool = False) -> User:
        """Create a new user account."""
        # Validate input
        if not validate_username(username):
            raise ValidationError(
                "Username must be 3-50 characters long, start with a letter, "
                "and contain only alphanumeric characters and underscores"
            )
        
        if not validate_password(password):
            raise ValidationError(
                "Password must be at least 8 characters long and contain "
                "uppercase, lowercase, digit, and special character"
            )
        
        # Check if user already exists
        existing_user = self.db.query(User).filter(
            (User.username == username) | (User.email == email)
        ).first()
        
        if existing_user:
            if existing_user.username == username:
                raise ValidationError("Username already registered")
            else:
                raise ValidationError("Email already registered")
        
        # Create new user
        password_hash = get_password_hash(password)
        db_user = User(
            username=username,
            email=email,
            password_hash=password_hash,
            is_admin=is_admin
        )
        
        try:
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)
            return db_user
        except IntegrityError:
            self.db.rollback()
            raise ValidationError("User with this username or email already exists")
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username and password."""
        user = self.db.query(User).filter(User.username == username).first()
        if not user:
            return None
        
        if not user.is_active:
            return None
        
        if not verify_password(password, user.password_hash):
            return None
        
        # Update last login
        user.last_login = datetime.utcnow()
        self.db.commit()
        
        return user
    
    def create_user_session(self, user: User) -> dict:
        """Create user session with JWT tokens."""
        # Create token data
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "is_admin": user.is_admin
        }
        
        # Generate tokens
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        session_token = generate_session_token()
        
        # Store session in database
        expires_at = datetime.utcnow() + timedelta(days=7)  # Refresh token expiry
        db_session = UserSession(
            user_id=user.id,
            session_token=session_token,
            expires_at=expires_at
        )
        
        self.db.add(db_session)
        self.db.commit()
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "session_token": session_token
        }
    
    def refresh_access_token(self, refresh_token: str) -> dict:
        """Refresh access token using refresh token."""
        token_data = verify_token(refresh_token, "refresh")
        if not token_data:
            raise AuthenticationError("Invalid refresh token")
        
        # Verify user exists and is active
        user = self.db.query(User).filter(User.id == token_data.user_id).first()
        if not user or not user.is_active:
            raise AuthenticationError("User not found or inactive")
        
        # Create new access token
        new_token_data = {
            "sub": user.username,
            "user_id": user.id,
            "is_admin": user.is_admin
        }
        
        access_token = create_access_token(new_token_data)
        
        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
    
    def logout_user(self, session_token: str) -> bool:
        """Logout user by invalidating session."""
        session = self.db.query(UserSession).filter(
            UserSession.session_token == session_token
        ).first()
        
        if session:
            self.db.delete(session)
            self.db.commit()
            return True
        
        return False
    
    def get_user_by_token(self, token: str) -> Optional[User]:
        """Get user by access token."""
        token_data = verify_token(token, "access")
        if not token_data:
            return None
        
        user = self.db.query(User).filter(User.id == token_data.user_id).first()
        if not user or not user.is_active:
            return None
        
        return user
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        return self.db.query(User).filter(User.username == username).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email).first()
    
    def update_user_profile(self, user_id: int, email: Optional[str] = None, 
                          current_password: Optional[str] = None, 
                          new_password: Optional[str] = None) -> User:
        """Update user profile information."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValidationError("User not found")
        
        # Update email if provided
        if email and email != user.email:
            # Check if email is already taken
            existing_user = self.db.query(User).filter(
                User.email == email, User.id != user_id
            ).first()
            if existing_user:
                raise ValidationError("Email already registered")
            user.email = email
        
        # Update password if provided
        if new_password:
            if not current_password:
                raise ValidationError("Current password required to change password")
            
            if not verify_password(current_password, user.password_hash):
                raise ValidationError("Current password is incorrect")
            
            if not validate_password(new_password):
                raise ValidationError(
                    "New password must be at least 8 characters long and contain "
                    "uppercase, lowercase, digit, and special character"
                )
            
            user.password_hash = get_password_hash(new_password)
        
        try:
            self.db.commit()
            self.db.refresh(user)
            return user
        except IntegrityError:
            self.db.rollback()
            raise ValidationError("Email already registered")
    
    def create_password_reset_token(self, email: str) -> str:
        """Create password reset token for user."""
        user = self.get_user_by_email(email)
        if not user:
            # Don't reveal if email exists or not
            raise ValidationError("If the email exists, a reset link will be sent")
        
        if not user.is_active:
            raise ValidationError("Account is inactive")
        
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "email": user.email
        }
        
        return create_reset_token(token_data)
    
    def reset_password(self, token: str, new_password: str) -> bool:
        """Reset user password using reset token."""
        token_data = verify_token(token, "reset")
        if not token_data:
            raise ValidationError("Invalid or expired reset token")
        
        if not validate_password(new_password):
            raise ValidationError(
                "Password must be at least 8 characters long and contain "
                "uppercase, lowercase, digit, and special character"
            )
        
        user = self.db.query(User).filter(User.id == token_data.user_id).first()
        if not user or not user.is_active:
            raise ValidationError("User not found or inactive")
        
        user.password_hash = get_password_hash(new_password)
        self.db.commit()
        
        # Invalidate all user sessions
        self.db.query(UserSession).filter(UserSession.user_id == user.id).delete()
        self.db.commit()
        
        return True
    
    def get_all_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get all users (admin only)."""
        return self.db.query(User).offset(skip).limit(limit).all()
    
    def update_user_status(self, user_id: int, is_active: bool) -> User:
        """Update user active status (admin only)."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValidationError("User not found")
        
        user.is_active = is_active
        self.db.commit()
        self.db.refresh(user)
        
        # If deactivating user, invalidate all sessions
        if not is_active:
            self.db.query(UserSession).filter(UserSession.user_id == user_id).delete()
            self.db.commit()
        
        return user
    
    def update_user_admin_status(self, user_id: int, is_admin: bool) -> User:
        """Update user admin status (admin only)."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValidationError("User not found")
        
        user.is_admin = is_admin
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def delete_user(self, user_id: int) -> bool:
        """Delete user account (admin only)."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValidationError("User not found")
        
        # Delete user sessions first
        self.db.query(UserSession).filter(UserSession.user_id == user_id).delete()
        
        # Delete user
        self.db.delete(user)
        self.db.commit()
        
        return True
    
    def cleanup_expired_sessions(self):
        """Clean up expired user sessions."""
        expired_sessions = self.db.query(UserSession).filter(
            UserSession.expires_at < datetime.utcnow()
        )
        expired_sessions.delete()
        self.db.commit()