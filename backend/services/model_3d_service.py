"""
3D Model Generation Service using OpenSCAD.

This service handles the generation of 3D models from 2D geometric analysis data.
It creates OpenSCAD scripts, executes them to generate STL files, and manages
the resulting 3D model files.
"""
import os
import json
import subprocess
import tempfile
import time
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging

from sqlalchemy.orm import Session
from models.design import Model3D, Design, AnalysisResult
from database.connection import get_database

logger = logging.getLogger(__name__)


class OpenSCADGenerator:
    """Handles OpenSCAD script generation and execution."""
    
    def __init__(self):
        self.openscad_executable = self._find_openscad_executable()
    
    def _find_openscad_executable(self) -> str:
        """Find OpenSCAD executable on the system."""
        possible_paths = [
            "openscad",  # In PATH
            "/usr/bin/openscad",  # Linux
            "/usr/local/bin/openscad",  # macOS Homebrew
            "/Applications/OpenSCAD.app/Contents/MacOS/OpenSCAD",  # macOS App
            "C:\\Program Files\\OpenSCAD\\openscad.exe",  # Windows
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    logger.info(f"Found OpenSCAD at: {path}")
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        raise RuntimeError("OpenSCAD executable not found. Please install OpenSCAD.")
    
    def generate_part_script(self, part_data: Dict[str, Any]) -> str:
        """
        Generate OpenSCAD script for a single part.
        
        Args:
            part_data: Dictionary containing part geometry and properties
            
        Returns:
            OpenSCAD script as string
        """
        script_lines = []
        script_lines.append("// Generated OpenSCAD script for part")
        script_lines.append(f"// Part: {part_data.get('part_number', 'Unknown')}")
        script_lines.append("")
        
        # Extract geometric features
        features = part_data.get('features', [])
        dimensions = part_data.get('dimensions', {})
        
        # Generate main body using linear extrusion
        main_profile = self._extract_main_profile(features)
        if main_profile:
            thickness = dimensions.get('thickness', 10.0)  # Default 10mm
            script_lines.append("// Main body")
            script_lines.append("difference() {")
            script_lines.append(f"    linear_extrude(height={thickness}) {{")
            script_lines.append(f"        {self._generate_2d_profile(main_profile)}")
            script_lines.append("    }")
            
            # Add holes and cutouts
            holes = self._extract_holes(features)
            for hole in holes:
                script_lines.append(f"    {self._generate_hole_script(hole, thickness)}")
            
            script_lines.append("}")
        else:
            # Fallback to simple box if no profile detected
            width = dimensions.get('width', 50.0)
            height = dimensions.get('height', 50.0)
            thickness = dimensions.get('thickness', 10.0)
            script_lines.append(f"cube([{width}, {height}, {thickness}]);")
        
        return "\n".join(script_lines)
    
    def _extract_main_profile(self, features: List[Dict]) -> Optional[Dict]:
        """Extract the main 2D profile from detected features."""
        # Look for the largest closed contour
        profiles = [f for f in features if f.get('type') == 'contour' and f.get('closed', False)]
        if profiles:
            # Return the profile with the largest area
            return max(profiles, key=lambda p: p.get('area', 0))
        return None
    
    def _extract_holes(self, features: List[Dict]) -> List[Dict]:
        """Extract hole features from the analysis data."""
        return [f for f in features if f.get('type') == 'circle' and f.get('is_hole', False)]
    
    def _generate_2d_profile(self, profile: Dict) -> str:
        """Generate 2D profile code from contour data."""
        points = profile.get('points', [])
        if not points:
            return "square([50, 50]);"  # Fallback
        
        # Convert points to OpenSCAD polygon format
        point_strings = [f"[{p[0]}, {p[1]}]" for p in points]
        return f"polygon([{', '.join(point_strings)}]);"
    
    def _generate_hole_script(self, hole: Dict, thickness: float) -> str:
        """Generate OpenSCAD script for a hole."""
        center = hole.get('center', [0, 0])
        radius = hole.get('radius', 5.0)
        
        return f"    translate([{center[0]}, {center[1]}, -0.1]) cylinder(h={thickness + 0.2}, r={radius});"
    
    def generate_assembly_script(self, parts_data: List[Dict[str, Any]]) -> str:
        """
        Generate master OpenSCAD script for assembly.
        
        Args:
            parts_data: List of part data dictionaries
            
        Returns:
            Master assembly OpenSCAD script
        """
        script_lines = []
        script_lines.append("// Generated OpenSCAD assembly script")
        script_lines.append("// Assembly of multiple parts")
        script_lines.append("")
        
        for i, part_data in enumerate(parts_data):
            part_name = part_data.get('part_number', f'part_{i}')
            script_lines.append(f"// Part: {part_name}")
            
            # Calculate position based on part layout
            position = self._calculate_part_position(part_data, i, len(parts_data))
            
            script_lines.append(f"translate([{position[0]}, {position[1]}, {position[2]}]) {{")
            
            # Include the part script inline
            part_script = self.generate_part_script(part_data)
            # Remove the header comments and indent the script
            part_lines = part_script.split('\n')[3:]  # Skip header comments
            for line in part_lines:
                if line.strip():
                    script_lines.append(f"    {line}")
            
            script_lines.append("}")
            script_lines.append("")
        
        return "\n".join(script_lines)
    
    def _calculate_part_position(self, part_data: Dict, index: int, total_parts: int) -> Tuple[float, float, float]:
        """Calculate position for part in assembly."""
        # Simple grid layout for now
        grid_size = int(total_parts ** 0.5) + 1
        spacing = 100.0  # 100mm spacing
        
        row = index // grid_size
        col = index % grid_size
        
        x = col * spacing
        y = row * spacing
        z = 0.0
        
        return (x, y, z)
    
    def execute_openscad(self, script_content: str, output_path: str) -> bool:
        """
        Execute OpenSCAD script to generate STL file.
        
        Args:
            script_content: OpenSCAD script content
            output_path: Path where STL file should be saved
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create temporary script file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.scad', delete=False) as temp_file:
                temp_file.write(script_content)
                temp_script_path = temp_file.name
            
            try:
                # Execute OpenSCAD
                cmd = [
                    self.openscad_executable,
                    "-o", output_path,
                    temp_script_path
                ]
                
                logger.info(f"Executing OpenSCAD: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )
                
                if result.returncode == 0:
                    logger.info(f"Successfully generated STL file: {output_path}")
                    return True
                else:
                    logger.error(f"OpenSCAD execution failed: {result.stderr}")
                    return False
                    
            finally:
                # Clean up temporary script file
                os.unlink(temp_script_path)
                
        except subprocess.TimeoutExpired:
            logger.error("OpenSCAD execution timed out")
            return False
        except Exception as e:
            logger.error(f"Error executing OpenSCAD: {str(e)}")
            return False


class Model3DService:
    """Service for managing 3D model generation and storage."""
    
    def __init__(self):
        try:
            self.openscad_generator = OpenSCADGenerator()
        except RuntimeError:
            # OpenSCAD not found, create a mock generator for testing
            self.openscad_generator = OpenSCADGenerator.__new__(OpenSCADGenerator)
            self.openscad_generator.openscad_executable = "openscad"
        self.models_directory = Path("storage/models")
        self.models_directory.mkdir(parents=True, exist_ok=True)
    
    def generate_3d_model(self, design_id: int, db: Session) -> Optional[Model3D]:
        """
        Generate 3D model from design analysis data.
        
        Args:
            design_id: ID of the design to generate model for
            db: Database session
            
        Returns:
            Model3D instance if successful, None otherwise
        """
        start_time = time.time()
        
        try:
            # Get design and analysis data
            design = db.query(Design).filter(Design.id == design_id).first()
            if not design:
                logger.error(f"Design {design_id} not found")
                return None
            
            analysis_result = db.query(AnalysisResult).filter(
                AnalysisResult.design_id == design_id
            ).first()
            
            if not analysis_result or not analysis_result.analysis_data:
                logger.error(f"No analysis data found for design {design_id}")
                return None
            
            analysis_data = analysis_result.analysis_data
            
            # Extract parts data from analysis
            parts_data = self._extract_parts_from_analysis(analysis_data)
            if not parts_data:
                logger.error(f"No parts data found in analysis for design {design_id}")
                return None
            
            # Generate OpenSCAD script
            if len(parts_data) == 1:
                # Single part
                openscad_script = self.openscad_generator.generate_part_script(parts_data[0])
            else:
                # Assembly
                openscad_script = self.openscad_generator.generate_assembly_script(parts_data)
            
            # Generate STL file
            model_filename = f"model_{design_id}_{int(time.time())}.stl"
            model_path = self.models_directory / model_filename
            
            success = self.openscad_generator.execute_openscad(
                openscad_script, str(model_path)
            )
            
            if not success:
                logger.error(f"Failed to generate STL file for design {design_id}")
                return None
            
            # Calculate file size
            file_size = model_path.stat().st_size if model_path.exists() else 0
            
            # Save to database
            model_3d = Model3D(
                design_id=design_id,
                model_file_path=str(model_path),
                openscad_script=openscad_script,
                generation_time=int(time.time() - start_time),
                file_size=file_size
            )
            
            db.add(model_3d)
            db.commit()
            db.refresh(model_3d)
            
            logger.info(f"Successfully generated 3D model for design {design_id}")
            return model_3d
            
        except Exception as e:
            logger.error(f"Error generating 3D model for design {design_id}: {str(e)}")
            db.rollback()
            return None
    
    def _extract_parts_from_analysis(self, analysis_data: Dict) -> List[Dict[str, Any]]:
        """Extract parts data from analysis results."""
        parts = []
        
        # Extract from BOM data if available
        bom_data = analysis_data.get('bom', {})
        if bom_data and 'items' in bom_data:
            for item in bom_data['items']:
                part_data = {
                    'part_number': item.get('part_number', ''),
                    'description': item.get('description', ''),
                    'material': item.get('material', ''),
                    'features': [],
                    'dimensions': {}
                }
                parts.append(part_data)
        
        # Extract geometric features from computer vision data
        cv_data = analysis_data.get('computer_vision', {})
        if cv_data and 'features' in cv_data:
            features = cv_data['features']
            
            # If no BOM parts, create parts from geometric features
            if not parts:
                part_data = {
                    'part_number': 'main_part',
                    'description': 'Main part from drawing',
                    'material': 'Steel',
                    'features': features,
                    'dimensions': cv_data.get('dimensions', {})
                }
                parts.append(part_data)
            else:
                # Assign features to existing parts (simplified approach)
                if parts:
                    parts[0]['features'] = features
                    parts[0]['dimensions'] = cv_data.get('dimensions', {})
        
        return parts
    
    def get_model_by_design_id(self, design_id: int, db: Session) -> Optional[Model3D]:
        """Get 3D model for a design."""
        return db.query(Model3D).filter(Model3D.design_id == design_id).first()
    
    def get_model_file_path(self, model_id: int, db: Session) -> Optional[str]:
        """Get file path for a 3D model."""
        model = db.query(Model3D).filter(Model3D.id == model_id).first()
        if model and model.model_file_path:
            model_path = Path(model.model_file_path)
            if model_path.exists():
                return str(model_path)
        return None
    
    def delete_model(self, model_id: int, db: Session) -> bool:
        """Delete a 3D model and its file."""
        try:
            model = db.query(Model3D).filter(Model3D.id == model_id).first()
            if not model:
                return False
            
            # Delete file if it exists
            if model.model_file_path:
                model_path = Path(model.model_file_path)
                if model_path.exists():
                    model_path.unlink()
            
            # Delete database record
            db.delete(model)
            db.commit()
            
            logger.info(f"Successfully deleted 3D model {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting 3D model {model_id}: {str(e)}")
            db.rollback()
            return False
    
    def validate_model_file(self, file_path: str) -> Dict[str, Any]:
        """
        Validate generated STL file.
        
        Args:
            file_path: Path to STL file
            
        Returns:
            Validation results dictionary
        """
        validation_result = {
            'is_valid': False,
            'file_exists': False,
            'file_size': 0,
            'errors': []
        }
        
        try:
            model_path = Path(file_path)
            
            # Check if file exists
            if not model_path.exists():
                validation_result['errors'].append("STL file does not exist")
                return validation_result
            
            validation_result['file_exists'] = True
            validation_result['file_size'] = model_path.stat().st_size
            
            # Check if file is not empty
            if validation_result['file_size'] == 0:
                validation_result['errors'].append("STL file is empty")
                return validation_result
            
            # Basic STL format validation
            with open(model_path, 'rb') as f:
                header = f.read(80)
                if len(header) < 80:
                    validation_result['errors'].append("Invalid STL file format")
                    return validation_result
            
            validation_result['is_valid'] = True
            
        except Exception as e:
            validation_result['errors'].append(f"Validation error: {str(e)}")
        
        return validation_result


# Global service instance
model_3d_service = Model3DService()