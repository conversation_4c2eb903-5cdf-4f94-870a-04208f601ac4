"""
Bill of Materials (BOM) Generation Service

This service extracts BOM data from analysis results, performs part number
cross-referencing, validates completeness, and provides export functionality.
"""

import csv
import io
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session

from models.design import Design, AnalysisResult, BOMItem
from models.material import Material
from database.connection import get_database as get_db

logger = logging.getLogger(__name__)


class BOMValidationStatus(Enum):
    COMPLETE = "complete"
    INCOMPLETE = "incomplete"
    MISSING_CRITICAL = "missing_critical"
    NEEDS_REVIEW = "needs_review"


@dataclass
class BOMValidationIssue:
    severity: str  # 'error', 'warning', 'info'
    field: str
    message: str
    part_number: Optional[str] = None
    suggestions: List[str] = None


@dataclass
class BOMValidationResult:
    status: BOMValidationStatus
    completeness_score: float
    issues: List[BOMValidationIssue]
    missing_fields: List[str]
    flagged_parts: List[str]


@dataclass
class BOMExportData:
    headers: List[str]
    rows: List[List[str]]
    metadata: Dict[str, Any]


class BOMExtractor:
    """Extracts BOM data from analysis results"""
    
    def __init__(self):
        self.part_number_patterns = [
            r'[A-Z]{1,3}-?\d{3,6}',  # Standard part numbers like A-123456, AB-1234
            r'P/N:?\s*([A-Z0-9-]+)',  # Part number with P/N prefix
            r'PART\s*#:?\s*([A-Z0-9-]+)',  # Part number with PART # prefix
            r'\b\d{4,6}-\d{2,4}\b',  # Numeric part numbers like 1234-56
        ]
        
        self.material_patterns = [
            r'(?i)(steel|aluminum|brass|copper|titanium|plastic|abs|pla)',
            r'(?i)(carbon\s+steel|stainless\s+steel|alloy\s+steel)',
            r'(?i)(aluminum\s+\d{4}|al\s+\d{4})',  # Aluminum grades
            r'(?i)(grade\s+\d+|type\s+\d+)',
        ]
        
        self.quantity_patterns = [
            r'QTY:?\s*(\d+)',
            r'QUANTITY:?\s*(\d+)',
            r'(\d+)\s*(?:PCS?|PIECES?|EA|EACH)',
            r'(\d+)X',  # Like 2X for quantity 2
        ]
    
    def extract_bom_from_analysis(self, analysis_result: AnalysisResult) -> List[Dict[str, Any]]:
        """
        Extract BOM data from analysis results
        
        Args:
            analysis_result: Analysis result containing CV and OCR data
            
        Returns:
            List of BOM items as dictionaries
        """
        try:
            analysis_data = analysis_result.analysis_data
            cv_results = analysis_data.get('cv_results', {})
            ocr_results = analysis_data.get('ocr_results', {})
            
            # Extract parts from different sources
            parts_from_list = self._extract_from_parts_list(ocr_results)
            parts_from_views = self._extract_from_drawing_views(cv_results, ocr_results)
            
            # Cross-reference and merge parts
            merged_parts = self._cross_reference_parts(parts_from_list, parts_from_views)
            
            # Enhance with additional data
            enhanced_parts = self._enhance_part_data(merged_parts, analysis_data)
            
            return enhanced_parts
            
        except Exception as e:
            logger.error(f"BOM extraction failed: {str(e)}")
            return []
    
    def _extract_from_parts_list(self, ocr_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract parts from the parts list section"""
        parts = []
        
        try:
            extracted_texts = ocr_results.get('extracted_texts', [])
            
            # Look for parts list section
            parts_list_texts = []
            in_parts_section = False
            
            for text_item in extracted_texts:
                text_content = text_item.get('text', '').strip()
                text_type = text_item.get('text_type', '')
                
                # Identify parts list section
                if any(keyword in text_content.upper() for keyword in ['PARTS LIST', 'BILL OF MATERIALS', 'BOM', 'ITEM LIST']):
                    in_parts_section = True
                    continue
                
                if in_parts_section and text_type in ['part_number', 'annotation', 'general']:
                    parts_list_texts.append(text_item)
            
            # Parse parts from the parts list texts
            current_part = {}
            
            for text_item in parts_list_texts:
                text_content = text_item.get('text', '').strip()
                
                # Try to extract part number
                part_number = self._extract_part_number(text_content)
                if part_number:
                    if current_part:
                        parts.append(current_part)
                    current_part = {
                        'part_number': part_number,
                        'description': '',
                        'quantity': 1,
                        'material': '',
                        'source': 'parts_list',
                        'confidence': text_item.get('confidence', 0)
                    }
                
                # Try to extract quantity
                quantity = self._extract_quantity(text_content)
                if quantity and current_part:
                    current_part['quantity'] = quantity
                
                # Try to extract material
                material = self._extract_material(text_content)
                if material and current_part:
                    current_part['material'] = material
                
                # Use as description if no specific field matched
                if not part_number and not quantity and not material and current_part:
                    if len(text_content) > 5:  # Avoid short meaningless text
                        current_part['description'] = text_content
            
            # Add the last part
            if current_part:
                parts.append(current_part)
                
        except Exception as e:
            logger.error(f"Parts list extraction failed: {str(e)}")
        
        return parts
    
    def _extract_from_drawing_views(self, cv_results: Dict[str, Any], ocr_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract parts from drawing views and annotations"""
        parts = []
        
        try:
            # Get features and sections from CV results
            features = cv_results.get('features', [])
            sections = cv_results.get('sections', [])
            extracted_texts = ocr_results.get('extracted_texts', [])
            
            # Look for part callouts and annotations in drawing views
            for text_item in extracted_texts:
                text_content = text_item.get('text', '').strip()
                text_type = text_item.get('text_type', '')
                
                # Skip if this looks like it's from parts list
                if any(keyword in text_content.upper() for keyword in ['PARTS LIST', 'BILL OF MATERIALS']):
                    continue
                
                # Look for part numbers in annotations
                if text_type in ['part_number', 'annotation']:
                    part_number = self._extract_part_number(text_content)
                    if part_number:
                        parts.append({
                            'part_number': part_number,
                            'description': text_content,
                            'quantity': 1,
                            'material': '',
                            'source': 'drawing_view',
                            'confidence': text_item.get('confidence', 0)
                        })
            
            # Infer parts from geometric features
            geometric_parts = self._infer_parts_from_geometry(features)
            parts.extend(geometric_parts)
            
        except Exception as e:
            logger.error(f"Drawing view extraction failed: {str(e)}")
        
        return parts
    
    def _cross_reference_parts(self, parts_list_parts: List[Dict], view_parts: List[Dict]) -> List[Dict[str, Any]]:
        """Cross-reference parts between parts list and drawing views"""
        merged_parts = []
        
        try:
            # Create a map of parts list parts by part number
            parts_list_map = {}
            for part in parts_list_parts:
                part_num = part.get('part_number', '').upper()
                if part_num:
                    parts_list_map[part_num] = part
            
            # Match view parts with parts list
            matched_view_parts = set()
            
            for part_num, list_part in parts_list_map.items():
                # Look for matching part in views
                matching_view_part = None
                for i, view_part in enumerate(view_parts):
                    view_part_num = view_part.get('part_number', '').upper()
                    if view_part_num == part_num:
                        matching_view_part = view_part
                        matched_view_parts.add(i)
                        break
                
                # Merge data from both sources
                merged_part = list_part.copy()
                if matching_view_part:
                    # Use the more detailed description
                    if len(matching_view_part.get('description', '')) > len(merged_part.get('description', '')):
                        merged_part['description'] = matching_view_part['description']
                    
                    # Average confidence scores
                    merged_part['confidence'] = (
                        merged_part.get('confidence', 0) + matching_view_part.get('confidence', 0)
                    ) / 2
                    
                    merged_part['cross_referenced'] = True
                else:
                    merged_part['cross_referenced'] = False
                
                merged_parts.append(merged_part)
            
            # Add unmatched view parts
            for i, view_part in enumerate(view_parts):
                if i not in matched_view_parts:
                    view_part['cross_referenced'] = False
                    merged_parts.append(view_part)
            
        except Exception as e:
            logger.error(f"Cross-referencing failed: {str(e)}")
            # Return all parts without cross-referencing
            merged_parts = parts_list_parts + view_parts
        
        return merged_parts
    
    def _enhance_part_data(self, parts: List[Dict], analysis_data: Dict) -> List[Dict[str, Any]]:
        """Enhance part data with additional information"""
        enhanced_parts = []
        
        for part in parts:
            enhanced_part = part.copy()
            
            # Clean up part number
            part_number = enhanced_part.get('part_number', '').strip()
            enhanced_part['part_number'] = part_number
            
            # Clean up description
            description = enhanced_part.get('description', '').strip()
            enhanced_part['description'] = description
            
            # Ensure quantity is at least 1
            quantity = enhanced_part.get('quantity', 1)
            enhanced_part['quantity'] = max(1, quantity)
            
            # Standardize material name
            material = enhanced_part.get('material', '').strip()
            enhanced_part['material'] = self._standardize_material_name(material)
            
            # Add metadata
            enhanced_part['extraction_method'] = enhanced_part.get('source', 'unknown')
            enhanced_part['needs_review'] = enhanced_part.get('confidence', 0) < 70
            
            enhanced_parts.append(enhanced_part)
        
        return enhanced_parts
    
    def _extract_part_number(self, text: str) -> Optional[str]:
        """Extract part number from text using patterns"""
        for pattern in self.part_number_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if match.groups():
                    return match.group(1).strip()
                else:
                    return match.group(0).strip()
        return None
    
    def _extract_quantity(self, text: str) -> Optional[int]:
        """Extract quantity from text using patterns"""
        for pattern in self.quantity_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return int(match.group(1))
                except (ValueError, IndexError):
                    continue
        return None
    
    def _extract_material(self, text: str) -> Optional[str]:
        """Extract material from text using patterns"""
        for pattern in self.material_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0).strip()
        return None
    
    def _infer_parts_from_geometry(self, features: List[Dict]) -> List[Dict[str, Any]]:
        """Infer parts from geometric features"""
        parts = []
        
        try:
            # Group features that might represent the same part
            feature_groups = self._group_related_features(features)
            
            for i, group in enumerate(feature_groups):
                if len(group) >= 2:  # Only consider groups with multiple features
                    parts.append({
                        'part_number': f'INFERRED-{i+1:03d}',
                        'description': f'Inferred part from {len(group)} geometric features',
                        'quantity': 1,
                        'material': '',
                        'source': 'geometry_inference',
                        'confidence': 30,  # Low confidence for inferred parts
                        'feature_count': len(group)
                    })
        
        except Exception as e:
            logger.error(f"Geometry inference failed: {str(e)}")
        
        return parts
    
    def _group_related_features(self, features: List[Dict]) -> List[List[Dict]]:
        """Group related geometric features"""
        # Simple grouping by proximity - could be enhanced
        groups = []
        used_features = set()
        
        for i, feature in enumerate(features):
            if i in used_features:
                continue
            
            group = [feature]
            used_features.add(i)
            
            # Find nearby features
            for j, other_feature in enumerate(features):
                if j in used_features or i == j:
                    continue
                
                # Simple distance check (would need actual coordinates)
                # For now, just group by feature type
                if (feature.get('feature_type') == other_feature.get('feature_type') and
                    len(group) < 5):  # Limit group size
                    group.append(other_feature)
                    used_features.add(j)
            
            groups.append(group)
        
        return groups
    
    def _standardize_material_name(self, material: str) -> str:
        """Standardize material names"""
        if not material:
            return ''
        
        material_lower = material.lower()
        
        # Common material mappings
        material_mappings = {
            'steel': 'Carbon Steel A36',
            'carbon steel': 'Carbon Steel A36',
            'stainless': 'Stainless Steel 304',
            'stainless steel': 'Stainless Steel 304',
            'aluminum': 'Aluminum 6061',
            'al': 'Aluminum 6061',
            'brass': 'Brass 360',
            'copper': 'Copper 110',
            'plastic': 'ABS Plastic',
            'abs': 'ABS Plastic',
        }
        
        for key, standard_name in material_mappings.items():
            if key in material_lower:
                return standard_name
        
        return material


class BOMValidator:
    """Validates BOM completeness and flags issues"""
    
    def __init__(self):
        self.required_fields = ['part_number', 'description', 'quantity']
        self.recommended_fields = ['material']
        self.critical_patterns = [
            r'(?i)(main|primary|critical|structural)',
            r'(?i)(frame|housing|base|support)',
        ]
    
    def validate_bom(self, bom_items: List[Dict[str, Any]]) -> BOMValidationResult:
        """
        Validate BOM completeness and identify issues
        
        Args:
            bom_items: List of BOM items to validate
            
        Returns:
            Validation result with issues and recommendations
        """
        issues = []
        missing_fields = set()
        flagged_parts = []
        
        if not bom_items:
            return BOMValidationResult(
                status=BOMValidationStatus.MISSING_CRITICAL,
                completeness_score=0.0,
                issues=[BOMValidationIssue(
                    severity='error',
                    field='bom',
                    message='No BOM items found',
                    suggestions=['Check if the drawing contains a parts list', 'Verify drawing quality and text clarity']
                )],
                missing_fields=['all'],
                flagged_parts=[]
            )
        
        total_score = 0
        max_score = 0
        
        for item in bom_items:
            item_score = 0
            item_max_score = 0
            part_number = item.get('part_number', 'Unknown')
            
            # Check required fields
            for field in self.required_fields:
                item_max_score += 3  # Required fields worth 3 points each
                value = item.get(field)
                if value and str(value).strip() and str(value).strip() != '':
                    item_score += 3
                else:
                    missing_fields.add(field)
                    issues.append(BOMValidationIssue(
                        severity='error',
                        field=field,
                        message=f'Missing required field: {field}',
                        part_number=part_number,
                        suggestions=[f'Verify {field} is clearly visible in the drawing']
                    ))
            
            # Check recommended fields
            for field in self.recommended_fields:
                item_max_score += 1  # Recommended fields worth 1 point each
                value = item.get(field)
                if value and str(value).strip():
                    item_score += 1
                else:
                    issues.append(BOMValidationIssue(
                        severity='warning',
                        field=field,
                        message=f'Missing recommended field: {field}',
                        part_number=part_number,
                        suggestions=[f'Consider adding {field} information for better BOM completeness']
                    ))
            
            # Check data quality
            confidence = item.get('confidence', 0)
            if confidence < 50:
                flagged_parts.append(part_number)
                issues.append(BOMValidationIssue(
                    severity='warning',
                    field='confidence',
                    message=f'Low confidence extraction ({confidence}%)',
                    part_number=part_number,
                    suggestions=['Review and verify this part information manually']
                ))
            
            # Check for critical parts
            description = item.get('description', '').lower()
            if any(re.search(pattern, description) for pattern in self.critical_patterns):
                if not item.get('material'):
                    issues.append(BOMValidationIssue(
                        severity='error',
                        field='material',
                        message='Critical part missing material specification',
                        part_number=part_number,
                        suggestions=['Material specification is critical for structural parts']
                    ))
            
            # Check cross-referencing
            if not item.get('cross_referenced', True):
                issues.append(BOMValidationIssue(
                    severity='info',
                    field='cross_reference',
                    message='Part not cross-referenced between views and parts list',
                    part_number=part_number,
                    suggestions=['Verify part appears in both drawing views and parts list']
                ))
            
            total_score += item_score
            max_score += item_max_score
        
        # Calculate completeness score
        completeness_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        # Determine overall status
        error_count = len([i for i in issues if i.severity == 'error'])
        warning_count = len([i for i in issues if i.severity == 'warning'])
        
        if error_count == 0 and warning_count == 0:
            status = BOMValidationStatus.COMPLETE
        elif error_count == 0:
            status = BOMValidationStatus.NEEDS_REVIEW
        elif completeness_score < 50:
            status = BOMValidationStatus.MISSING_CRITICAL
        else:
            status = BOMValidationStatus.INCOMPLETE
        
        return BOMValidationResult(
            status=status,
            completeness_score=completeness_score,
            issues=issues,
            missing_fields=list(missing_fields),
            flagged_parts=flagged_parts
        )


class BOMExporter:
    """Handles BOM export functionality"""
    
    def __init__(self):
        self.standard_headers = [
            'Part Number',
            'Description', 
            'Quantity',
            'Material',
            'Unit Weight (kg)',
            'Total Weight (kg)',
            'Volume (cm³)',
            'Notes'
        ]
    
    def export_to_csv(self, bom_items: List[BOMItem], include_metadata: bool = True) -> str:
        """
        Export BOM items to CSV format
        
        Args:
            bom_items: List of BOM items from database
            include_metadata: Whether to include metadata in export
            
        Returns:
            CSV content as string
        """
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write headers
        headers = self.standard_headers.copy()
        if include_metadata:
            headers.extend(['Confidence', 'Cross Referenced', 'Source'])
        
        writer.writerow(headers)
        
        # Write data rows
        for item in bom_items:
            row = [
                item.part_number or '',
                item.description or '',
                item.quantity or 1,
                item.material or '',
                f"{item.unit_weight:.4f}" if item.unit_weight else '',
                f"{item.weight:.4f}" if item.weight else '',
                f"{item.volume:.4f}" if item.volume else '',
                ''  # Notes column for user additions
            ]
            
            if include_metadata:
                # These would need to be stored in the database or passed separately
                row.extend(['', '', ''])  # Placeholder for metadata
            
            writer.writerow(row)
        
        return output.getvalue()
    
    def prepare_export_data(self, bom_items: List[BOMItem], design: Design) -> BOMExportData:
        """
        Prepare BOM data for export with metadata
        
        Args:
            bom_items: List of BOM items
            design: Design information
            
        Returns:
            Structured export data
        """
        rows = []
        
        for item in bom_items:
            row = [
                item.part_number or '',
                item.description or '',
                str(item.quantity or 1),
                item.material or '',
                f"{item.unit_weight:.4f}" if item.unit_weight else '',
                f"{item.weight:.4f}" if item.weight else '',
                f"{item.volume:.4f}" if item.volume else '',
                ''  # Notes
            ]
            rows.append(row)
        
        metadata = {
            'design_name': design.name,
            'design_id': design.id,
            'export_date': design.updated_at.isoformat() if design.updated_at else '',
            'total_parts': len(bom_items),
            'total_weight': sum(item.weight or 0 for item in bom_items),
            'unique_materials': len(set(item.material for item in bom_items if item.material))
        }
        
        return BOMExportData(
            headers=self.standard_headers,
            rows=rows,
            metadata=metadata
        )


class BOMService:
    """Main BOM generation and management service"""
    
    def __init__(self):
        self.extractor = BOMExtractor()
        self.validator = BOMValidator()
        self.exporter = BOMExporter()
    
    def generate_bom_from_analysis(self, design_id: int) -> Tuple[List[BOMItem], BOMValidationResult]:
        """
        Generate BOM from analysis results and save to database
        
        Args:
            design_id: Design ID to generate BOM for
            
        Returns:
            Tuple of (BOM items, validation result)
        """
        db = next(get_db())
        
        try:
            # Get the latest analysis result
            analysis_result = db.query(AnalysisResult).filter(
                AnalysisResult.design_id == design_id
            ).order_by(AnalysisResult.created_at.desc()).first()
            
            if not analysis_result:
                raise ValueError(f"No analysis results found for design {design_id}")
            
            # Extract BOM data
            extracted_parts = self.extractor.extract_bom_from_analysis(analysis_result)
            
            # Validate BOM
            validation_result = self.validator.validate_bom(extracted_parts)
            
            # Clear existing BOM items for this design
            db.query(BOMItem).filter(BOMItem.design_id == design_id).delete()
            
            # Create new BOM items
            bom_items = []
            for part_data in extracted_parts:
                bom_item = BOMItem(
                    design_id=design_id,
                    part_number=part_data.get('part_number'),
                    description=part_data.get('description'),
                    quantity=part_data.get('quantity', 1),
                    material=part_data.get('material'),
                    volume=None,  # Will be calculated by weight service
                    weight=None,  # Will be calculated by weight service
                    unit_weight=None  # Will be calculated by weight service
                )
                db.add(bom_item)
                bom_items.append(bom_item)
            
            db.commit()
            
            # Refresh items to get IDs
            for item in bom_items:
                db.refresh(item)
            
            logger.info(f"Generated BOM with {len(bom_items)} items for design {design_id}")
            
            return bom_items, validation_result
            
        except Exception as e:
            db.rollback()
            logger.error(f"BOM generation failed for design {design_id}: {str(e)}")
            raise
        finally:
            db.close()
    
    def get_bom_items(self, design_id: int) -> List[BOMItem]:
        """
        Get BOM items for a design
        
        Args:
            design_id: Design ID
            
        Returns:
            List of BOM items
        """
        db = next(get_db())
        
        try:
            bom_items = db.query(BOMItem).filter(
                BOMItem.design_id == design_id
            ).order_by(BOMItem.part_number).all()
            
            return bom_items
            
        finally:
            db.close()
    
    def validate_existing_bom(self, design_id: int) -> BOMValidationResult:
        """
        Validate existing BOM items
        
        Args:
            design_id: Design ID
            
        Returns:
            Validation result
        """
        bom_items = self.get_bom_items(design_id)
        
        # Convert to dict format for validation
        bom_dicts = []
        for item in bom_items:
            bom_dicts.append({
                'part_number': item.part_number,
                'description': item.description,
                'quantity': item.quantity,
                'material': item.material,
                'confidence': 100,  # Assume high confidence for saved items
                'cross_referenced': True
            })
        
        return self.validator.validate_bom(bom_dicts)
    
    def export_bom_csv(self, design_id: int) -> str:
        """
        Export BOM to CSV format
        
        Args:
            design_id: Design ID
            
        Returns:
            CSV content as string
        """
        bom_items = self.get_bom_items(design_id)
        return self.exporter.export_to_csv(bom_items)
    
    def get_bom_export_data(self, design_id: int) -> BOMExportData:
        """
        Get structured BOM export data
        
        Args:
            design_id: Design ID
            
        Returns:
            Structured export data
        """
        db = next(get_db())
        
        try:
            bom_items = self.get_bom_items(design_id)
            design = db.query(Design).filter(Design.id == design_id).first()
            
            if not design:
                raise ValueError(f"Design {design_id} not found")
            
            return self.exporter.prepare_export_data(bom_items, design)
            
        finally:
            db.close()
    
    def update_bom_item(self, item_id: int, updates: Dict[str, Any]) -> BOMItem:
        """
        Update a BOM item
        
        Args:
            item_id: BOM item ID
            updates: Dictionary of fields to update
            
        Returns:
            Updated BOM item
        """
        db = next(get_db())
        
        try:
            bom_item = db.query(BOMItem).filter(BOMItem.id == item_id).first()
            
            if not bom_item:
                raise ValueError(f"BOM item {item_id} not found")
            
            # Update allowed fields
            allowed_fields = ['part_number', 'description', 'quantity', 'material']
            for field, value in updates.items():
                if field in allowed_fields and hasattr(bom_item, field):
                    setattr(bom_item, field, value)
            
            db.commit()
            db.refresh(bom_item)
            
            return bom_item
            
        except Exception as e:
            db.rollback()
            raise
        finally:
            db.close()
    
    def delete_bom_item(self, item_id: int) -> bool:
        """
        Delete a BOM item
        
        Args:
            item_id: BOM item ID
            
        Returns:
            Success status
        """
        db = next(get_db())
        
        try:
            bom_item = db.query(BOMItem).filter(BOMItem.id == item_id).first()
            
            if not bom_item:
                return False
            
            db.delete(bom_item)
            db.commit()
            
            return True
            
        except Exception as e:
            db.rollback()
            raise
        finally:
            db.close()