"""
Graceful degradation service for handling partial analysis failures.
Provides fallback mechanisms and partial result handling.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

from ..utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorHandler
from ..utils.confidence_assessor import ConfidenceAssessor, AnalysisComponent, ConfidenceLevel


class DegradationLevel(Enum):
    """Levels of service degradation"""
    NONE = "none"           # Full functionality
    MINIMAL = "minimal"     # Minor features disabled
    MODERATE = "moderate"   # Some core features limited
    SEVERE = "severe"       # Major functionality reduced
    CRITICAL = "critical"   # Basic functionality only


@dataclass
class PartialResult:
    """Represents a partial analysis result"""
    component: AnalysisComponent
    success: bool
    data: Optional[Dict[str, Any]]
    error_code: Optional[ErrorCode]
    fallback_used: bool
    confidence_score: float
    user_message: str


@dataclass
class DegradedAnalysis:
    """Complete degraded analysis result"""
    overall_success: bool
    degradation_level: DegradationLevel
    partial_results: Dict[AnalysisComponent, PartialResult]
    available_features: List[str]
    disabled_features: List[str]
    user_guidance: List[str]
    retry_suggestions: List[str]


class GracefulDegradationService:
    """Handles graceful degradation of analysis services"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_handler = ErrorHandler()
    
    def handle_analysis_failure(
        self,
        analysis_results: Dict[AnalysisComponent, Any],
        errors: Dict[AnalysisComponent, Exception]
    ) -> DegradedAnalysis:
        """Handle partial analysis failures with graceful degradation"""
        
        partial_results = {}
        available_features = []
        disabled_features = []
        user_guidance = []
        retry_suggestions = []
        
        # Process each analysis component
        for component in AnalysisComponent:
            if component in errors:
                # Component failed - attempt fallback
                partial_result = self._handle_component_failure(
                    component, errors[component], analysis_results.get(component)
                )
            else:
                # Component succeeded
                partial_result = PartialResult(
                    component=component,
                    success=True,
                    data=analysis_results.get(component),
                    error_code=None,
                    fallback_used=False,
                    confidence_score=1.0,
                    user_message="Analysis completed successfully"
                )
            
            partial_results[component] = partial_result
            
            # Determine available/disabled features
            if partial_result.success or partial_result.fallback_used:
                available_features.extend(self._get_component_features(component))
            else:
                disabled_features.extend(self._get_component_features(component))
        
        # Determine overall degradation level
        degradation_level = self._calculate_degradation_level(partial_results)
        
        # Generate user guidance
        user_guidance = self._generate_user_guidance(partial_results, degradation_level)
        retry_suggestions = self._generate_retry_suggestions(partial_results)
        
        # Determine overall success
        overall_success = any(result.success for result in partial_results.values())
        
        return DegradedAnalysis(
            overall_success=overall_success,
            degradation_level=degradation_level,
            partial_results=partial_results,
            available_features=list(set(available_features)),
            disabled_features=list(set(disabled_features)),
            user_guidance=user_guidance,
            retry_suggestions=retry_suggestions
        )
    
    def _handle_component_failure(
        self,
        component: AnalysisComponent,
        error: Exception,
        partial_data: Any = None
    ) -> PartialResult:
        """Handle failure of a specific analysis component"""
        
        fallback_data = None
        fallback_used = False
        confidence_score = 0.0
        user_message = f"{component.value} analysis failed"
        
        # Attempt component-specific fallbacks
        if component == AnalysisComponent.IMAGE_QUALITY:
            fallback_data, fallback_used = self._fallback_image_quality(partial_data)
            user_message = "Using basic image quality assessment"
            
        elif component == AnalysisComponent.FEATURE_DETECTION:
            fallback_data, fallback_used = self._fallback_feature_detection(partial_data)
            user_message = "Using simplified feature detection"
            
        elif component == AnalysisComponent.OCR_TEXT:
            fallback_data, fallback_used = self._fallback_ocr(partial_data)
            user_message = "Text recognition limited - manual input may be needed"
            
        elif component == AnalysisComponent.BOM_EXTRACTION:
            fallback_data, fallback_used = self._fallback_bom_extraction(partial_data)
            user_message = "Partial BOM extracted - please review and complete manually"
            
        elif component == AnalysisComponent.WEIGHT_CALCULATION:
            fallback_data, fallback_used = self._fallback_weight_calculation(partial_data)
            user_message = "Weight estimates using default materials"
            
        elif component == AnalysisComponent.MODEL_3D_GENERATION:
            fallback_data, fallback_used = self._fallback_3d_generation(partial_data)
            user_message = "3D model generation not available"
        
        # Set confidence score based on fallback success
        if fallback_used and fallback_data:
            confidence_score = 0.3  # Low confidence for fallback data
        
        # Determine error code
        error_code = self._map_exception_to_error_code(error, component)
        
        return PartialResult(
            component=component,
            success=fallback_used and fallback_data is not None,
            data=fallback_data,
            error_code=error_code,
            fallback_used=fallback_used,
            confidence_score=confidence_score,
            user_message=user_message
        )
    
    def _fallback_image_quality(self, partial_data: Any) -> Tuple[Optional[Dict], bool]:
        """Fallback for image quality assessment"""
        try:
            # Provide basic quality metrics
            fallback_data = {
                'resolution': 200,  # Assume reasonable resolution
                'contrast': 0.5,    # Assume moderate contrast
                'noise_level': 0.1, # Assume low noise
                'blur_metric': 0.2, # Assume slight blur
                'quality_score': 0.6,
                'fallback_used': True
            }
            return fallback_data, True
        except Exception:
            return None, False
    
    def _fallback_feature_detection(self, partial_data: Any) -> Tuple[Optional[Dict], bool]:
        """Fallback for feature detection"""
        try:
            # Provide minimal feature data
            fallback_data = {
                'detected_lines': 0,
                'detected_circles': 0,
                'detected_rectangles': 0,
                'average_confidence': 0.0,
                'geometric_consistency': 0.0,
                'fallback_used': True,
                'manual_input_required': True
            }
            return fallback_data, True
        except Exception:
            return None, False
    
    def _fallback_ocr(self, partial_data: Any) -> Tuple[Optional[Dict], bool]:
        """Fallback for OCR text recognition"""
        try:
            # Provide empty OCR results structure
            fallback_data = {
                'text_confidence': 0.0,
                'extracted_text': [],
                'dimensions_found': 0,
                'part_numbers_found': 0,
                'material_specs_found': 0,
                'fallback_used': True,
                'manual_input_required': True
            }
            return fallback_data, True
        except Exception:
            return None, False
    
    def _fallback_bom_extraction(self, partial_data: Any) -> Tuple[Optional[Dict], bool]:
        """Fallback for BOM extraction"""
        try:
            # Create minimal BOM structure
            fallback_data = {
                'parts_extracted': 0,
                'bom_items': [],
                'missing_materials': 0,
                'missing_quantities': 0,
                'confidence_score': 0.0,
                'fallback_used': True,
                'manual_completion_required': True
            }
            return fallback_data, True
        except Exception:
            return None, False
    
    def _fallback_weight_calculation(self, partial_data: Any) -> Tuple[Optional[Dict], bool]:
        """Fallback for weight calculation"""
        try:
            # Provide default weight estimates
            fallback_data = {
                'total_weight': 0.0,
                'individual_weights': [],
                'volume_confidence': 0.0,
                'unknown_materials': 1,
                'default_material_used': 'Carbon Steel A36',
                'fallback_used': True,
                'estimates_only': True
            }
            return fallback_data, True
        except Exception:
            return None, False
    
    def _fallback_3d_generation(self, partial_data: Any) -> Tuple[Optional[Dict], bool]:
        """Fallback for 3D model generation"""
        # 3D generation typically can't have a meaningful fallback
        return None, False
    
    def _map_exception_to_error_code(self, error: Exception, component: AnalysisComponent) -> ErrorCode:
        """Map exception to appropriate error code"""
        error_type = type(error).__name__
        
        if component == AnalysisComponent.IMAGE_QUALITY:
            return ErrorCode.IMAGE_QUALITY_POOR
        elif component == AnalysisComponent.FEATURE_DETECTION:
            return ErrorCode.FEATURE_DETECTION_FAILED
        elif component == AnalysisComponent.OCR_TEXT:
            return ErrorCode.OCR_FAILED
        elif component == AnalysisComponent.BOM_EXTRACTION:
            return ErrorCode.BOM_EXTRACTION_FAILED
        elif component == AnalysisComponent.WEIGHT_CALCULATION:
            return ErrorCode.WEIGHT_CALCULATION_FAILED
        elif component == AnalysisComponent.MODEL_3D_GENERATION:
            return ErrorCode.MODEL_3D_GENERATION_FAILED
        else:
            return ErrorCode.PARTIAL_ANALYSIS_FAILURE
    
    def _get_component_features(self, component: AnalysisComponent) -> List[str]:
        """Get features provided by each component"""
        feature_map = {
            AnalysisComponent.IMAGE_QUALITY: ["image_preview", "quality_metrics"],
            AnalysisComponent.FEATURE_DETECTION: ["geometric_analysis", "shape_detection"],
            AnalysisComponent.OCR_TEXT: ["text_extraction", "dimension_reading"],
            AnalysisComponent.BOM_EXTRACTION: ["parts_list", "bom_table", "csv_export"],
            AnalysisComponent.WEIGHT_CALCULATION: ["weight_summary", "material_analysis"],
            AnalysisComponent.MODEL_3D_GENERATION: ["3d_viewer", "model_export"]
        }
        return feature_map.get(component, [])
    
    def _calculate_degradation_level(self, partial_results: Dict[AnalysisComponent, PartialResult]) -> DegradationLevel:
        """Calculate overall degradation level"""
        
        total_components = len(partial_results)
        successful_components = sum(1 for result in partial_results.values() if result.success and not result.fallback_used)
        fallback_components = sum(1 for result in partial_results.values() if result.fallback_used)
        failed_components = sum(1 for result in partial_results.values() if not result.success)
        
        success_rate = successful_components / total_components
        fallback_rate = fallback_components / total_components
        failure_rate = failed_components / total_components
        
        # Determine degradation level - fallbacks count as degradation
        if success_rate >= 0.9 and fallback_rate == 0:
            return DegradationLevel.NONE
        elif success_rate >= 0.7 and fallback_rate <= 0.2:
            return DegradationLevel.MINIMAL
        elif success_rate >= 0.5 or (fallback_rate >= 0.3 and failure_rate <= 0.3):
            return DegradationLevel.MODERATE
        elif success_rate >= 0.3 or failure_rate <= 0.7:
            return DegradationLevel.SEVERE
        else:
            return DegradationLevel.CRITICAL
    
    def _generate_user_guidance(
        self,
        partial_results: Dict[AnalysisComponent, PartialResult],
        degradation_level: DegradationLevel
    ) -> List[str]:
        """Generate user guidance based on degradation level"""
        
        guidance = []
        
        if degradation_level == DegradationLevel.NONE:
            guidance.append("All analysis components completed successfully")
        
        elif degradation_level == DegradationLevel.MINIMAL:
            guidance.append("Analysis completed with minor limitations")
            guidance.append("Some advanced features may not be available")
        
        elif degradation_level == DegradationLevel.MODERATE:
            guidance.append("Analysis completed with some limitations")
            guidance.append("Manual input may be required for missing information")
            guidance.append("Review results carefully and add missing details")
        
        elif degradation_level == DegradationLevel.SEVERE:
            guidance.append("Analysis completed with significant limitations")
            guidance.append("Many features require manual completion")
            guidance.append("Consider uploading a higher quality drawing")
        
        else:  # CRITICAL
            guidance.append("Analysis failed for most components")
            guidance.append("Manual input required for most information")
            guidance.append("Please check drawing quality and try again")
        
        # Add component-specific guidance
        for component, result in partial_results.items():
            if not result.success and not result.fallback_used:
                if component == AnalysisComponent.OCR_TEXT:
                    guidance.append("Text recognition failed - use manual input for dimensions and specifications")
                elif component == AnalysisComponent.BOM_EXTRACTION:
                    guidance.append("BOM extraction failed - manually create parts list")
                elif component == AnalysisComponent.MODEL_3D_GENERATION:
                    guidance.append("3D model generation not available - 2D analysis only")
        
        return list(set(guidance))  # Remove duplicates
    
    def _generate_retry_suggestions(self, partial_results: Dict[AnalysisComponent, PartialResult]) -> List[str]:
        """Generate retry suggestions based on failures"""
        
        suggestions = []
        
        # Check for image quality issues
        image_result = partial_results.get(AnalysisComponent.IMAGE_QUALITY)
        if image_result and not image_result.success:
            suggestions.extend([
                "Upload a higher resolution image (minimum 300 DPI)",
                "Ensure clear contrast between lines and background",
                "Remove shadows and distortions from scan"
            ])
        
        # Check for OCR issues
        ocr_result = partial_results.get(AnalysisComponent.OCR_TEXT)
        if ocr_result and not ocr_result.success:
            suggestions.extend([
                "Ensure text is clearly readable and not blurred",
                "Check that drawing orientation is correct",
                "Verify text size is adequate (minimum 8pt font)"
            ])
        
        # Check for feature detection issues
        feature_result = partial_results.get(AnalysisComponent.FEATURE_DETECTION)
        if feature_result and not feature_result.success:
            suggestions.extend([
                "Ensure drawing lines are continuous and well-defined",
                "Remove background patterns or watermarks",
                "Check that drawing follows standard conventions"
            ])
        
        # General suggestions
        failed_count = sum(1 for result in partial_results.values() if not result.success)
        if failed_count > len(partial_results) / 2:
            suggestions.extend([
                "Try uploading the drawing in a different format",
                "Consider splitting complex drawings into sections",
                "Contact support if issues persist"
            ])
        
        return list(set(suggestions))  # Remove duplicates
    
    def create_manual_correction_interface_data(self, degraded_analysis: DegradedAnalysis) -> Dict[str, Any]:
        """Create data structure for manual correction interface"""
        
        correction_data = {
            'requires_manual_input': [],
            'editable_fields': {},
            'suggestions': {},
            'templates': {}
        }
        
        # Identify components requiring manual input
        for component, result in degraded_analysis.partial_results.items():
            if not result.success or (result.fallback_used and result.confidence_score < 0.5):
                correction_data['requires_manual_input'].append(component.value)
                
                # Add component-specific correction interfaces
                if component == AnalysisComponent.OCR_TEXT:
                    correction_data['editable_fields']['dimensions'] = []
                    correction_data['editable_fields']['part_numbers'] = []
                    correction_data['editable_fields']['materials'] = []
                    correction_data['templates']['dimension_format'] = "Length x Width x Height"
                
                elif component == AnalysisComponent.BOM_EXTRACTION:
                    correction_data['editable_fields']['bom_items'] = []
                    correction_data['templates']['bom_item'] = {
                        'part_number': '',
                        'description': '',
                        'quantity': 1,
                        'material': ''
                    }
                
                elif component == AnalysisComponent.WEIGHT_CALCULATION:
                    correction_data['editable_fields']['materials'] = []
                    correction_data['suggestions']['common_materials'] = [
                        'Carbon Steel A36', 'Aluminum 6061', 'Stainless Steel 304'
                    ]
        
        return correction_data