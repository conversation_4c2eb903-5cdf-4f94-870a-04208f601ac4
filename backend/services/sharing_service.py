"""
Design sharing and collaboration service.
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPException, status
from datetime import datetime

from models.sharing import DesignShare
from models.design import Design
from models.user import User


class SharingService:
    """Service for managing design sharing and collaboration."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def grant_access(
        self, 
        design_id: int, 
        owner_id: int, 
        shared_with_email: str, 
        permission_level: str = "view"
    ) -> DesignShare:
        """Grant access to a design for another user."""
        # Validate permission level
        if permission_level not in ["view", "edit"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Permission level must be 'view' or 'edit'"
            )
        
        # Check if design exists and user owns it
        design = self.db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Design not found"
            )
        
        if design.user_id != owner_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only share your own designs"
            )
        
        # Find user to share with
        shared_with_user = self.db.query(User).filter(User.email == shared_with_email).first()
        if not shared_with_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if user is trying to share with themselves
        if shared_with_user.id == owner_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot share design with yourself"
            )
        
        # Check if sharing already exists
        existing_share = self.db.query(DesignShare).filter(
            and_(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == shared_with_user.id
            )
        ).first()
        
        if existing_share:
            # Update existing share
            existing_share.permission_level = permission_level
            self.db.commit()
            self.db.refresh(existing_share)
            return existing_share
        
        # Create new share
        new_share = DesignShare(
            design_id=design_id,
            owner_id=owner_id,
            shared_with_id=shared_with_user.id,
            permission_level=permission_level
        )
        
        self.db.add(new_share)
        self.db.commit()
        self.db.refresh(new_share)
        
        return new_share
    
    def revoke_access(self, design_id: int, owner_id: int, shared_with_id: int) -> bool:
        """Revoke access to a design from a user."""
        # Check if design exists and user owns it
        design = self.db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Design not found"
            )
        
        if design.user_id != owner_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only manage sharing for your own designs"
            )
        
        # Find and delete the share
        share = self.db.query(DesignShare).filter(
            and_(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == shared_with_id
            )
        ).first()
        
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Share not found"
            )
        
        self.db.delete(share)
        self.db.commit()
        
        return True
    
    def get_design_shares(self, design_id: int, user_id: int) -> List[Dict[str, Any]]:
        """Get all users who have access to a design."""
        # Check if design exists and user owns it
        design = self.db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Design not found"
            )
        
        if design.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only view sharing information for your own designs"
            )
        
        # Get all shares for this design
        shares = self.db.query(DesignShare).filter(
            DesignShare.design_id == design_id
        ).all()
        
        result = []
        for share in shares:
            user = self.db.query(User).filter(User.id == share.shared_with_id).first()
            if user:
                result.append({
                    "share_id": share.id,
                    "user_id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "permission_level": share.permission_level,
                    "shared_at": share.created_at
                })
        
        return result
    
    def get_shared_with_me(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all designs shared with the current user."""
        shares = self.db.query(DesignShare).filter(
            DesignShare.shared_with_id == user_id
        ).all()
        
        result = []
        for share in shares:
            design = self.db.query(Design).filter(Design.id == share.design_id).first()
            owner = self.db.query(User).filter(User.id == share.owner_id).first()
            
            if design and owner:
                result.append({
                    "share_id": share.id,
                    "design_id": design.id,
                    "design_name": design.name,
                    "owner_username": owner.username,
                    "owner_email": owner.email,
                    "permission_level": share.permission_level,
                    "shared_at": share.created_at,
                    "design_status": design.status,
                    "design_created_at": design.created_at,
                    "design_updated_at": design.updated_at
                })
        
        return result
    
    def check_access(self, design_id: int, user_id: int) -> Optional[str]:
        """Check if user has access to a design and return permission level."""
        # Check if user owns the design
        design = self.db.query(Design).filter(Design.id == design_id).first()
        if not design:
            return None
        
        if design.user_id == user_id:
            return "owner"
        
        # Check if design is shared with user
        share = self.db.query(DesignShare).filter(
            and_(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == user_id
            )
        ).first()
        
        if share:
            return share.permission_level
        
        return None
    
    def update_permission(
        self, 
        design_id: int, 
        owner_id: int, 
        shared_with_id: int, 
        permission_level: str
    ) -> DesignShare:
        """Update permission level for an existing share."""
        # Validate permission level
        if permission_level not in ["view", "edit"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Permission level must be 'view' or 'edit'"
            )
        
        # Check if design exists and user owns it
        design = self.db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Design not found"
            )
        
        if design.user_id != owner_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only manage sharing for your own designs"
            )
        
        # Find and update the share
        share = self.db.query(DesignShare).filter(
            and_(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == shared_with_id
            )
        ).first()
        
        if not share:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Share not found"
            )
        
        share.permission_level = permission_level
        self.db.commit()
        self.db.refresh(share)
        
        return share
    
    def get_sharing_history(self, design_id: int, user_id: int) -> List[Dict[str, Any]]:
        """Get sharing history for a design (audit log)."""
        # Check if design exists and user owns it
        design = self.db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Design not found"
            )
        
        if design.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only view sharing history for your own designs"
            )
        
        # Get all current and historical shares
        # Note: This is a simplified version. In a production system,
        # you might want to maintain a separate audit log table
        shares = self.db.query(DesignShare).filter(
            DesignShare.design_id == design_id
        ).all()
        
        result = []
        for share in shares:
            user = self.db.query(User).filter(User.id == share.shared_with_id).first()
            if user:
                result.append({
                    "action": "granted",
                    "user_id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "permission_level": share.permission_level,
                    "timestamp": share.created_at
                })
        
        return sorted(result, key=lambda x: x["timestamp"], reverse=True)
    
    def search_users(self, query: str, current_user_id: int) -> List[Dict[str, Any]]:
        """Search for users to share with (excluding current user)."""
        if len(query) < 2:
            return []
        
        users = self.db.query(User).filter(
            and_(
                User.id != current_user_id,
                User.is_active == True,
                or_(
                    User.username.ilike(f"%{query}%"),
                    User.email.ilike(f"%{query}%")
                )
            )
        ).limit(10).all()
        
        result = []
        for user in users:
            result.append({
                "user_id": user.id,
                "username": user.username,
                "email": user.email
            })
        
        return result