"""
Retry mechanisms for failed analysis operations.
Provides intelligent retry logic with exponential backoff and failure recovery.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timedelta

from ..utils.error_handler import <PERSON>rror<PERSON><PERSON>, ErrorHandler


class RetryStrategy(Enum):
    """Different retry strategies"""
    IMMEDIATE = "immediate"
    LINEAR_BACKOFF = "linear_backoff"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    CUSTOM_INTERVALS = "custom_intervals"


class RetryReason(Enum):
    """Reasons for retry attempts"""
    TRANSIENT_ERROR = "transient_error"
    RESOURCE_UNAVAILABLE = "resource_unavailable"
    TIMEOUT = "timeout"
    NETWORK_ERROR = "network_error"
    USER_REQUESTED = "user_requested"
    IMPROVED_INPUT = "improved_input"


@dataclass
class RetryConfig:
    """Configuration for retry attempts"""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0  # seconds
    max_delay: float = 60.0  # seconds
    backoff_multiplier: float = 2.0
    custom_intervals: Optional[List[float]] = None
    retry_on_errors: List[ErrorCode] = None
    timeout_per_attempt: Optional[float] = None


@dataclass
class RetryAttempt:
    """Information about a retry attempt"""
    attempt_number: int
    timestamp: datetime
    error_code: Optional[ErrorCode]
    error_message: str
    delay_before_attempt: float
    success: bool
    execution_time: float


@dataclass
class RetryResult:
    """Result of retry operation"""
    success: bool
    final_result: Any
    total_attempts: int
    total_time: float
    attempts_history: List[RetryAttempt]
    final_error: Optional[Exception]


class RetryService:
    """Service for handling retry operations"""
    
    # Default retry configurations for different error types
    DEFAULT_CONFIGS = {
        ErrorCode.ANALYSIS_TIMEOUT: RetryConfig(
            max_attempts=2,
            strategy=RetryStrategy.LINEAR_BACKOFF,
            base_delay=5.0,
            retry_on_errors=[ErrorCode.ANALYSIS_TIMEOUT]
        ),
        ErrorCode.DATABASE_ERROR: RetryConfig(
            max_attempts=3,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=1.0,
            retry_on_errors=[ErrorCode.DATABASE_ERROR, ErrorCode.NETWORK_ERROR]
        ),
        ErrorCode.FILE_STORAGE_ERROR: RetryConfig(
            max_attempts=3,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=2.0,
            retry_on_errors=[ErrorCode.FILE_STORAGE_ERROR, ErrorCode.NETWORK_ERROR]
        ),
        ErrorCode.SERVICE_UNAVAILABLE: RetryConfig(
            max_attempts=5,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=1.0,
            max_delay=30.0,
            retry_on_errors=[ErrorCode.SERVICE_UNAVAILABLE, ErrorCode.NETWORK_ERROR]
        ),
        ErrorCode.MEMORY_EXCEEDED: RetryConfig(
            max_attempts=2,
            strategy=RetryStrategy.LINEAR_BACKOFF,
            base_delay=10.0,
            retry_on_errors=[ErrorCode.MEMORY_EXCEEDED]
        )
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_handler = ErrorHandler()
    
    async def retry_with_config(
        self,
        operation: Callable,
        config: RetryConfig,
        operation_args: Tuple = (),
        operation_kwargs: Dict = None,
        context: Dict[str, Any] = None
    ) -> RetryResult:
        """Execute operation with retry logic based on configuration"""
        
        if operation_kwargs is None:
            operation_kwargs = {}
        
        attempts_history = []
        start_time = time.time()
        final_error = None
        
        for attempt in range(1, config.max_attempts + 1):
            attempt_start = time.time()
            
            # Calculate delay for this attempt
            if attempt > 1:
                delay = self._calculate_delay(config, attempt - 1)
                await asyncio.sleep(delay)
            else:
                delay = 0.0
            
            try:
                # Execute the operation
                if asyncio.iscoroutinefunction(operation):
                    result = await operation(*operation_args, **operation_kwargs)
                else:
                    result = operation(*operation_args, **operation_kwargs)
                
                # Success - record attempt and return
                attempt_time = time.time() - attempt_start
                attempts_history.append(RetryAttempt(
                    attempt_number=attempt,
                    timestamp=datetime.utcnow(),
                    error_code=None,
                    error_message="",
                    delay_before_attempt=delay,
                    success=True,
                    execution_time=attempt_time
                ))
                
                total_time = time.time() - start_time
                
                self.logger.info(f"Operation succeeded on attempt {attempt}/{config.max_attempts}")
                
                return RetryResult(
                    success=True,
                    final_result=result,
                    total_attempts=attempt,
                    total_time=total_time,
                    attempts_history=attempts_history,
                    final_error=None
                )
            
            except Exception as error:
                attempt_time = time.time() - attempt_start
                error_code = self._determine_error_code(error)
                
                # Record failed attempt
                attempts_history.append(RetryAttempt(
                    attempt_number=attempt,
                    timestamp=datetime.utcnow(),
                    error_code=error_code,
                    error_message=str(error),
                    delay_before_attempt=delay,
                    success=False,
                    execution_time=attempt_time
                ))
                
                final_error = error
                
                # Check if we should retry this error
                if not self._should_retry_error(error_code, config):
                    self.logger.warning(f"Error {error_code} not retryable, stopping after attempt {attempt}")
                    break
                
                # Check if this is the last attempt
                if attempt == config.max_attempts:
                    self.logger.error(f"Operation failed after {attempt} attempts")
                    break
                
                self.logger.warning(f"Attempt {attempt} failed with {error_code}, retrying...")
        
        # All attempts failed
        total_time = time.time() - start_time
        
        return RetryResult(
            success=False,
            final_result=None,
            total_attempts=len(attempts_history),
            total_time=total_time,
            attempts_history=attempts_history,
            final_error=final_error
        )
    
    async def retry_analysis_component(
        self,
        component_name: str,
        operation: Callable,
        error_code: ErrorCode,
        operation_args: Tuple = (),
        operation_kwargs: Dict = None,
        custom_config: RetryConfig = None
    ) -> RetryResult:
        """Retry a specific analysis component with appropriate configuration"""
        
        # Use custom config or default for error type
        config = custom_config or self.DEFAULT_CONFIGS.get(error_code, RetryConfig())
        
        context = {
            'component': component_name,
            'original_error': error_code
        }
        
        self.logger.info(f"Starting retry for {component_name} due to {error_code}")
        
        result = await self.retry_with_config(
            operation=operation,
            config=config,
            operation_args=operation_args,
            operation_kwargs=operation_kwargs,
            context=context
        )
        
        # Log final result
        if result.success:
            self.logger.info(f"Retry successful for {component_name} after {result.total_attempts} attempts")
        else:
            self.logger.error(f"Retry failed for {component_name} after {result.total_attempts} attempts")
        
        return result
    
    def _calculate_delay(self, config: RetryConfig, attempt_number: int) -> float:
        """Calculate delay before retry attempt"""
        
        if config.strategy == RetryStrategy.IMMEDIATE:
            return 0.0
        
        elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay * attempt_number
        
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.backoff_multiplier ** (attempt_number - 1))
        
        elif config.strategy == RetryStrategy.CUSTOM_INTERVALS:
            if config.custom_intervals and attempt_number <= len(config.custom_intervals):
                delay = config.custom_intervals[attempt_number - 1]
            else:
                delay = config.base_delay
        
        else:
            delay = config.base_delay
        
        # Apply maximum delay limit
        return min(delay, config.max_delay)
    
    def _determine_error_code(self, error: Exception) -> ErrorCode:
        """Determine error code from exception"""
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        if "timeout" in error_message:
            return ErrorCode.ANALYSIS_TIMEOUT
        elif "database" in error_message or "sql" in error_message:
            return ErrorCode.DATABASE_ERROR
        elif "file" in error_message or "storage" in error_message:
            return ErrorCode.FILE_STORAGE_ERROR
        elif "memory" in error_message:
            return ErrorCode.MEMORY_EXCEEDED
        elif "network" in error_message or "connection" in error_message:
            return ErrorCode.NETWORK_ERROR
        else:
            return ErrorCode.SERVICE_UNAVAILABLE
    
    def _should_retry_error(self, error_code: ErrorCode, config: RetryConfig) -> bool:
        """Determine if an error should be retried"""
        
        if config.retry_on_errors:
            return error_code in config.retry_on_errors
        
        # Default retryable errors
        retryable_errors = {
            ErrorCode.ANALYSIS_TIMEOUT,
            ErrorCode.DATABASE_ERROR,
            ErrorCode.FILE_STORAGE_ERROR,
            ErrorCode.SERVICE_UNAVAILABLE,
            ErrorCode.NETWORK_ERROR,
            ErrorCode.MEMORY_EXCEEDED
        }
        
        return error_code in retryable_errors
    
    def create_user_retry_options(self, error_code: ErrorCode, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create retry options for user interface"""
        
        retry_options = {
            'can_retry': True,
            'retry_suggestions': [],
            'estimated_wait_time': 0,
            'retry_with_changes': False,
            'manual_alternatives': []
        }
        
        # Determine if retry is possible
        if error_code in {ErrorCode.INVALID_FILE_FORMAT, ErrorCode.FILE_TOO_LARGE, ErrorCode.ACCESS_DENIED}:
            retry_options['can_retry'] = False
            retry_options['retry_suggestions'] = ["Please fix the issue and upload again"]
            return retry_options
        
        # Get retry configuration
        config = self.DEFAULT_CONFIGS.get(error_code, RetryConfig())
        
        # Calculate estimated wait time
        total_wait = 0
        for attempt in range(1, config.max_attempts):
            total_wait += self._calculate_delay(config, attempt)
        retry_options['estimated_wait_time'] = int(total_wait)
        
        # Add error-specific suggestions
        if error_code == ErrorCode.IMAGE_QUALITY_POOR:
            retry_options['retry_with_changes'] = True
            retry_options['retry_suggestions'] = [
                "Upload a higher resolution image",
                "Improve image contrast and clarity",
                "Ensure proper lighting when scanning"
            ]
            retry_options['manual_alternatives'] = [
                "Use manual input for dimensions",
                "Manually create BOM from drawing"
            ]
        
        elif error_code == ErrorCode.OCR_FAILED:
            retry_options['retry_with_changes'] = True
            retry_options['retry_suggestions'] = [
                "Ensure text is clearly readable",
                "Check drawing orientation",
                "Try a different file format"
            ]
            retry_options['manual_alternatives'] = [
                "Manually enter text and dimensions",
                "Use manual correction interface"
            ]
        
        elif error_code == ErrorCode.ANALYSIS_TIMEOUT:
            retry_options['retry_suggestions'] = [
                "Retry with current settings",
                "Try with a smaller or simpler drawing"
            ]
            retry_options['manual_alternatives'] = [
                "Split drawing into sections",
                "Use simplified analysis mode"
            ]
        
        elif error_code in {ErrorCode.DATABASE_ERROR, ErrorCode.SERVICE_UNAVAILABLE}:
            retry_options['retry_suggestions'] = [
                "Retry automatically in a few moments",
                "System issue should resolve shortly"
            ]
        
        return retry_options


class ManualCorrectionService:
    """Service for handling manual corrections and user input"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def create_correction_session(
        self,
        design_id: str,
        failed_components: List[str],
        partial_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a manual correction session for failed analysis"""
        
        session_id = f"correction_{design_id}_{int(time.time())}"
        
        correction_session = {
            'session_id': session_id,
            'design_id': design_id,
            'created_at': datetime.utcnow().isoformat(),
            'status': 'active',
            'failed_components': failed_components,
            'correction_interfaces': {},
            'progress': {
                'total_fields': 0,
                'completed_fields': 0,
                'percentage': 0
            }
        }
        
        # Create correction interfaces for each failed component
        for component in failed_components:
            interface_data = self._create_component_correction_interface(
                component, partial_data.get(component, {})
            )
            correction_session['correction_interfaces'][component] = interface_data
            correction_session['progress']['total_fields'] += interface_data.get('field_count', 0)
        
        return correction_session
    
    def _create_component_correction_interface(self, component: str, partial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create correction interface for a specific component"""
        
        if component == 'ocr_text':
            return {
                'type': 'text_correction',
                'title': 'Text and Dimension Correction',
                'description': 'Please review and correct the extracted text and dimensions',
                'fields': [
                    {
                        'id': 'dimensions',
                        'label': 'Dimensions',
                        'type': 'list',
                        'items': partial_data.get('dimensions', []),
                        'template': {'value': '', 'unit': 'mm', 'tolerance': ''}
                    },
                    {
                        'id': 'part_numbers',
                        'label': 'Part Numbers',
                        'type': 'list',
                        'items': partial_data.get('part_numbers', []),
                        'template': {'number': '', 'description': ''}
                    },
                    {
                        'id': 'materials',
                        'label': 'Material Specifications',
                        'type': 'list',
                        'items': partial_data.get('materials', []),
                        'template': {'material': '', 'grade': '', 'thickness': ''}
                    }
                ],
                'field_count': 3
            }
        
        elif component == 'bom_extraction':
            return {
                'type': 'bom_correction',
                'title': 'Bill of Materials Correction',
                'description': 'Please complete the Bill of Materials information',
                'fields': [
                    {
                        'id': 'bom_items',
                        'label': 'BOM Items',
                        'type': 'table',
                        'columns': ['Part Number', 'Description', 'Quantity', 'Material'],
                        'items': partial_data.get('bom_items', []),
                        'template': {
                            'part_number': '',
                            'description': '',
                            'quantity': 1,
                            'material': ''
                        }
                    }
                ],
                'field_count': 1,
                'suggestions': {
                    'common_materials': [
                        'Carbon Steel A36',
                        'Aluminum 6061',
                        'Stainless Steel 304',
                        'Brass 360'
                    ]
                }
            }
        
        elif component == 'weight_calculation':
            return {
                'type': 'material_correction',
                'title': 'Material and Weight Correction',
                'description': 'Please specify materials for accurate weight calculation',
                'fields': [
                    {
                        'id': 'part_materials',
                        'label': 'Part Materials',
                        'type': 'list',
                        'items': partial_data.get('parts', []),
                        'template': {
                            'part_id': '',
                            'material': '',
                            'density': '',
                            'estimated_volume': ''
                        }
                    }
                ],
                'field_count': 1
            }
        
        else:
            return {
                'type': 'generic_correction',
                'title': f'{component.replace("_", " ").title()} Correction',
                'description': f'Please provide manual input for {component}',
                'fields': [],
                'field_count': 0
            }
    
    def validate_correction_data(self, component: str, correction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate manual correction data"""
        
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        if component == 'bom_extraction':
            bom_items = correction_data.get('bom_items', [])
            for i, item in enumerate(bom_items):
                if not item.get('part_number'):
                    validation_result['errors'].append(f"Item {i+1}: Part number is required")
                if not item.get('quantity') or item.get('quantity', 0) <= 0:
                    validation_result['errors'].append(f"Item {i+1}: Valid quantity is required")
                if not item.get('material'):
                    validation_result['warnings'].append(f"Item {i+1}: Material not specified")
        
        elif component == 'ocr_text':
            dimensions = correction_data.get('dimensions', [])
            for i, dim in enumerate(dimensions):
                if not dim.get('value'):
                    validation_result['warnings'].append(f"Dimension {i+1}: Value not specified")
        
        validation_result['valid'] = len(validation_result['errors']) == 0
        
        return validation_result
    
    def apply_corrections(
        self,
        session_id: str,
        corrections: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply manual corrections and update analysis results"""
        
        result = {
            'success': True,
            'updated_components': [],
            'errors': []
        }
        
        for component, correction_data in corrections.items():
            validation = self.validate_correction_data(component, correction_data)
            
            if validation['valid']:
                # Apply corrections (in real implementation, this would update the database)
                result['updated_components'].append(component)
                self.logger.info(f"Applied corrections for component: {component}")
            else:
                result['errors'].extend(validation['errors'])
                result['success'] = False
        
        return result