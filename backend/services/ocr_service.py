"""
OCR Service

Service layer for OCR text extraction functionality.
Integrates with the processing module and provides API endpoints.
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
import json
from dataclasses import asdict

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from processing.ocr.text_extractor import OCRTextExtractor, OCRResult, ExtractedText, TextType

logger = logging.getLogger(__name__)


class OCRService:
    """Service for handling OCR operations"""
    
    def __init__(self):
        self.ocr_extractor = OCRTextExtractor()
    
    async def process_image_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process an image file and extract text
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Dictionary containing OCR results
        """
        try:
            # Load image
            image = cv2.imread(file_path)
            if image is None:
                raise ValueError(f"Could not load image from {file_path}")
            
            # Extract text
            ocr_result = self.ocr_extractor.extract_text_from_image(image)
            
            # Convert to serializable format
            result_dict = self._ocr_result_to_dict(ocr_result)
            
            # Add file metadata
            result_dict['source_file'] = file_path
            result_dict['image_dimensions'] = {
                'width': image.shape[1],
                'height': image.shape[0],
                'channels': image.shape[2] if len(image.shape) > 2 else 1
            }
            
            return result_dict
            
        except Exception as e:
            error_msg = f"Failed to process image {file_path}: {str(e)}"
            logger.error(error_msg)
            return {
                'source_file': file_path,
                'extracted_texts': [],
                'overall_confidence': 0.0,
                'processing_time': 0.0,
                'errors': [error_msg],
                'warnings': []
            }
    
    async def extract_patterns_from_file(self, file_path: str) -> Dict[str, List[str]]:
        """
        Extract specific patterns from an image file
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Dictionary with extracted patterns by type
        """
        try:
            image = cv2.imread(file_path)
            if image is None:
                raise ValueError(f"Could not load image from {file_path}")
            
            patterns = self.ocr_extractor.extract_specific_patterns(image)
            return patterns
            
        except Exception as e:
            logger.error(f"Failed to extract patterns from {file_path}: {str(e)}")
            return {
                'dimensions': [],
                'part_numbers': [],
                'materials': [],
                'annotations': []
            }
    
    async def validate_ocr_results(self, ocr_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate OCR results and provide quality assessment
        
        Args:
            ocr_results: OCR results dictionary
            
        Returns:
            Validation results with quality metrics
        """
        validation_result = {
            'is_valid': True,
            'quality_score': 0.0,
            'issues': [],
            'recommendations': []
        }
        
        try:
            overall_confidence = ocr_results.get('overall_confidence', 0.0)
            extracted_texts = ocr_results.get('extracted_texts', [])
            errors = ocr_results.get('errors', [])
            
            # Calculate quality score
            if errors:
                validation_result['quality_score'] = 0.0
                validation_result['is_valid'] = False
                validation_result['issues'].append("OCR processing errors occurred")
            elif not extracted_texts:
                validation_result['quality_score'] = 0.0
                validation_result['issues'].append("No text extracted from image")
                validation_result['recommendations'].append("Try uploading a higher quality image")
            else:
                validation_result['quality_score'] = overall_confidence
                
                # Quality assessment based on confidence
                if overall_confidence < 30:
                    validation_result['issues'].append("Very low text recognition confidence")
                    validation_result['recommendations'].extend([
                        "Ensure image has clear, dark text",
                        "Try uploading a higher resolution image",
                        "Check that the drawing is properly scanned"
                    ])
                elif overall_confidence < 60:
                    validation_result['issues'].append("Low text recognition confidence")
                    validation_result['recommendations'].append("Consider manual verification of extracted text")
                
                # Check for specific text types
                text_types = [text.get('text_type') for text in extracted_texts]
                if 'dimension' not in text_types:
                    validation_result['issues'].append("No dimensions detected")
                if 'part_number' not in text_types:
                    validation_result['issues'].append("No part numbers detected")
                if 'material' not in text_types:
                    validation_result['issues'].append("No material specifications detected")
            
        except Exception as e:
            logger.error(f"Validation failed: {str(e)}")
            validation_result['is_valid'] = False
            validation_result['issues'].append(f"Validation error: {str(e)}")
        
        return validation_result
    
    def _ocr_result_to_dict(self, ocr_result: OCRResult) -> Dict[str, Any]:
        """Convert OCRResult to serializable dictionary"""
        return {
            'extracted_texts': [self._extracted_text_to_dict(et) for et in ocr_result.extracted_texts],
            'overall_confidence': ocr_result.overall_confidence,
            'processing_time': ocr_result.processing_time,
            'errors': ocr_result.errors,
            'warnings': ocr_result.warnings
        }
    
    def _extracted_text_to_dict(self, extracted_text: ExtractedText) -> Dict[str, Any]:
        """Convert ExtractedText to serializable dictionary"""
        return {
            'text': extracted_text.text,
            'confidence': extracted_text.confidence,
            'bbox': extracted_text.bbox,
            'text_type': extracted_text.text_type.value,
            'has_preprocessed_image': extracted_text.preprocessed_image is not None
        }


class ManualCorrectionService:
    """Service for handling manual corrections of OCR results"""
    
    def __init__(self):
        pass
    
    async def create_correction_session(self, ocr_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a manual correction session for failed OCR results
        
        Args:
            ocr_results: Original OCR results
            
        Returns:
            Correction session data
        """
        correction_session = {
            'session_id': self._generate_session_id(),
            'original_results': ocr_results,
            'corrections_needed': [],
            'status': 'pending'
        }
        
        # Identify texts that need correction
        for text_data in ocr_results.get('extracted_texts', []):
            if text_data.get('confidence', 0) < 50:  # Low confidence threshold
                correction_session['corrections_needed'].append({
                    'original_text': text_data.get('text', ''),
                    'confidence': text_data.get('confidence', 0),
                    'bbox': text_data.get('bbox', [0, 0, 0, 0]),
                    'text_type': text_data.get('text_type', 'general'),
                    'suggested_corrections': self._generate_suggestions(text_data.get('text', '')),
                    'corrected_text': None,
                    'user_verified': False
                })
        
        return correction_session
    
    async def apply_corrections(self, session_id: str, corrections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Apply manual corrections to OCR results
        
        Args:
            session_id: Correction session ID
            corrections: List of corrections to apply
            
        Returns:
            Updated OCR results with corrections
        """
        # In a real implementation, this would load the session from storage
        # For now, we'll return a mock corrected result
        
        corrected_results = {
            'session_id': session_id,
            'corrections_applied': len(corrections),
            'status': 'completed',
            'updated_results': {
                'extracted_texts': [],
                'overall_confidence': 0.0,
                'manual_corrections': corrections
            }
        }
        
        return corrected_results
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID"""
        import uuid
        return str(uuid.uuid4())
    
    def _generate_suggestions(self, original_text: str) -> List[str]:
        """Generate correction suggestions for unclear text"""
        suggestions = []
        
        # Common OCR error corrections
        corrections_map = {
            '0': ['O', 'D'],
            'O': ['0', 'Q'],
            '1': ['I', 'l'],
            'I': ['1', 'l'],
            '5': ['S'],
            'S': ['5'],
            '8': ['B'],
            'B': ['8']
        }
        
        # Generate variations based on common OCR errors
        for char, replacements in corrections_map.items():
            if char in original_text:
                for replacement in replacements:
                    suggestion = original_text.replace(char, replacement)
                    if suggestion != original_text:
                        suggestions.append(suggestion)
        
        return suggestions[:3]  # Limit to top 3 suggestions