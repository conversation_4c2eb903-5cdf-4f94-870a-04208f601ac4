"""
File storage and management service using MinIO/S3.
"""
import os
import uuid
import mimetypes
from typing import Optional, List, BinaryIO
from datetime import datetime, timedelta
import aiofiles
from minio import Minio
from minio.error import S3Error
from fastapi import HTTPException, UploadFile
from sqlalchemy.orm import Session
from models.design import Design
from models.user import User
import logging

logger = logging.getLogger(__name__)

class FileStorageService:
    """Service for handling file storage operations with MinIO/S3."""
    
    # Supported file formats
    SUPPORTED_FORMATS = {
        'application/pdf': ['.pdf'],
        'image/png': ['.png'],
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/tiff': ['.tiff', '.tif'],
        'application/dxf': ['.dxf'],
        'image/vnd.dxf': ['.dxf'],
        'application/octet-stream': ['.dxf']  # DXF files sometimes have this MIME type
    }
    
    # Maximum file size (50MB)
    MAX_FILE_SIZE = 50 * 1024 * 1024
    
    def __init__(self):
        """Initialize MinIO client."""
        self.is_test_mode = os.getenv('TESTING', 'false').lower() == 'true'
        self.connection_available = False
        
        if not self.is_test_mode:
            self.endpoint = os.getenv('MINIO_ENDPOINT', 'localhost:9000')
            self.access_key = os.getenv('MINIO_ACCESS_KEY', 'minioadmin')
            self.secret_key = os.getenv('MINIO_SECRET_KEY', 'minioadmin123')
            self.bucket_name = os.getenv('MINIO_BUCKET_NAME', 'bom-generator-files')
            self.secure = os.getenv('MINIO_SECURE', 'false').lower() == 'true'
            
            try:
                self.client = Minio(
                    self.endpoint,
                    access_key=self.access_key,
                    secret_key=self.secret_key,
                    secure=self.secure
                )
                
                # Try to ensure bucket exists
                self._ensure_bucket_exists()
                self.connection_available = True
                logger.info(f"MinIO connection established to {self.endpoint}")
            except Exception as e:
                logger.warning(f"MinIO connection failed: {e}. Running in fallback mode.")
                self.client = None
                self.bucket_name = 'fallback-bucket'
                self._mock_storage = {}
        else:
            # Test mode - use mock storage
            self.client = None
            self.bucket_name = 'test-bucket'
            self._mock_storage = {}
    
    def _ensure_bucket_exists(self):
        """Ensure the storage bucket exists."""
        if self.is_test_mode:
            return  # Skip bucket creation in test mode
            
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"Created bucket: {self.bucket_name}")
        except S3Error as e:
            logger.error(f"Error creating bucket: {e}")
            raise HTTPException(status_code=500, detail="Storage initialization failed")
    
    def validate_file(self, file: UploadFile) -> dict:
        """
        Validate uploaded file format, size, and security constraints.
        
        Args:
            file: FastAPI UploadFile object
            
        Returns:
            dict: Validation result with status and details
            
        Raises:
            HTTPException: If validation fails
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        # Check file size
        if hasattr(file, 'size') and file.size:
            if file.size > self.MAX_FILE_SIZE:
                validation_result['valid'] = False
                validation_result['errors'].append(
                    f"File size ({file.size / 1024 / 1024:.1f}MB) exceeds maximum allowed size (50MB)"
                )
        
        # Check file format by extension
        file_extension = None
        if file.filename:
            file_extension = os.path.splitext(file.filename.lower())[1]
            validation_result['file_info']['extension'] = file_extension
            
            # Validate extension
            valid_extensions = []
            for mime_type, extensions in self.SUPPORTED_FORMATS.items():
                valid_extensions.extend(extensions)
            
            if file_extension not in valid_extensions:
                validation_result['valid'] = False
                validation_result['errors'].append(
                    f"Unsupported file format: {file_extension}. "
                    f"Supported formats: {', '.join(valid_extensions)}"
                )
        
        # Check MIME type
        if file.content_type:
            validation_result['file_info']['mime_type'] = file.content_type
            
            # For DXF files, be more lenient with MIME type checking
            if file_extension == '.dxf':
                if file.content_type not in ['application/dxf', 'image/vnd.dxf', 'application/octet-stream', 'text/plain']:
                    validation_result['warnings'].append(
                        f"Unusual MIME type for DXF file: {file.content_type}"
                    )
                elif file.content_type == 'application/octet-stream':
                    validation_result['warnings'].append(
                        f"Unusual MIME type for DXF file: {file.content_type}"
                    )
            elif file.content_type not in self.SUPPORTED_FORMATS:
                validation_result['valid'] = False
                validation_result['errors'].append(
                    f"Unsupported MIME type: {file.content_type}"
                )
        
        # Security checks - scan filename for suspicious patterns
        if file.filename:
            suspicious_patterns = ['..', '/', '\\', '<', '>', '|', ':', '*', '?', '"']
            for pattern in suspicious_patterns:
                if pattern in file.filename:
                    validation_result['valid'] = False
                    validation_result['errors'].append(
                        f"Filename contains suspicious characters: {pattern}"
                    )
                    break
        
        validation_result['file_info']['filename'] = file.filename
        validation_result['file_info']['content_type'] = file.content_type
        
        return validation_result
    
    async def store_file(self, file: UploadFile, user_id: int, design_name: str) -> dict:
        """
        Store uploaded file in MinIO/S3 storage.
        
        Args:
            file: FastAPI UploadFile object
            user_id: ID of the user uploading the file
            design_name: Name for the design project
            
        Returns:
            dict: Storage result with file path and metadata
            
        Raises:
            HTTPException: If storage fails
        """
        # Validate file first
        validation = self.validate_file(file)
        if not validation['valid']:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "File validation failed",
                    "errors": validation['errors']
                }
            )
        
        try:
            # Generate unique file path
            file_id = str(uuid.uuid4())
            file_extension = os.path.splitext(file.filename.lower())[1] if file.filename else ''
            object_name = f"designs/{user_id}/{file_id}{file_extension}"
            
            # Read file content
            file_content = await file.read()
            file_size = len(file_content)
            
            if self.is_test_mode or not self.connection_available:
                # Store in mock storage for testing or when MinIO is unavailable
                if not hasattr(self, '_mock_storage'):
                    self._mock_storage = {}
                self._mock_storage[object_name] = {
                    'content': file_content,
                    'size': file_size,
                    'content_type': file.content_type or 'application/octet-stream'
                }
                logger.info(f"File stored in mock storage: {object_name}")
            else:
                # Store in MinIO
                from io import BytesIO
                file_stream = BytesIO(file_content)
                
                self.client.put_object(
                    bucket_name=self.bucket_name,
                    object_name=object_name,
                    data=file_stream,
                    length=file_size,
                    content_type=file.content_type or 'application/octet-stream'
                )
            
            # Reset file position for potential future use
            await file.seek(0)
            
            return {
                'file_path': object_name,
                'file_size': file_size,
                'original_filename': file.filename,
                'content_type': file.content_type,
                'file_id': file_id,
                'validation_warnings': validation.get('warnings', [])
            }
            
        except S3Error as e:
            logger.error(f"MinIO storage error: {e}")
            raise HTTPException(status_code=500, detail="File storage failed")
        except Exception as e:
            logger.error(f"Unexpected error storing file: {e}")
            raise HTTPException(status_code=500, detail="File storage failed")
    
    async def retrieve_file(self, file_path: str) -> bytes:
        """
        Retrieve file from MinIO/S3 storage.
        
        Args:
            file_path: Path to the file in storage
            
        Returns:
            bytes: File content
            
        Raises:
            HTTPException: If retrieval fails
        """
        try:
            response = self.client.get_object(self.bucket_name, file_path)
            file_content = response.read()
            response.close()
            response.release_conn()
            return file_content
        except S3Error as e:
            if e.code == 'NoSuchKey':
                raise HTTPException(status_code=404, detail="File not found")
            logger.error(f"MinIO retrieval error: {e}")
            raise HTTPException(status_code=500, detail="File retrieval failed")
        except Exception as e:
            logger.error(f"Unexpected error retrieving file: {e}")
            raise HTTPException(status_code=500, detail="File retrieval failed")
    
    async def delete_file(self, file_path: str) -> bool:
        """
        Delete file from MinIO/S3 storage.
        
        Args:
            file_path: Path to the file in storage
            
        Returns:
            bool: True if deletion successful
            
        Raises:
            HTTPException: If deletion fails
        """
        try:
            self.client.remove_object(self.bucket_name, file_path)
            return True
        except S3Error as e:
            if e.code == 'NoSuchKey':
                # File doesn't exist, consider it successfully deleted
                return True
            logger.error(f"MinIO deletion error: {e}")
            raise HTTPException(status_code=500, detail="File deletion failed")
        except Exception as e:
            logger.error(f"Unexpected error deleting file: {e}")
            raise HTTPException(status_code=500, detail="File deletion failed")
    
    def get_file_url(self, file_path: str, expires: timedelta = timedelta(hours=1)) -> str:
        """
        Generate presigned URL for file access.
        
        Args:
            file_path: Path to the file in storage
            expires: URL expiration time
            
        Returns:
            str: Presigned URL
        """
        try:
            url = self.client.presigned_get_object(
                self.bucket_name,
                file_path,
                expires=expires
            )
            return url
        except S3Error as e:
            logger.error(f"Error generating presigned URL: {e}")
            raise HTTPException(status_code=500, detail="URL generation failed")


# Global instance
file_storage_service = FileStorageService()