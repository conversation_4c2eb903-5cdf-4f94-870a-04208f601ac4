"""Initial database schema

Revision ID: 001
Revises: 
Create Date: 2025-01-18 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('email', sa.String(length=100), nullable=False),
        sa.Column('password_hash', sa.String(length=255), nullable=False),
        sa.Column('is_admin', sa.<PERSON>(), nullable=True),
        sa.Column('is_active', sa.Bo<PERSON>an(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)

    # Create user_sessions table
    op.create_table('user_sessions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('session_token', sa.String(length=255), nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('session_token')
    )
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)

    # Create materials table
    op.create_table('materials',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('density', sa.DECIMAL(precision=8, scale=4), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_materials_category'), 'materials', ['category'], unique=False)
    op.create_index(op.f('ix_materials_id'), 'materials', ['id'], unique=False)
    op.create_index(op.f('ix_materials_name'), 'materials', ['name'], unique=False)

    # Create fabrication_stages table
    op.create_table('fabrication_stages',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('order_index', sa.Integer(), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_fabrication_stages_id'), 'fabrication_stages', ['id'], unique=False)

    # Create fabrication_teams table
    op.create_table('fabrication_teams',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('lead_user_id', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['lead_user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_fabrication_teams_id'), 'fabrication_teams', ['id'], unique=False)

    # Create designs table
    op.create_table('designs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('original_filename', sa.String(length=255), nullable=True),
        sa.Column('file_path', sa.String(length=500), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_designs_id'), 'designs', ['id'], unique=False)
    op.create_index(op.f('ix_designs_status'), 'designs', ['status'], unique=False)
    op.create_index(op.f('ix_designs_user_id'), 'designs', ['user_id'], unique=False)

    # Create team_members table
    op.create_table('team_members',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('team_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('role', sa.String(length=50), nullable=True),
        sa.Column('joined_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['team_id'], ['fabrication_teams.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('team_id', 'user_id', name='unique_team_member')
    )
    op.create_index(op.f('ix_team_members_id'), 'team_members', ['id'], unique=False)

    # Create analysis_results table
    op.create_table('analysis_results',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('design_id', sa.Integer(), nullable=False),
        sa.Column('analysis_data', sa.JSON(), nullable=True),
        sa.Column('confidence_score', sa.DECIMAL(precision=3, scale=2), nullable=True),
        sa.Column('processing_time', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['design_id'], ['designs.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_analysis_results_design_id'), 'analysis_results', ['design_id'], unique=False)
    op.create_index(op.f('ix_analysis_results_id'), 'analysis_results', ['id'], unique=False)

    # Create bom_items table
    op.create_table('bom_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('design_id', sa.Integer(), nullable=False),
        sa.Column('part_number', sa.String(length=100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('quantity', sa.Integer(), nullable=True),
        sa.Column('material', sa.String(length=100), nullable=True),
        sa.Column('volume', sa.DECIMAL(precision=10, scale=4), nullable=True),
        sa.Column('weight', sa.DECIMAL(precision=10, scale=4), nullable=True),
        sa.Column('unit_weight', sa.DECIMAL(precision=10, scale=4), nullable=True),
        sa.ForeignKeyConstraint(['design_id'], ['designs.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bom_items_design_id'), 'bom_items', ['design_id'], unique=False)
    op.create_index(op.f('ix_bom_items_id'), 'bom_items', ['id'], unique=False)

    # Create design_shares table
    op.create_table('design_shares',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('design_id', sa.Integer(), nullable=False),
        sa.Column('owner_id', sa.Integer(), nullable=False),
        sa.Column('shared_with_id', sa.Integer(), nullable=False),
        sa.Column('permission_level', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['design_id'], ['designs.id'], ),
        sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['shared_with_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('design_id', 'shared_with_id', name='unique_design_share')
    )
    op.create_index(op.f('ix_design_shares_design_id'), 'design_shares', ['design_id'], unique=False)
    op.create_index(op.f('ix_design_shares_id'), 'design_shares', ['id'], unique=False)
    op.create_index(op.f('ix_design_shares_shared_with_id'), 'design_shares', ['shared_with_id'], unique=False)

    # Create models_3d table
    op.create_table('models_3d',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('design_id', sa.Integer(), nullable=False),
        sa.Column('model_file_path', sa.String(length=500), nullable=True),
        sa.Column('openscad_script', sa.Text(), nullable=True),
        sa.Column('generation_time', sa.Integer(), nullable=True),
        sa.Column('file_size', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['design_id'], ['designs.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_models_3d_design_id'), 'models_3d', ['design_id'], unique=False)
    op.create_index(op.f('ix_models_3d_id'), 'models_3d', ['id'], unique=False)

    # Create part_fabrication_progress table
    op.create_table('part_fabrication_progress',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('design_id', sa.Integer(), nullable=False),
        sa.Column('part_id', sa.Integer(), nullable=False),
        sa.Column('current_stage_id', sa.Integer(), nullable=True),
        sa.Column('assigned_to', sa.Integer(), nullable=True),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('estimated_completion', sa.DateTime(timezone=True), nullable=True),
        sa.Column('actual_completion', sa.DateTime(timezone=True), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id'], ),
        sa.ForeignKeyConstraint(['current_stage_id'], ['fabrication_stages.id'], ),
        sa.ForeignKeyConstraint(['design_id'], ['designs.id'], ),
        sa.ForeignKeyConstraint(['part_id'], ['bom_items.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_part_fabrication_progress_id'), 'part_fabrication_progress', ['id'], unique=False)

    # Create fabrication_stage_history table
    op.create_table('fabrication_stage_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('part_progress_id', sa.Integer(), nullable=False),
        sa.Column('stage_id', sa.Integer(), nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('duration_minutes', sa.Integer(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['part_progress_id'], ['part_fabrication_progress.id'], ),
        sa.ForeignKeyConstraint(['stage_id'], ['fabrication_stages.id'], ),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_fabrication_stage_history_id'), 'fabrication_stage_history', ['id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_fabrication_stage_history_id'), table_name='fabrication_stage_history')
    op.drop_table('fabrication_stage_history')
    
    op.drop_index(op.f('ix_part_fabrication_progress_id'), table_name='part_fabrication_progress')
    op.drop_table('part_fabrication_progress')
    
    op.drop_index(op.f('ix_models_3d_design_id'), table_name='models_3d')
    op.drop_index(op.f('ix_models_3d_id'), table_name='models_3d')
    op.drop_table('models_3d')
    
    op.drop_index(op.f('ix_design_shares_design_id'), table_name='design_shares')
    op.drop_index(op.f('ix_design_shares_id'), table_name='design_shares')
    op.drop_index(op.f('ix_design_shares_shared_with_id'), table_name='design_shares')
    op.drop_table('design_shares')
    
    op.drop_index(op.f('ix_bom_items_design_id'), table_name='bom_items')
    op.drop_index(op.f('ix_bom_items_id'), table_name='bom_items')
    op.drop_table('bom_items')
    
    op.drop_index(op.f('ix_analysis_results_design_id'), table_name='analysis_results')
    op.drop_index(op.f('ix_analysis_results_id'), table_name='analysis_results')
    op.drop_table('analysis_results')
    
    op.drop_index(op.f('ix_team_members_id'), table_name='team_members')
    op.drop_table('team_members')
    
    op.drop_index(op.f('ix_designs_id'), table_name='designs')
    op.drop_index(op.f('ix_designs_status'), table_name='designs')
    op.drop_index(op.f('ix_designs_user_id'), table_name='designs')
    op.drop_table('designs')
    
    op.drop_index(op.f('ix_fabrication_teams_id'), table_name='fabrication_teams')
    op.drop_table('fabrication_teams')
    
    op.drop_index(op.f('ix_fabrication_stages_id'), table_name='fabrication_stages')
    op.drop_table('fabrication_stages')
    
    op.drop_index(op.f('ix_materials_category'), table_name='materials')
    op.drop_index(op.f('ix_materials_id'), table_name='materials')
    op.drop_index(op.f('ix_materials_name'), table_name='materials')
    op.drop_table('materials')
    
    op.drop_index(op.f('ix_user_sessions_id'), table_name='user_sessions')
    op.drop_table('user_sessions')
    
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')