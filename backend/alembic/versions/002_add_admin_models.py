"""Add admin models for system management

Revision ID: 002
Revises: 001
Create Date: 2025-01-18 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create user_activity_logs table
    op.create_table('user_activity_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('action', sa.String(length=100), nullable=False),
        sa.Column('resource_type', sa.String(length=50), nullable=True),
        sa.Column('resource_id', sa.Integer(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('request_data', sa.Text(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_activity_logs_id'), 'user_activity_logs', ['id'], unique=False)
    op.create_index(op.f('ix_user_activity_logs_action'), 'user_activity_logs', ['action'], unique=False)
    op.create_index(op.f('ix_user_activity_logs_resource_type'), 'user_activity_logs', ['resource_type'], unique=False)
    op.create_index(op.f('ix_user_activity_logs_timestamp'), 'user_activity_logs', ['timestamp'], unique=False)

    # Create error_logs table
    op.create_table('error_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('error_type', sa.String(length=100), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=False),
        sa.Column('stack_trace', sa.Text(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('endpoint', sa.String(length=200), nullable=True),
        sa.Column('request_data', sa.Text(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('resolved', sa.Boolean(), nullable=True),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['resolved_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_error_logs_id'), 'error_logs', ['id'], unique=False)
    op.create_index(op.f('ix_error_logs_error_type'), 'error_logs', ['error_type'], unique=False)
    op.create_index(op.f('ix_error_logs_timestamp'), 'error_logs', ['timestamp'], unique=False)
    op.create_index(op.f('ix_error_logs_resolved'), 'error_logs', ['resolved'], unique=False)

    # Create system_health_metrics table
    op.create_table('system_health_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('metric_name', sa.String(length=100), nullable=False),
        sa.Column('value', sa.String(length=50), nullable=False),
        sa.Column('unit', sa.String(length=20), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_health_metrics_id'), 'system_health_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_system_health_metrics_metric_name'), 'system_health_metrics', ['metric_name'], unique=False)
    op.create_index(op.f('ix_system_health_metrics_status'), 'system_health_metrics', ['status'], unique=False)
    op.create_index(op.f('ix_system_health_metrics_timestamp'), 'system_health_metrics', ['timestamp'], unique=False)


def downgrade() -> None:
    # Drop system_health_metrics table
    op.drop_index(op.f('ix_system_health_metrics_timestamp'), table_name='system_health_metrics')
    op.drop_index(op.f('ix_system_health_metrics_status'), table_name='system_health_metrics')
    op.drop_index(op.f('ix_system_health_metrics_metric_name'), table_name='system_health_metrics')
    op.drop_index(op.f('ix_system_health_metrics_id'), table_name='system_health_metrics')
    op.drop_table('system_health_metrics')

    # Drop error_logs table
    op.drop_index(op.f('ix_error_logs_resolved'), table_name='error_logs')
    op.drop_index(op.f('ix_error_logs_timestamp'), table_name='error_logs')
    op.drop_index(op.f('ix_error_logs_error_type'), table_name='error_logs')
    op.drop_index(op.f('ix_error_logs_id'), table_name='error_logs')
    op.drop_table('error_logs')

    # Drop user_activity_logs table
    op.drop_index(op.f('ix_user_activity_logs_timestamp'), table_name='user_activity_logs')
    op.drop_index(op.f('ix_user_activity_logs_resource_type'), table_name='user_activity_logs')
    op.drop_index(op.f('ix_user_activity_logs_action'), table_name='user_activity_logs')
    op.drop_index(op.f('ix_user_activity_logs_id'), table_name='user_activity_logs')
    op.drop_table('user_activity_logs')