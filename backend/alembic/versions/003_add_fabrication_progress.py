"""Add fabrication progress tracking

Revision ID: 003
Revises: 002
Create Date: 2025-01-18 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    # Create fabrication_stages table
    op.create_table('fabrication_stages',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('order_index', sa.Integer(), nullable=False),
        sa.Column('is_default', sa.<PERSON>(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create fabrication_teams table
    op.create_table('fabrication_teams',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('lead_user_id', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.ForeignKeyConstraint(['lead_user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create part_fabrication_progress table
    op.create_table('part_fabrication_progress',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('design_id', sa.Integer(), nullable=True),
        sa.Column('part_id', sa.Integer(), nullable=True),
        sa.Column('current_stage_id', sa.Integer(), nullable=True),
        sa.Column('assigned_to', sa.Integer(), nullable=True),
        sa.Column('assigned_team_id', sa.Integer(), nullable=True),
        sa.Column('started_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('estimated_completion', sa.TIMESTAMP(), nullable=True),
        sa.Column('actual_completion', sa.TIMESTAMP(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.ForeignKeyConstraint(['assigned_team_id'], ['fabrication_teams.id'], ),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id'], ),
        sa.ForeignKeyConstraint(['current_stage_id'], ['fabrication_stages.id'], ),
        sa.ForeignKeyConstraint(['design_id'], ['designs.id'], ),
        sa.ForeignKeyConstraint(['part_id'], ['bom_items.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create fabrication_stage_history table
    op.create_table('fabrication_stage_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('part_progress_id', sa.Integer(), nullable=True),
        sa.Column('stage_id', sa.Integer(), nullable=True),
        sa.Column('started_at', sa.TIMESTAMP(), nullable=False),
        sa.Column('completed_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('duration_minutes', sa.Integer(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.ForeignKeyConstraint(['part_progress_id'], ['part_fabrication_progress.id'], ),
        sa.ForeignKeyConstraint(['stage_id'], ['fabrication_stages.id'], ),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create team_members table
    op.create_table('team_members',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('team_id', sa.Integer(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('role', sa.String(length=50), nullable=True),
        sa.Column('joined_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.ForeignKeyConstraint(['team_id'], ['fabrication_teams.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('team_id', 'user_id')
    )
    
    # Insert default fabrication stages
    op.execute("""
        INSERT INTO fabrication_stages (name, description, order_index, is_default) VALUES
        ('Not Started', 'Part has not begun fabrication', 1, TRUE),
        ('Material Prep', 'Material cutting and preparation', 2, TRUE),
        ('Machining', 'CNC machining or manual machining operations', 3, TRUE),
        ('Welding/Assembly', 'Welding and assembly operations', 4, TRUE),
        ('Quality Check', 'Quality inspection and testing', 5, TRUE),
        ('Finishing', 'Surface treatment, painting, or coating', 6, TRUE),
        ('Completed', 'Part fabrication completed', 7, TRUE);
    """)


def downgrade():
    op.drop_table('team_members')
    op.drop_table('fabrication_stage_history')
    op.drop_table('part_fabrication_progress')
    op.drop_table('fabrication_teams')
    op.drop_table('fabrication_stages')