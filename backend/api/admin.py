"""
Admin panel API endpoints for system management.
"""
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from database.connection import get_database
from auth.dependencies import get_current_admin_user
from services.admin_service import AdminService
from models.user import User
from pydantic import BaseModel


class SystemStats(BaseModel):
    """System statistics model."""
    total_users: int
    active_users: int
    admin_users: int
    total_designs: int
    designs_this_month: int
    total_analyses: int
    successful_analyses: int
    failed_analyses: int
    total_storage_mb: float
    avg_processing_time_seconds: float


class UserActivityLog(BaseModel):
    """User activity log model."""
    id: int
    user_id: int
    username: str
    action: str
    resource_type: Optional[str]
    resource_id: Optional[int]
    ip_address: Optional[str]
    user_agent: Optional[str]
    timestamp: datetime


class SystemHealthMetric(BaseModel):
    """System health metric model."""
    metric_name: str
    value: float
    unit: str
    status: str  # 'healthy', 'warning', 'critical'
    last_updated: datetime


class ErrorLogEntry(BaseModel):
    """Error log entry model."""
    id: int
    error_type: str
    error_message: str
    stack_trace: Optional[str]
    user_id: Optional[int]
    username: Optional[str]
    endpoint: Optional[str]
    request_data: Optional[str]
    timestamp: datetime
    resolved: bool


class MaterialCreate(BaseModel):
    """Material creation model."""
    name: str
    density: float
    category: str
    description: Optional[str] = None


class MaterialUpdate(BaseModel):
    """Material update model."""
    name: Optional[str] = None
    density: Optional[float] = None
    category: Optional[str] = None
    description: Optional[str] = None


class MaterialResponse(BaseModel):
    """Material response model."""
    id: int
    name: str
    density: float
    category: str
    description: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


router = APIRouter(prefix="/api/admin", tags=["admin"])


def get_admin_service(db: Session = Depends(get_database)) -> AdminService:
    """Get admin service instance."""
    return AdminService(db)


@router.get("/dashboard/stats", response_model=SystemStats)
async def get_system_statistics(
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Get system statistics for admin dashboard."""
    return admin_service.get_system_statistics()


@router.get("/users/activity", response_model=List[UserActivityLog])
async def get_user_activity_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    user_id: Optional[int] = Query(None),
    action: Optional[str] = Query(None),
    days: int = Query(7, ge=1, le=90),
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Get user activity logs with filtering."""
    return admin_service.get_user_activity_logs(
        skip=skip, 
        limit=limit, 
        user_id=user_id, 
        action=action, 
        days=days
    )


@router.get("/system/health", response_model=List[SystemHealthMetric])
async def get_system_health_metrics(
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Get system health metrics."""
    return admin_service.get_system_health_metrics()


@router.get("/system/errors", response_model=List[ErrorLogEntry])
async def get_error_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    error_type: Optional[str] = Query(None),
    resolved: Optional[bool] = Query(None),
    days: int = Query(7, ge=1, le=90),
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Get system error logs with filtering."""
    return admin_service.get_error_logs(
        skip=skip,
        limit=limit,
        error_type=error_type,
        resolved=resolved,
        days=days
    )


@router.put("/system/errors/{error_id}/resolve")
async def resolve_error_log(
    error_id: int,
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Mark error log entry as resolved."""
    success = admin_service.resolve_error_log(error_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Error log entry not found"
        )
    return {"message": "Error log entry marked as resolved"}


@router.get("/materials", response_model=List[MaterialResponse])
async def get_materials(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Get materials with filtering and search."""
    return admin_service.get_materials(
        skip=skip,
        limit=limit,
        category=category,
        search=search
    )


@router.post("/materials", response_model=MaterialResponse, status_code=status.HTTP_201_CREATED)
async def create_material(
    material_data: MaterialCreate,
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Create a new material."""
    try:
        material = admin_service.create_material(
            name=material_data.name,
            density=material_data.density,
            category=material_data.category,
            description=material_data.description
        )
        return material
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.put("/materials/{material_id}", response_model=MaterialResponse)
async def update_material(
    material_id: int,
    material_data: MaterialUpdate,
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Update an existing material."""
    try:
        material = admin_service.update_material(
            material_id=material_id,
            name=material_data.name,
            density=material_data.density,
            category=material_data.category,
            description=material_data.description
        )
        if not material:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Material not found"
            )
        return material
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete("/materials/{material_id}")
async def delete_material(
    material_id: int,
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Delete a material."""
    success = admin_service.delete_material(material_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Material not found"
        )
    return {"message": "Material deleted successfully"}


@router.post("/system/cleanup")
async def cleanup_system_data(
    current_admin: User = Depends(get_current_admin_user),
    admin_service: AdminService = Depends(get_admin_service)
):
    """Cleanup expired sessions and old logs."""
    cleanup_results = admin_service.cleanup_system_data()
    return {
        "message": "System cleanup completed",
        "results": cleanup_results
    }