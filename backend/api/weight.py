"""
Weight Calculation API

Provides endpoints for weight calculations, material management,
and weight-related operations for the BOM system.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import logging

from services.weight_service import WeightCalculationService, AssemblyWeightSummary
from auth.dependencies import get_current_user
from models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/weight", tags=["weight"])


# Request/Response Models
class WeightCalculationRequest(BaseModel):
    design_id: int = Field(..., description="Design ID to calculate weights for")


class MaterialSelectionRequest(BaseModel):
    design_id: int = Field(..., description="Design ID")
    part_number: str = Field(..., description="Part number to update")
    material_name: str = Field(..., description="New material name")


class CustomMaterialRequest(BaseModel):
    name: str = Field(..., description="Material name")
    density: float = Field(..., gt=0, description="Density in kg/m³")
    category: str = Field(..., description="Material category")
    description: Optional[str] = Field("", description="Material description")


class WeightCalculationResponse(BaseModel):
    part_number: str
    material_name: str
    material_density: float
    volume_cm3: float
    unit_weight_kg: float
    quantity: int
    total_weight_kg: float
    confidence: float
    calculation_notes: List[str]


class AssemblyWeightResponse(BaseModel):
    total_weight_kg: float
    part_count: int
    unique_materials: List[str]
    weight_breakdown: List[WeightCalculationResponse]
    heaviest_part: Optional[WeightCalculationResponse]
    lightest_part: Optional[WeightCalculationResponse]
    material_distribution: Dict[str, float]
    confidence_score: float
    missing_data_flags: List[str]


class MaterialOption(BaseModel):
    id: int
    name: str
    density: float
    category: str
    description: Optional[str]


class MaterialOptionsResponse(BaseModel):
    materials: List[MaterialOption]
    categories: List[str]


# Initialize service
weight_service = WeightCalculationService()


@router.post("/calculate", response_model=AssemblyWeightResponse)
async def calculate_assembly_weight(
    request: WeightCalculationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Calculate weights for all parts in a design assembly
    
    This endpoint performs volume calculations from 2D views and dimensional data,
    matches materials from the database, and calculates individual and total weights.
    """
    try:
        logger.info(f"Calculating weights for design {request.design_id} by user {current_user.id}")
        
        # Calculate weights
        weight_summary = weight_service.calculate_weights_for_design(request.design_id)
        
        # Convert to response format
        weight_breakdown = [
            WeightCalculationResponse(
                part_number=result.part_number,
                material_name=result.material_name,
                material_density=result.material_density,
                volume_cm3=result.volume_cm3,
                unit_weight_kg=result.unit_weight_kg,
                quantity=result.quantity,
                total_weight_kg=result.total_weight_kg,
                confidence=result.confidence,
                calculation_notes=result.calculation_notes
            )
            for result in weight_summary.weight_breakdown
        ]
        
        heaviest_part = None
        if weight_summary.heaviest_part:
            heaviest_part = WeightCalculationResponse(
                part_number=weight_summary.heaviest_part.part_number,
                material_name=weight_summary.heaviest_part.material_name,
                material_density=weight_summary.heaviest_part.material_density,
                volume_cm3=weight_summary.heaviest_part.volume_cm3,
                unit_weight_kg=weight_summary.heaviest_part.unit_weight_kg,
                quantity=weight_summary.heaviest_part.quantity,
                total_weight_kg=weight_summary.heaviest_part.total_weight_kg,
                confidence=weight_summary.heaviest_part.confidence,
                calculation_notes=weight_summary.heaviest_part.calculation_notes
            )
        
        lightest_part = None
        if weight_summary.lightest_part:
            lightest_part = WeightCalculationResponse(
                part_number=weight_summary.lightest_part.part_number,
                material_name=weight_summary.lightest_part.material_name,
                material_density=weight_summary.lightest_part.material_density,
                volume_cm3=weight_summary.lightest_part.volume_cm3,
                unit_weight_kg=weight_summary.lightest_part.unit_weight_kg,
                quantity=weight_summary.lightest_part.quantity,
                total_weight_kg=weight_summary.lightest_part.total_weight_kg,
                confidence=weight_summary.lightest_part.confidence,
                calculation_notes=weight_summary.lightest_part.calculation_notes
            )
        
        response = AssemblyWeightResponse(
            total_weight_kg=weight_summary.total_weight_kg,
            part_count=weight_summary.part_count,
            unique_materials=weight_summary.unique_materials,
            weight_breakdown=weight_breakdown,
            heaviest_part=heaviest_part,
            lightest_part=lightest_part,
            material_distribution=weight_summary.material_distribution,
            confidence_score=weight_summary.confidence_score,
            missing_data_flags=weight_summary.missing_data_flags
        )
        
        logger.info(f"Weight calculation completed for design {request.design_id}: {weight_summary.total_weight_kg:.2f} kg")
        
        return response
        
    except ValueError as e:
        logger.error(f"Weight calculation validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Weight calculation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Weight calculation failed"
        )


@router.get("/materials", response_model=MaterialOptionsResponse)
async def get_material_options(
    category: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    Get available material options for selection
    
    Returns a list of materials with their properties, optionally filtered by category.
    Used for material selection interfaces when materials are unknown or need to be changed.
    """
    try:
        logger.info(f"Getting material options for user {current_user.id}, category: {category}")
        
        materials = weight_service.get_material_options(category)
        
        # Extract unique categories
        categories = list(set(material['category'] for material in materials if material['category']))
        categories.sort()
        
        material_options = [
            MaterialOption(
                id=material['id'],
                name=material['name'],
                density=material['density'],
                category=material['category'],
                description=material['description']
            )
            for material in materials
        ]
        
        return MaterialOptionsResponse(
            materials=material_options,
            categories=categories
        )
        
    except Exception as e:
        logger.error(f"Failed to get material options: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve material options"
        )


@router.put("/material", response_model=WeightCalculationResponse)
async def update_part_material(
    request: MaterialSelectionRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Update material for a specific part and recalculate weight
    
    This endpoint allows users to change the material assignment for a part
    when the automatic material detection is incorrect or when materials are unknown.
    """
    try:
        logger.info(f"Updating material for part {request.part_number} in design {request.design_id}")
        
        # Update material and recalculate weight
        weight_result = weight_service.update_part_material(
            request.design_id,
            request.part_number,
            request.material_name
        )
        
        response = WeightCalculationResponse(
            part_number=weight_result.part_number,
            material_name=weight_result.material_name,
            material_density=weight_result.material_density,
            volume_cm3=weight_result.volume_cm3,
            unit_weight_kg=weight_result.unit_weight_kg,
            quantity=weight_result.quantity,
            total_weight_kg=weight_result.total_weight_kg,
            confidence=weight_result.confidence,
            calculation_notes=weight_result.calculation_notes
        )
        
        logger.info(f"Material updated for part {request.part_number}: {weight_result.material_name}")
        
        return response
        
    except ValueError as e:
        logger.error(f"Material update validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Material update failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update part material"
        )


@router.post("/materials/custom", response_model=MaterialOption)
async def add_custom_material(
    request: CustomMaterialRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Add a custom material with specified properties
    
    This endpoint allows users to add custom materials when the standard
    material database doesn't contain the required material properties.
    """
    try:
        logger.info(f"Adding custom material '{request.name}' by user {current_user.id}")
        
        # Add custom material
        material_data = weight_service.add_custom_material(
            request.name,
            request.density,
            request.category,
            request.description
        )
        
        response = MaterialOption(
            id=material_data['id'],
            name=material_data['name'],
            density=material_data['density'],
            category=material_data['category'],
            description=material_data['description']
        )
        
        logger.info(f"Custom material '{request.name}' added successfully")
        
        return response
        
    except ValueError as e:
        logger.error(f"Custom material validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Custom material creation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create custom material"
        )


@router.get("/design/{design_id}/summary")
async def get_weight_summary(
    design_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    Get a quick weight summary for a design
    
    Returns basic weight information without performing full recalculation.
    Uses previously calculated values from the database.
    """
    try:
        logger.info(f"Getting weight summary for design {design_id}")
        
        # This would typically get cached/stored weight data
        # For now, we'll perform a full calculation
        weight_summary = weight_service.calculate_weights_for_design(design_id)
        
        summary = {
            "design_id": design_id,
            "total_weight_kg": weight_summary.total_weight_kg,
            "part_count": weight_summary.part_count,
            "unique_materials": len(weight_summary.unique_materials),
            "confidence_score": weight_summary.confidence_score,
            "has_missing_data": len(weight_summary.missing_data_flags) > 0,
            "material_distribution": weight_summary.material_distribution
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"Failed to get weight summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve weight summary"
        )


@router.get("/design/{design_id}/validation")
async def validate_weight_calculations(
    design_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    Validate weight calculations and identify potential issues
    
    Returns validation results highlighting parts that may need manual review
    or have low confidence calculations.
    """
    try:
        logger.info(f"Validating weight calculations for design {design_id}")
        
        weight_summary = weight_service.calculate_weights_for_design(design_id)
        
        # Analyze for validation issues
        validation_issues = []
        
        for result in weight_summary.weight_breakdown:
            if result.confidence < 50:
                validation_issues.append({
                    "part_number": result.part_number,
                    "issue_type": "low_confidence",
                    "severity": "warning",
                    "message": f"Low confidence calculation ({result.confidence:.1f}%)",
                    "suggestions": result.calculation_notes
                })
            
            if result.volume_cm3 < 0.1:
                validation_issues.append({
                    "part_number": result.part_number,
                    "issue_type": "small_volume",
                    "severity": "warning",
                    "message": f"Very small calculated volume ({result.volume_cm3:.2f} cm³)",
                    "suggestions": ["Verify part dimensions", "Check if part is a fastener or small component"]
                })
            
            if result.total_weight_kg > 100:
                validation_issues.append({
                    "part_number": result.part_number,
                    "issue_type": "heavy_part",
                    "severity": "info",
                    "message": f"Heavy part detected ({result.total_weight_kg:.1f} kg)",
                    "suggestions": ["Verify material and dimensions", "Consider structural requirements"]
                })
        
        validation_result = {
            "design_id": design_id,
            "overall_confidence": weight_summary.confidence_score,
            "total_issues": len(validation_issues),
            "issues": validation_issues,
            "missing_data_flags": weight_summary.missing_data_flags,
            "recommendations": []
        }
        
        # Add general recommendations
        if weight_summary.confidence_score < 70:
            validation_result["recommendations"].append(
                "Overall confidence is low - consider reviewing part dimensions and materials"
            )
        
        if len(weight_summary.missing_data_flags) > 0:
            validation_result["recommendations"].append(
                "Some parts have missing data - manual verification recommended"
            )
        
        return validation_result
        
    except Exception as e:
        logger.error(f"Weight validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Weight validation failed"
        )