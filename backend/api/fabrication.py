"""
Fabrication progress tracking API endpoints
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from database.connection import get_database
from auth.dependencies import get_current_user
from services.fabrication_service import FabricationService
from models.user import User


router = APIRouter(prefix="/api/fabrication", tags=["fabrication"])


# Pydantic models for request/response
class StageCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    order_index: int = Field(..., ge=1)


class StageUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    order_index: Optional[int] = Field(None, ge=1)


class TeamCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    lead_user_id: int


class TeamMemberAdd(BaseModel):
    user_id: int
    role: Optional[str] = Field(None, max_length=50)


class PartStatusUpdate(BaseModel):
    stage_id: int
    notes: Optional[str] = None
    estimated_completion: Optional[datetime] = None


class PartAssignment(BaseModel):
    assigned_to: Optional[int] = None
    assigned_team_id: Optional[int] = None


# Stage Management Endpoints
@router.get("/stages")
async def get_fabrication_stages(
    include_custom: bool = True,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Get all fabrication stages"""
    service = FabricationService(db)
    return service.get_fabrication_stages(include_custom=include_custom)


@router.post("/stages")
async def create_fabrication_stage(
    stage_data: StageCreate,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Create a new custom fabrication stage"""
    service = FabricationService(db)
    try:
        return service.create_fabrication_stage(
            name=stage_data.name,
            description=stage_data.description,
            order_index=stage_data.order_index,
            created_by=current_user.id
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/stages/{stage_id}")
async def update_fabrication_stage(
    stage_id: int,
    stage_data: StageUpdate,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Update a fabrication stage"""
    service = FabricationService(db)
    try:
        return service.update_fabrication_stage(
            stage_id=stage_id,
            name=stage_data.name,
            description=stage_data.description,
            order_index=stage_data.order_index
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/stages/{stage_id}")
async def delete_fabrication_stage(
    stage_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Delete a custom fabrication stage"""
    service = FabricationService(db)
    success = service.delete_fabrication_stage(stage_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Stage not found or cannot be deleted"
        )
    return {"message": "Stage deleted successfully"}


# Team Management Endpoints
@router.get("/teams")
async def get_fabrication_teams(
    active_only: bool = True,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Get all fabrication teams"""
    service = FabricationService(db)
    return service.get_fabrication_teams(active_only=active_only)


@router.post("/teams")
async def create_fabrication_team(
    team_data: TeamCreate,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Create a new fabrication team"""
    service = FabricationService(db)
    try:
        return service.create_fabrication_team(
            name=team_data.name,
            description=team_data.description,
            lead_user_id=team_data.lead_user_id
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/teams/{team_id}/members")
async def add_team_member(
    team_id: int,
    member_data: TeamMemberAdd,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Add a member to a fabrication team"""
    service = FabricationService(db)
    success = service.add_team_member(
        team_id=team_id,
        user_id=member_data.user_id,
        role=member_data.role
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Member already exists in team or invalid data"
        )
    return {"message": "Member added successfully"}


@router.delete("/teams/{team_id}/members/{user_id}")
async def remove_team_member(
    team_id: int,
    user_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Remove a member from a fabrication team"""
    service = FabricationService(db)
    success = service.remove_team_member(team_id=team_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Member not found in team"
        )
    return {"message": "Member removed successfully"}


# Progress Tracking Endpoints
@router.get("/{design_id}/progress")
async def get_design_progress(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Get fabrication progress for a design"""
    service = FabricationService(db)
    try:
        return service.get_design_progress(design_id)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.put("/{design_id}/parts/{part_id}/status")
async def update_part_status(
    design_id: int,
    part_id: int,
    status_data: PartStatusUpdate,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Update the fabrication status of a part"""
    service = FabricationService(db)
    try:
        return service.update_part_status(
            design_id=design_id,
            part_id=part_id,
            stage_id=status_data.stage_id,
            updated_by=current_user.id,
            notes=status_data.notes,
            estimated_completion=status_data.estimated_completion
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{design_id}/parts/{part_id}/assignment")
async def assign_part(
    design_id: int,
    part_id: int,
    assignment_data: PartAssignment,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Assign a part to a user or team"""
    service = FabricationService(db)
    try:
        return service.assign_part(
            design_id=design_id,
            part_id=part_id,
            assigned_to=assignment_data.assigned_to,
            assigned_team_id=assignment_data.assigned_team_id
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{design_id}/timeline")
async def get_timeline_data(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Get timeline data for Gantt chart visualization"""
    service = FabricationService(db)
    return service.get_timeline_data(design_id)


@router.get("/{design_id}/reports")
async def get_progress_reports(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Generate progress reports with charts and statistics"""
    service = FabricationService(db)
    return service.get_progress_reports(design_id)


@router.get("/reports")
async def get_all_progress_reports(
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Generate progress reports for all designs"""
    service = FabricationService(db)
    return service.get_progress_reports()


@router.get("/dashboard")
async def get_dashboard_overview(
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_user)
):
    """Get overview data for fabrication dashboard"""
    service = FabricationService(db)
    return service.get_dashboard_overview()