"""
Design sharing and collaboration API endpoints.
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr

from database.connection import get_database
from auth.dependencies import get_current_user
from models.user import User
from services.sharing_service import SharingService


router = APIRouter(prefix="/api/sharing", tags=["sharing"])


# Pydantic models for request/response
class ShareRequest(BaseModel):
    """Request model for sharing a design."""
    design_id: int
    shared_with_email: EmailStr
    permission_level: str = "view"


class UpdatePermissionRequest(BaseModel):
    """Request model for updating share permissions."""
    permission_level: str


class ShareResponse(BaseModel):
    """Response model for sharing operations."""
    share_id: int
    design_id: int
    shared_with_user_id: int
    shared_with_username: str
    shared_with_email: str
    permission_level: str
    shared_at: str


class SharedDesignResponse(BaseModel):
    """Response model for designs shared with user."""
    share_id: int
    design_id: int
    design_name: str
    owner_username: str
    owner_email: str
    permission_level: str
    shared_at: str
    design_status: str
    design_created_at: str
    design_updated_at: str


class UserSearchResponse(BaseModel):
    """Response model for user search."""
    user_id: int
    username: str
    email: str


@router.post("/grant", response_model=Dict[str, Any])
async def grant_access(
    request: ShareRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Grant access to a design for another user."""
    sharing_service = SharingService(db)
    
    try:
        share = sharing_service.grant_access(
            design_id=request.design_id,
            owner_id=current_user.id,
            shared_with_email=request.shared_with_email,
            permission_level=request.permission_level
        )
        
        # Get user details for response
        shared_with_user = db.query(User).filter(User.id == share.shared_with_id).first()
        
        return {
            "success": True,
            "message": f"Design shared with {shared_with_user.username}",
            "share": {
                "share_id": share.id,
                "design_id": share.design_id,
                "shared_with_user_id": share.shared_with_id,
                "shared_with_username": shared_with_user.username,
                "shared_with_email": shared_with_user.email,
                "permission_level": share.permission_level,
                "shared_at": share.created_at.isoformat()
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to share design: {str(e)}"
        )


@router.delete("/revoke/{design_id}/{shared_with_id}")
async def revoke_access(
    design_id: int,
    shared_with_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Revoke access to a design from a user."""
    sharing_service = SharingService(db)
    
    try:
        success = sharing_service.revoke_access(
            design_id=design_id,
            owner_id=current_user.id,
            shared_with_id=shared_with_id
        )
        
        return {
            "success": success,
            "message": "Access revoked successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to revoke access: {str(e)}"
        )


@router.get("/design/{design_id}/shares")
async def get_design_shares(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Get all users who have access to a design."""
    sharing_service = SharingService(db)
    
    try:
        shares = sharing_service.get_design_shares(
            design_id=design_id,
            user_id=current_user.id
        )
        
        return {
            "design_id": design_id,
            "shares": shares
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get design shares: {str(e)}"
        )


@router.get("/shared-with-me")
async def get_shared_with_me(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Get all designs shared with the current user."""
    sharing_service = SharingService(db)
    
    try:
        shared_designs = sharing_service.get_shared_with_me(current_user.id)
        
        return {
            "shared_designs": shared_designs
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get shared designs: {str(e)}"
        )


@router.put("/design/{design_id}/user/{shared_with_id}/permission")
async def update_permission(
    design_id: int,
    shared_with_id: int,
    request: UpdatePermissionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Update permission level for an existing share."""
    sharing_service = SharingService(db)
    
    try:
        share = sharing_service.update_permission(
            design_id=design_id,
            owner_id=current_user.id,
            shared_with_id=shared_with_id,
            permission_level=request.permission_level
        )
        
        # Get user details for response
        shared_with_user = db.query(User).filter(User.id == share.shared_with_id).first()
        
        return {
            "success": True,
            "message": "Permission updated successfully",
            "share": {
                "share_id": share.id,
                "design_id": share.design_id,
                "shared_with_user_id": share.shared_with_id,
                "shared_with_username": shared_with_user.username,
                "shared_with_email": shared_with_user.email,
                "permission_level": share.permission_level,
                "shared_at": share.created_at.isoformat()
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update permission: {str(e)}"
        )


@router.get("/design/{design_id}/history")
async def get_sharing_history(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Get sharing history for a design (audit log)."""
    sharing_service = SharingService(db)
    
    try:
        history = sharing_service.get_sharing_history(
            design_id=design_id,
            user_id=current_user.id
        )
        
        return {
            "design_id": design_id,
            "history": history
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sharing history: {str(e)}"
        )


@router.get("/users/search")
async def search_users(
    q: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Search for users to share with."""
    sharing_service = SharingService(db)
    
    try:
        users = sharing_service.search_users(
            query=q,
            current_user_id=current_user.id
        )
        
        return {
            "query": q,
            "users": users
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search users: {str(e)}"
        )


@router.get("/design/{design_id}/access")
async def check_design_access(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Check current user's access level to a design."""
    sharing_service = SharingService(db)
    
    try:
        access_level = sharing_service.check_access(
            design_id=design_id,
            user_id=current_user.id
        )
        
        return {
            "design_id": design_id,
            "access_level": access_level,
            "has_access": access_level is not None
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check access: {str(e)}"
        )