"""
Authentication API endpoints.
"""
from typing import List
from fastapi import APIR<PERSON>er, Depends, HTTPException, status, Header
from sqlalchemy.orm import Session

from database.connection import get_database
from services.auth_service import AuthService
from auth.dependencies import get_auth_service, get_current_active_user, get_current_admin_user
from pydantic import BaseModel
from auth.security import (
    UserCreate, UserLogin, UserResponse, Token, PasswordReset, 
    PasswordResetConfirm, UserUpdate, ValidationError, AuthenticationError
)
from models.user import User

router = APIRouter(prefix="/api/auth", tags=["authentication"])


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Register a new user account."""
    try:
        user = auth_service.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password
        )
        return UserResponse.model_validate(user)
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/login", response_model=Token)
async def login_user(
    user_data: UserLogin,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Authenticate user and return access tokens."""
    user = auth_service.authenticate_user(user_data.username, user_data.password)
    if not user:
        raise AuthenticationError("Incorrect username or password")
    
    tokens = auth_service.create_user_session(user)
    return Token(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"]
    )


class RefreshTokenRequest(BaseModel):
    """Refresh token request model."""
    refresh_token: str


@router.post("/refresh", response_model=dict)
async def refresh_token(
    request: RefreshTokenRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Refresh access token using refresh token."""
    try:
        tokens = auth_service.refresh_access_token(request.refresh_token)
        return tokens
    except AuthenticationError as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))


@router.post("/logout")
async def logout_user(
    session_token: str = Header(None, alias="X-Session-Token"),
    auth_service: AuthService = Depends(get_auth_service),
    current_user: User = Depends(get_current_active_user)
):
    """Logout user by invalidating session."""
    if session_token:
        auth_service.logout_user(session_token)
    
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user profile information."""
    return UserResponse.model_validate(current_user)


@router.put("/me", response_model=UserResponse)
async def update_current_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Update current user profile information."""
    try:
        updated_user = auth_service.update_user_profile(
            user_id=current_user.id,
            email=user_update.email,
            current_password=user_update.current_password,
            new_password=user_update.new_password
        )
        return UserResponse.model_validate(updated_user)
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/password-reset")
async def request_password_reset(
    reset_data: PasswordReset,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Request password reset token."""
    try:
        token = auth_service.create_password_reset_token(reset_data.email)
        # In a real application, you would send this token via email
        # For development, we'll return it in the response
        return {
            "message": "Password reset token generated",
            "reset_token": token  # Remove this in production
        }
    except ValidationError as e:
        # Always return success to prevent email enumeration
        return {"message": "If the email exists, a reset link will be sent"}


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Confirm password reset with token."""
    try:
        success = auth_service.reset_password(reset_data.token, reset_data.new_password)
        if success:
            return {"message": "Password reset successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to reset password"
            )
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# Admin-only endpoints
@router.get("/users", response_model=List[UserResponse])
async def get_all_users(
    skip: int = 0,
    limit: int = 100,
    current_admin: User = Depends(get_current_admin_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Get all users (admin only)."""
    users = auth_service.get_all_users(skip=skip, limit=limit)
    return [UserResponse.model_validate(user) for user in users]


@router.post("/users", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user_admin(
    user_data: UserCreate,
    is_admin: bool = False,
    current_admin: User = Depends(get_current_admin_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Create a new user account (admin only)."""
    try:
        user = auth_service.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
            is_admin=is_admin
        )
        return UserResponse.model_validate(user)
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.put("/users/{user_id}/status")
async def update_user_status(
    user_id: int,
    is_active: bool,
    current_admin: User = Depends(get_current_admin_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Update user active status (admin only)."""
    try:
        user = auth_service.update_user_status(user_id, is_active)
        return UserResponse.model_validate(user)
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.put("/users/{user_id}/admin")
async def update_user_admin_status(
    user_id: int,
    is_admin: bool,
    current_admin: User = Depends(get_current_admin_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Update user admin status (admin only)."""
    try:
        user = auth_service.update_user_admin_status(user_id, is_admin)
        return UserResponse.model_validate(user)
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    current_admin: User = Depends(get_current_admin_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Delete user account (admin only)."""
    # Prevent admin from deleting themselves
    if user_id == current_admin.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    try:
        success = auth_service.delete_user(user_id)
        if success:
            return {"message": "User deleted successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))