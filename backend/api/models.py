"""
3D Models API endpoints.

This module provides REST API endpoints for 3D model generation,
retrieval, and export functionality.
"""
import os
from typing import Optional
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from database.connection import get_database as get_db
from auth.dependencies import get_current_user
from models.user import User
from models.design import Design, Model3D
from services.model_3d_service import model_3d_service
from celery_tasks import generate_3d_model_task

router = APIRouter(prefix="/api/models", tags=["3D Models"])


class Model3DResponse(BaseModel):
    """Response model for 3D model data."""
    id: int
    design_id: int
    model_file_path: Optional[str]
    generation_time: Optional[int]
    file_size: Optional[int]
    created_at: str
    
    class Config:
        from_attributes = True


class ModelGenerationRequest(BaseModel):
    """Request model for 3D model generation."""
    design_id: int


class ModelGenerationResponse(BaseModel):
    """Response model for 3D model generation request."""
    message: str
    task_id: Optional[str] = None
    model_id: Optional[int] = None


class ModelValidationResponse(BaseModel):
    """Response model for model validation."""
    is_valid: bool
    file_exists: bool
    file_size: int
    errors: list[str]


@router.post("/generate", response_model=ModelGenerationResponse)
async def generate_3d_model(
    request: ModelGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Generate 3D model from design analysis data.
    
    This endpoint initiates 3D model generation for a design.
    The generation is performed asynchronously in the background.
    """
    # Verify design exists and user has access
    design = db.query(Design).filter(
        Design.id == request.design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(
            status_code=404,
            detail="Design not found or access denied"
        )
    
    # Check if model already exists
    existing_model = model_3d_service.get_model_by_design_id(request.design_id, db)
    if existing_model:
        return ModelGenerationResponse(
            message="3D model already exists for this design",
            model_id=existing_model.id
        )
    
    try:
        # For now, generate synchronously (can be moved to Celery later)
        model_3d = model_3d_service.generate_3d_model(request.design_id, db)
        
        if model_3d:
            return ModelGenerationResponse(
                message="3D model generated successfully",
                model_id=model_3d.id
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to generate 3D model"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating 3D model: {str(e)}"
        )


@router.get("/{model_id}", response_model=Model3DResponse)
async def get_3d_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get 3D model information by ID."""
    # Get model and verify access
    model = db.query(Model3D).join(Design).filter(
        Model3D.id == model_id,
        Design.user_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=404,
            detail="3D model not found or access denied"
        )
    
    return Model3DResponse(
        id=model.id,
        design_id=model.design_id,
        model_file_path=model.model_file_path,
        generation_time=model.generation_time,
        file_size=model.file_size,
        created_at=model.created_at.isoformat()
    )


@router.get("/design/{design_id}", response_model=Optional[Model3DResponse])
async def get_model_by_design(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get 3D model for a specific design."""
    # Verify design access
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(
            status_code=404,
            detail="Design not found or access denied"
        )
    
    model = model_3d_service.get_model_by_design_id(design_id, db)
    
    if not model:
        return None
    
    return Model3DResponse(
        id=model.id,
        design_id=model.design_id,
        model_file_path=model.model_file_path,
        generation_time=model.generation_time,
        file_size=model.file_size,
        created_at=model.created_at.isoformat()
    )


@router.get("/{model_id}/download/stl")
async def download_stl_file(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Download STL file for a 3D model."""
    # Get model and verify access
    model = db.query(Model3D).join(Design).filter(
        Model3D.id == model_id,
        Design.user_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=404,
            detail="3D model not found or access denied"
        )
    
    file_path = model_3d_service.get_model_file_path(model_id, db)
    if not file_path:
        raise HTTPException(
            status_code=404,
            detail="Model file not found"
        )
    
    # Get filename for download
    filename = f"model_{model.design_id}.stl"
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="application/octet-stream"
    )


@router.get("/{model_id}/script")
async def get_openscad_script(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get OpenSCAD script for a 3D model."""
    # Get model and verify access
    model = db.query(Model3D).join(Design).filter(
        Model3D.id == model_id,
        Design.user_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=404,
            detail="3D model not found or access denied"
        )
    
    if not model.openscad_script:
        raise HTTPException(
            status_code=404,
            detail="OpenSCAD script not available"
        )
    
    return {
        "script": model.openscad_script,
        "design_id": model.design_id,
        "model_id": model.id
    }


@router.get("/{model_id}/validate", response_model=ModelValidationResponse)
async def validate_3d_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Validate 3D model file integrity."""
    # Get model and verify access
    model = db.query(Model3D).join(Design).filter(
        Model3D.id == model_id,
        Design.user_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=404,
            detail="3D model not found or access denied"
        )
    
    if not model.model_file_path:
        raise HTTPException(
            status_code=404,
            detail="Model file path not available"
        )
    
    validation_result = model_3d_service.validate_model_file(model.model_file_path)
    
    return ModelValidationResponse(**validation_result)


@router.delete("/{model_id}")
async def delete_3d_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a 3D model and its associated files."""
    # Get model and verify access
    model = db.query(Model3D).join(Design).filter(
        Model3D.id == model_id,
        Design.user_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=404,
            detail="3D model not found or access denied"
        )
    
    success = model_3d_service.delete_model(model_id, db)
    
    if success:
        return {"message": "3D model deleted successfully"}
    else:
        raise HTTPException(
            status_code=500,
            detail="Failed to delete 3D model"
        )


@router.post("/regenerate/{design_id}", response_model=ModelGenerationResponse)
async def regenerate_3d_model(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Regenerate 3D model for a design (replaces existing model)."""
    # Verify design exists and user has access
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(
            status_code=404,
            detail="Design not found or access denied"
        )
    
    try:
        # Delete existing model if it exists
        existing_model = model_3d_service.get_model_by_design_id(design_id, db)
        if existing_model:
            model_3d_service.delete_model(existing_model.id, db)
        
        # Generate new model
        model_3d = model_3d_service.generate_3d_model(design_id, db)
        
        if model_3d:
            return ModelGenerationResponse(
                message="3D model regenerated successfully",
                model_id=model_3d.id
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to regenerate 3D model"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error regenerating 3D model: {str(e)}"
        )