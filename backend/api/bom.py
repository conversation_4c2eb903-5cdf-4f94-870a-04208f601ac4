"""
Bill of Materials (BOM) API endpoints

Provides REST API for BOM generation, validation, export, and management.
"""

from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import io
import logging

from database.connection import get_database
from auth.dependencies import get_current_user
from models.user import User
from models.design import Design, BOMItem
from services.bom_service import BOMService, BOMValidationResult, BOMExportData
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/bom", tags=["BOM"])

# Pydantic models for request/response
class BOMItemResponse(BaseModel):
    id: int
    part_number: str | None
    description: str | None
    quantity: int | None
    material: str | None
    volume: float | None
    weight: float | None
    unit_weight: float | None
    
    class Config:
        from_attributes = True


class BOMValidationIssueResponse(BaseModel):
    severity: str
    field: str
    message: str
    part_number: str | None = None
    suggestions: List[str] | None = None


class BOMValidationResponse(BaseModel):
    status: str
    completeness_score: float
    issues: List[BOMValidationIssueResponse]
    missing_fields: List[str]
    flagged_parts: List[str]


class BOMGenerationResponse(BaseModel):
    success: bool
    message: str
    bom_items: List[BOMItemResponse]
    validation: BOMValidationResponse
    total_items: int


class BOMUpdateRequest(BaseModel):
    part_number: str | None = None
    description: str | None = None
    quantity: int | None = None
    material: str | None = None


class BOMExportResponse(BaseModel):
    headers: List[str]
    rows: List[List[str]]
    metadata: Dict[str, Any]


# Initialize BOM service
bom_service = BOMService()


@router.post("/generate/{design_id}", response_model=BOMGenerationResponse)
async def generate_bom(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Generate BOM from analysis results for a design
    
    Args:
        design_id: ID of the design to generate BOM for
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        BOM generation results with validation
    """
    try:
        # Verify user owns the design or has access
        design = db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with user
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == current_user.id
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Generate BOM
        bom_items, validation_result = bom_service.generate_bom_from_analysis(design_id)
        
        # Convert to response format
        bom_items_response = [BOMItemResponse.from_orm(item) for item in bom_items]
        
        validation_issues = [
            BOMValidationIssueResponse(
                severity=issue.severity,
                field=issue.field,
                message=issue.message,
                part_number=issue.part_number,
                suggestions=issue.suggestions
            )
            for issue in validation_result.issues
        ]
        
        validation_response = BOMValidationResponse(
            status=validation_result.status.value,
            completeness_score=validation_result.completeness_score,
            issues=validation_issues,
            missing_fields=validation_result.missing_fields,
            flagged_parts=validation_result.flagged_parts
        )
        
        # Update design status
        design.status = "bom_generated"
        db.commit()
        
        logger.info(f"Generated BOM for design {design_id} with {len(bom_items)} items")
        
        return BOMGenerationResponse(
            success=True,
            message=f"Successfully generated BOM with {len(bom_items)} items",
            bom_items=bom_items_response,
            validation=validation_response,
            total_items=len(bom_items)
        )
        
    except ValueError as e:
        logger.error(f"BOM generation validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"BOM generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="BOM generation failed")


@router.get("/{design_id}", response_model=List[BOMItemResponse])
async def get_bom(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Get BOM items for a design
    
    Args:
        design_id: ID of the design
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of BOM items
    """
    try:
        # Verify access
        design = db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with user
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == current_user.id
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Get BOM items
        bom_items = bom_service.get_bom_items(design_id)
        
        return [BOMItemResponse.from_orm(item) for item in bom_items]
        
    except Exception as e:
        logger.error(f"Failed to get BOM for design {design_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve BOM")


@router.get("/{design_id}/validate", response_model=BOMValidationResponse)
async def validate_bom(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Validate existing BOM for completeness and issues
    
    Args:
        design_id: ID of the design
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        BOM validation results
    """
    try:
        # Verify access
        design = db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with user
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == current_user.id
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Validate BOM
        validation_result = bom_service.validate_existing_bom(design_id)
        
        validation_issues = [
            BOMValidationIssueResponse(
                severity=issue.severity,
                field=issue.field,
                message=issue.message,
                part_number=issue.part_number,
                suggestions=issue.suggestions
            )
            for issue in validation_result.issues
        ]
        
        return BOMValidationResponse(
            status=validation_result.status.value,
            completeness_score=validation_result.completeness_score,
            issues=validation_issues,
            missing_fields=validation_result.missing_fields,
            flagged_parts=validation_result.flagged_parts
        )
        
    except Exception as e:
        logger.error(f"BOM validation failed for design {design_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="BOM validation failed")


@router.get("/{design_id}/export/csv")
async def export_bom_csv(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Export BOM to CSV format
    
    Args:
        design_id: ID of the design
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CSV file as streaming response
    """
    try:
        # Verify access
        design = db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with user
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == current_user.id
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Export BOM to CSV
        csv_content = bom_service.export_bom_csv(design_id)
        
        # Create streaming response
        csv_io = io.StringIO(csv_content)
        
        filename = f"BOM_{design.name}_{design_id}.csv"
        
        return StreamingResponse(
            io.BytesIO(csv_content.encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"BOM CSV export failed for design {design_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="BOM export failed")


@router.get("/{design_id}/export", response_model=BOMExportResponse)
async def get_bom_export_data(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Get structured BOM export data
    
    Args:
        design_id: ID of the design
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Structured BOM export data
    """
    try:
        # Verify access
        design = db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with user
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == current_user.id
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Get export data
        export_data = bom_service.get_bom_export_data(design_id)
        
        return BOMExportResponse(
            headers=export_data.headers,
            rows=export_data.rows,
            metadata=export_data.metadata
        )
        
    except Exception as e:
        logger.error(f"BOM export data failed for design {design_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get BOM export data")


@router.put("/item/{item_id}", response_model=BOMItemResponse)
async def update_bom_item(
    item_id: int,
    updates: BOMUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Update a BOM item
    
    Args:
        item_id: ID of the BOM item to update
        updates: Fields to update
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Updated BOM item
    """
    try:
        # Get the BOM item and verify access
        bom_item = db.query(BOMItem).filter(BOMItem.id == item_id).first()
        if not bom_item:
            raise HTTPException(status_code=404, detail="BOM item not found")
        
        # Check design access
        design = db.query(Design).filter(Design.id == bom_item.design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with edit permissions
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design.id,
                DesignShare.shared_with_id == current_user.id,
                DesignShare.permission_level == 'edit'
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Edit access denied")
        
        # Update the item
        update_dict = updates.dict(exclude_unset=True)
        updated_item = bom_service.update_bom_item(item_id, update_dict)
        
        logger.info(f"Updated BOM item {item_id} for design {design.id}")
        
        return BOMItemResponse.from_orm(updated_item)
        
    except ValueError as e:
        logger.error(f"BOM item update validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"BOM item update failed: {str(e)}")
        raise HTTPException(status_code=500, detail="BOM item update failed")


@router.delete("/item/{item_id}")
async def delete_bom_item(
    item_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Delete a BOM item
    
    Args:
        item_id: ID of the BOM item to delete
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    try:
        # Get the BOM item and verify access
        bom_item = db.query(BOMItem).filter(BOMItem.id == item_id).first()
        if not bom_item:
            raise HTTPException(status_code=404, detail="BOM item not found")
        
        # Check design access
        design = db.query(Design).filter(Design.id == bom_item.design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with edit permissions
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design.id,
                DesignShare.shared_with_id == current_user.id,
                DesignShare.permission_level == 'edit'
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Edit access denied")
        
        # Delete the item
        success = bom_service.delete_bom_item(item_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="BOM item not found")
        
        logger.info(f"Deleted BOM item {item_id} from design {design.id}")
        
        return {"message": "BOM item deleted successfully"}
        
    except Exception as e:
        logger.error(f"BOM item deletion failed: {str(e)}")
        raise HTTPException(status_code=500, detail="BOM item deletion failed")


@router.get("/{design_id}/summary")
async def get_bom_summary(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Get BOM summary statistics
    
    Args:
        design_id: ID of the design
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        BOM summary with statistics
    """
    try:
        # Verify access
        design = db.query(Design).filter(Design.id == design_id).first()
        if not design:
            raise HTTPException(status_code=404, detail="Design not found")
        
        if design.user_id != current_user.id and not current_user.is_admin:
            # Check if design is shared with user
            from models.sharing import DesignShare
            shared = db.query(DesignShare).filter(
                DesignShare.design_id == design_id,
                DesignShare.shared_with_id == current_user.id
            ).first()
            
            if not shared:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Get BOM items
        bom_items = bom_service.get_bom_items(design_id)
        
        # Calculate summary statistics
        total_items = len(bom_items)
        total_quantity = sum(item.quantity or 0 for item in bom_items)
        total_weight = sum(item.weight or 0 for item in bom_items)
        unique_materials = len(set(item.material for item in bom_items if item.material))
        
        # Count items by material
        material_counts = {}
        for item in bom_items:
            material = item.material or 'Unknown'
            material_counts[material] = material_counts.get(material, 0) + (item.quantity or 0)
        
        # Get validation status
        validation_result = bom_service.validate_existing_bom(design_id)
        
        return {
            "design_id": design_id,
            "design_name": design.name,
            "total_items": total_items,
            "total_quantity": total_quantity,
            "total_weight": round(total_weight, 4),
            "unique_materials": unique_materials,
            "material_breakdown": material_counts,
            "validation_status": validation_result.status.value,
            "completeness_score": round(validation_result.completeness_score, 2),
            "has_issues": len(validation_result.issues) > 0,
            "critical_issues": len([i for i in validation_result.issues if i.severity == 'error'])
        }
        
    except Exception as e:
        logger.error(f"BOM summary failed for design {design_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get BOM summary")