"""
Drawing Analysis API Endpoints

FastAPI endpoints for the drawing analysis orchestration service.
"""

import os
import redis
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from typing import Dict, List, Any, Optional
import tempfile
from pathlib import Path
import logging

from services.drawing_analysis_service import (
    DrawingAnalysisService, AnalysisRequest, AnalysisProgress, AnalysisOutput
)
from celery_app import celery_app
from auth.dependencies import get_current_user
from models.user import User
from models.design import Design
from database.connection import get_database as get_db
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/analysis", tags=["Analysis"])

# Initialize Redis client and analysis service
redis_client = redis.Redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379/0'))
analysis_service = DrawingAnalysisService(redis_client, celery_app)


@router.post("/start", response_model=Dict[str, Any])
async def start_drawing_analysis(
    design_id: int,
    analysis_options: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start analysis of an uploaded drawing
    
    Args:
        design_id: ID of the design to analyze
        analysis_options: Optional analysis configuration
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Task information for tracking progress
    """
    try:
        # Verify design exists and user has access
        design = db.query(Design).filter(
            Design.id == design_id,
            Design.user_id == current_user.id
        ).first()
        
        if not design:
            raise HTTPException(
                status_code=404,
                detail="Design not found or access denied"
            )
        
        if not design.file_path or not os.path.exists(design.file_path):
            raise HTTPException(
                status_code=400,
                detail="Design file not found"
            )
        
        # Create analysis request
        analysis_request = AnalysisRequest(
            design_id=design_id,
            file_path=design.file_path,
            user_id=current_user.id,
            analysis_options=analysis_options or {}
        )
        
        # Start analysis
        task_id = await analysis_service.start_analysis(analysis_request)
        
        # Update design status
        design.status = "analysis_in_progress"
        db.commit()
        
        return {
            "task_id": task_id,
            "design_id": design_id,
            "status": "started",
            "message": "Analysis started successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start analysis: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start analysis: {str(e)}"
        )


@router.get("/status/{design_id}", response_model=Dict[str, Any])
async def get_analysis_status(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current analysis status for a design
    
    Args:
        design_id: Design ID
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Current analysis status and progress
    """
    try:
        # Verify design access
        design = db.query(Design).filter(
            Design.id == design_id,
            Design.user_id == current_user.id
        ).first()
        
        if not design:
            raise HTTPException(
                status_code=404,
                detail="Design not found or access denied"
            )
        
        # Get analysis status
        progress = await analysis_service.get_analysis_status(design_id)
        
        if progress:
            return {
                "design_id": design_id,
                "status": progress.status.value,
                "progress_percentage": progress.progress_percentage,
                "current_stage": progress.current_stage,
                "estimated_time_remaining": progress.estimated_time_remaining,
                "errors": progress.errors,
                "warnings": progress.warnings
            }
        else:
            return {
                "design_id": design_id,
                "status": "not_started",
                "progress_percentage": 0.0,
                "current_stage": "Analysis not started",
                "estimated_time_remaining": None,
                "errors": [],
                "warnings": []
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get analysis status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get analysis status: {str(e)}"
        )


@router.get("/results/{design_id}", response_model=Dict[str, Any])
async def get_analysis_results(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get analysis results for a design
    
    Args:
        design_id: Design ID
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Complete analysis results
    """
    try:
        # Verify design access
        design = db.query(Design).filter(
            Design.id == design_id,
            Design.user_id == current_user.id
        ).first()
        
        if not design:
            raise HTTPException(
                status_code=404,
                detail="Design not found or access denied"
            )
        
        # Get analysis results
        results = await analysis_service.get_analysis_results(design_id)
        
        if not results:
            raise HTTPException(
                status_code=404,
                detail="Analysis results not found"
            )
        
        return {
            "design_id": results.design_id,
            "cv_results": results.cv_results,
            "ocr_results": results.ocr_results,
            "confidence_assessment": results.confidence_assessment,
            "quality_report": results.quality_report,
            "processing_time": results.processing_time,
            "errors": results.errors,
            "warnings": results.warnings,
            "created_at": results.created_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get analysis results: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get analysis results: {str(e)}"
        )


@router.post("/upload-and-analyze", response_model=Dict[str, Any])
async def upload_and_analyze_drawing(
    file: UploadFile = File(...),
    analysis_options: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Upload a drawing file and immediately start analysis
    
    Args:
        file: Uploaded drawing file
        analysis_options: Optional analysis configuration
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Design and task information
    """
    try:
        # Validate file type
        allowed_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.dxf'}
        file_extension = Path(file.filename).suffix.lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type. Allowed: {', '.join(allowed_extensions)}"
            )
        
        # Create design record
        design = Design(
            user_id=current_user.id,
            name=Path(file.filename).stem,
            original_filename=file.filename,
            status="uploading"
        )
        db.add(design)
        db.commit()
        db.refresh(design)
        
        # Save uploaded file
        upload_dir = Path("uploads") / str(current_user.id)
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = upload_dir / f"{design.id}_{file.filename}"
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Update design with file path
        design.file_path = str(file_path)
        design.status = "uploaded"
        db.commit()
        
        # Start analysis
        analysis_request = AnalysisRequest(
            design_id=design.id,
            file_path=str(file_path),
            user_id=current_user.id,
            analysis_options=analysis_options or {}
        )
        
        task_id = await analysis_service.start_analysis(analysis_request)
        
        # Update design status
        design.status = "analysis_in_progress"
        db.commit()
        
        return {
            "design_id": design.id,
            "task_id": task_id,
            "filename": file.filename,
            "status": "analysis_started",
            "message": "File uploaded and analysis started successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to upload and analyze: {str(e)}")
        
        # Clean up on error
        if 'design' in locals():
            try:
                db.delete(design)
                db.commit()
            except:
                pass
        
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.unlink(file_path)
            except:
                pass
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upload and analyze: {str(e)}"
        )


@router.get("/confidence/{design_id}", response_model=Dict[str, Any])
async def get_confidence_assessment(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed confidence assessment for analysis results
    
    Args:
        design_id: Design ID
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Detailed confidence assessment
    """
    try:
        # Verify design access
        design = db.query(Design).filter(
            Design.id == design_id,
            Design.user_id == current_user.id
        ).first()
        
        if not design:
            raise HTTPException(
                status_code=404,
                detail="Design not found or access denied"
            )
        
        # Get analysis results
        results = await analysis_service.get_analysis_results(design_id)
        
        if not results:
            raise HTTPException(
                status_code=404,
                detail="Analysis results not found"
            )
        
        return results.confidence_assessment
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get confidence assessment: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get confidence assessment: {str(e)}"
        )


@router.get("/quality-report/{design_id}", response_model=Dict[str, Any])
async def get_quality_report(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get quality report for analysis results
    
    Args:
        design_id: Design ID
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Quality report with issues and recommendations
    """
    try:
        # Verify design access
        design = db.query(Design).filter(
            Design.id == design_id,
            Design.user_id == current_user.id
        ).first()
        
        if not design:
            raise HTTPException(
                status_code=404,
                detail="Design not found or access denied"
            )
        
        # Get analysis results
        results = await analysis_service.get_analysis_results(design_id)
        
        if not results:
            raise HTTPException(
                status_code=404,
                detail="Analysis results not found"
            )
        
        return results.quality_report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get quality report: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get quality report: {str(e)}"
        )


@router.delete("/results/{design_id}", response_model=Dict[str, str])
async def clear_analysis_cache(
    design_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Clear cached analysis results for a design
    
    Args:
        design_id: Design ID
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    try:
        # Verify design access
        design = db.query(Design).filter(
            Design.id == design_id,
            Design.user_id == current_user.id
        ).first()
        
        if not design:
            raise HTTPException(
                status_code=404,
                detail="Design not found or access denied"
            )
        
        # Clear cache
        cache_key = f"analysis_result:{design_id}"
        progress_key = f"analysis_progress:{design_id}"
        
        redis_client.delete(cache_key)
        redis_client.delete(progress_key)
        
        return {
            "message": "Analysis cache cleared successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to clear analysis cache: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear analysis cache: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, Any])
async def analysis_health_check():
    """
    Health check for the analysis service
    
    Returns:
        Service health status
    """
    try:
        # Test Redis connection
        redis_client.ping()
        
        # Test Celery connection
        celery_inspect = celery_app.control.inspect()
        active_tasks = celery_inspect.active()
        
        # Test basic functionality
        health_task = celery_app.send_task('drawing_analysis.health_check')
        health_result = health_task.get(timeout=10)
        
        return {
            "status": "healthy",
            "redis_connected": True,
            "celery_connected": active_tasks is not None,
            "analysis_engine_status": health_result.get('status', 'unknown'),
            "message": "Analysis service is operational"
        }
        
    except Exception as e:
        logger.error(f"Analysis health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Analysis service has issues"
        }