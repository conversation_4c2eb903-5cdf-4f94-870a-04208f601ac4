"""
File management API endpoints.
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import io
import logging
from datetime import datetime

from database.connection import get_database
from auth.dependencies import get_current_user, get_current_active_user
from models.user import User
from models.design import Design
from services.file_service import file_storage_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/files", tags=["files"])


@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    design_name: Optional[str] = None,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    Upload a new engineering drawing file.
    
    Supports PDF, DXF, and image formats (PNG, JPG, TIFF).
    Maximum file size: 50MB.
    """
    try:
        # Use filename as design name if not provided
        if not design_name and file.filename:
            design_name = file.filename.rsplit('.', 1)[0]  # Remove extension
        elif not design_name:
            design_name = f"Design_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Store file using file storage service
        storage_result = await file_storage_service.store_file(
            file=file,
            user_id=current_user.id,
            design_name=design_name
        )
        
        # Create design record in database
        design = Design(
            user_id=current_user.id,
            name=design_name,
            original_filename=storage_result['original_filename'],
            file_path=storage_result['file_path'],
            status="uploaded"
        )
        
        db.add(design)
        db.commit()
        db.refresh(design)
        
        return {
            "message": "File uploaded successfully",
            "design_id": design.id,
            "design_name": design.name,
            "file_size": storage_result['file_size'],
            "original_filename": storage_result['original_filename'],
            "status": design.status,
            "warnings": storage_result.get('validation_warnings', []),
            "created_at": design.created_at.isoformat()
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions from file service
        raise
    except Exception as e:
        logger.error(f"Unexpected error in file upload: {e}")
        raise HTTPException(status_code=500, detail="File upload failed")


@router.get("/{design_id}")
async def get_file_info(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get file information and metadata for a design.
    """
    # Get design with permission check
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(status_code=404, detail="Design not found")
    
    return {
        "design_id": design.id,
        "name": design.name,
        "original_filename": design.original_filename,
        "file_path": design.file_path,
        "status": design.status,
        "created_at": design.created_at.isoformat(),
        "updated_at": design.updated_at.isoformat()
    }


@router.get("/{design_id}/download")
async def download_file(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    Download the original file for a design.
    """
    # Get design with permission check
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(status_code=404, detail="Design not found")
    
    if not design.file_path:
        raise HTTPException(status_code=404, detail="File not found")
    
    try:
        # Retrieve file from storage
        file_content = await file_storage_service.retrieve_file(design.file_path)
        
        # Determine content type
        content_type = "application/octet-stream"
        if design.original_filename:
            if design.original_filename.lower().endswith('.pdf'):
                content_type = "application/pdf"
            elif design.original_filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                content_type = "image/jpeg" if design.original_filename.lower().endswith(('.jpg', '.jpeg')) else "image/png"
            elif design.original_filename.lower().endswith('.dxf'):
                content_type = "application/dxf"
        
        # Create streaming response
        file_stream = io.BytesIO(file_content)
        
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={design.original_filename or 'download'}"
            }
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions from file service
        raise
    except Exception as e:
        logger.error(f"Unexpected error downloading file: {e}")
        raise HTTPException(status_code=500, detail="File download failed")


@router.get("/{design_id}/preview-url")
async def get_preview_url(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a presigned URL for file preview (expires in 1 hour).
    """
    # Get design with permission check
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(status_code=404, detail="Design not found")
    
    if not design.file_path:
        raise HTTPException(status_code=404, detail="File not found")
    
    try:
        # Generate presigned URL
        preview_url = file_storage_service.get_file_url(design.file_path)
        
        return {
            "preview_url": preview_url,
            "expires_in": "1 hour",
            "design_id": design.id,
            "filename": design.original_filename
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions from file service
        raise
    except Exception as e:
        logger.error(f"Unexpected error generating preview URL: {e}")
        raise HTTPException(status_code=500, detail="Preview URL generation failed")


@router.delete("/{design_id}")
async def delete_file(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a design and its associated file.
    """
    # Get design with permission check
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(status_code=404, detail="Design not found")
    
    try:
        # Delete file from storage if it exists
        if design.file_path:
            await file_storage_service.delete_file(design.file_path)
        
        # Delete design record from database
        db.delete(design)
        db.commit()
        
        return {
            "message": "Design and file deleted successfully",
            "design_id": design_id
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions from file service
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting file: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="File deletion failed")


@router.put("/{design_id}/status")
async def update_file_status(
    design_id: int,
    status: str,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update the processing status of a design file.
    
    Valid statuses: uploaded, processing, analyzed, completed, failed
    """
    valid_statuses = ["uploaded", "processing", "analyzed", "completed", "failed"]
    
    if status not in valid_statuses:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid status. Valid statuses: {', '.join(valid_statuses)}"
        )
    
    # Get design with permission check
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(status_code=404, detail="Design not found")
    
    try:
        # Update status
        old_status = design.status
        design.status = status
        design.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(design)
        
        return {
            "message": "Status updated successfully",
            "design_id": design.id,
            "old_status": old_status,
            "new_status": design.status,
            "updated_at": design.updated_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Unexpected error updating status: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Status update failed")


@router.get("/{design_id}/status")
async def get_file_status(
    design_id: int,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the current processing status of a design file.
    """
    # Get design with permission check
    design = db.query(Design).filter(
        Design.id == design_id,
        Design.user_id == current_user.id
    ).first()
    
    if not design:
        raise HTTPException(status_code=404, detail="Design not found")
    
    return {
        "design_id": design.id,
        "status": design.status,
        "name": design.name,
        "created_at": design.created_at.isoformat(),
        "updated_at": design.updated_at.isoformat()
    }


@router.get("/")
async def list_user_files(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_database),
    current_user: User = Depends(get_current_active_user)
):
    """
    List all files/designs for the current user.
    """
    query = db.query(Design).filter(Design.user_id == current_user.id)
    
    # Filter by status if provided
    if status:
        query = query.filter(Design.status == status)
    
    # Apply pagination
    designs = query.offset(skip).limit(limit).all()
    
    # Get total count
    total_count = db.query(Design).filter(Design.user_id == current_user.id).count()
    
    return {
        "designs": [
            {
                "design_id": design.id,
                "name": design.name,
                "original_filename": design.original_filename,
                "status": design.status,
                "created_at": design.created_at.isoformat(),
                "updated_at": design.updated_at.isoformat()
            }
            for design in designs
        ],
        "total_count": total_count,
        "skip": skip,
        "limit": limit
    }