"""
OCR API Endpoints

FastAPI endpoints for OCR text extraction functionality.
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Depends
from typing import Dict, List, Any, Optional
import tempfile
import os
from pathlib import Path
import logging

from services.ocr_service import OCRService, ManualCorrectionService
from auth.dependencies import get_current_user
from models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ocr", tags=["OCR"])

# Service instances
ocr_service = OCRService()
correction_service = ManualCorrectionService()


@router.post("/extract", response_model=Dict[str, Any])
async def extract_text_from_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Extract text from an uploaded image using OCR
    
    Args:
        file: Uploaded image file
        current_user: Authenticated user
        
    Returns:
        OCR extraction results
    """
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=400,
            detail="File must be an image (PNG, JPG, TIFF supported)"
        )
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
        try:
            # Save uploaded file
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            # Process with OCR
            results = await ocr_service.process_image_file(temp_file.name)
            
            # Add user context
            results['processed_by'] = current_user.username
            results['filename'] = file.filename
            
            return results
            
        except Exception as e:
            logger.error(f"OCR extraction failed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"OCR processing failed: {str(e)}"
            )
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file.name)
            except OSError:
                pass


@router.post("/extract-patterns", response_model=Dict[str, List[str]])
async def extract_patterns_from_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Extract specific patterns (dimensions, part numbers, materials) from image
    
    Args:
        file: Uploaded image file
        current_user: Authenticated user
        
    Returns:
        Extracted patterns by type
    """
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=400,
            detail="File must be an image (PNG, JPG, TIFF supported)"
        )
    
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
        try:
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            patterns = await ocr_service.extract_patterns_from_file(temp_file.name)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Pattern extraction failed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Pattern extraction failed: {str(e)}"
            )
        finally:
            try:
                os.unlink(temp_file.name)
            except OSError:
                pass


@router.post("/validate", response_model=Dict[str, Any])
async def validate_ocr_results(
    ocr_results: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """
    Validate OCR results and provide quality assessment
    
    Args:
        ocr_results: OCR results to validate
        current_user: Authenticated user
        
    Returns:
        Validation results with quality metrics
    """
    try:
        validation_result = await ocr_service.validate_ocr_results(ocr_results)
        return validation_result
        
    except Exception as e:
        logger.error(f"OCR validation failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Validation failed: {str(e)}"
        )


@router.post("/correction-session", response_model=Dict[str, Any])
async def create_correction_session(
    ocr_results: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """
    Create a manual correction session for low-confidence OCR results
    
    Args:
        ocr_results: Original OCR results
        current_user: Authenticated user
        
    Returns:
        Correction session data
    """
    try:
        session = await correction_service.create_correction_session(ocr_results)
        session['created_by'] = current_user.username
        
        return session
        
    except Exception as e:
        logger.error(f"Failed to create correction session: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create correction session: {str(e)}"
        )


@router.post("/apply-corrections/{session_id}", response_model=Dict[str, Any])
async def apply_manual_corrections(
    session_id: str,
    corrections: List[Dict[str, Any]],
    current_user: User = Depends(get_current_user)
):
    """
    Apply manual corrections to OCR results
    
    Args:
        session_id: Correction session ID
        corrections: List of corrections to apply
        current_user: Authenticated user
        
    Returns:
        Updated OCR results with corrections
    """
    try:
        result = await correction_service.apply_corrections(session_id, corrections)
        result['corrected_by'] = current_user.username
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to apply corrections: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to apply corrections: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, str])
async def ocr_health_check():
    """
    Health check endpoint for OCR service
    
    Returns:
        Service health status
    """
    try:
        # Test basic OCR functionality
        import pytesseract
        import cv2
        import numpy as np
        
        # Create a simple test image with text
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "TEST", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Try OCR extraction
        text = pytesseract.image_to_string(test_image)
        
        if "TEST" in text.upper():
            return {"status": "healthy", "message": "OCR service is operational"}
        else:
            return {"status": "degraded", "message": "OCR recognition may be impaired"}
            
    except Exception as e:
        logger.error(f"OCR health check failed: {str(e)}")
        return {"status": "unhealthy", "message": f"OCR service error: {str(e)}"}