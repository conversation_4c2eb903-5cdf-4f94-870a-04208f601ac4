#!/usr/bin/env python3
"""
End-to-end integration tests for complete user workflows
Tests the entire system from file upload to final results
"""

import pytest
import asyncio
import os
import tempfile
import json
from httpx import AsyncClient
from fastapi.testclient import TestClient
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import cv2

from main import app
from database.connection import get_database
from models.user import User
from models.design import Design
from auth.security import create_access_token


class TestEndToEndWorkflows:
    """End-to-end integration tests for complete user workflows"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def test_user_token(self, client):
        """Create test user and return auth token"""
        # Register test user
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        response = client.post("/api/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Login and get token
        login_data = {
            "username": "testuser",
            "password": "testpassword123"
        }
        response = client.post("/api/auth/login", json=login_data)
        assert response.status_code == 200
        return response.json()["access_token"]
    
    @pytest.fixture
    def auth_headers(self, test_user_token):
        """Create authorization headers"""
        return {"Authorization": f"Bearer {test_user_token}"}
    
    @pytest.fixture
    def sample_drawing(self):
        """Create a sample engineering drawing for testing"""
        # Create a simple engineering drawing
        img = Image.new('RGB', (1200, 800), color='white')
        draw = ImageDraw.Draw(img)
        
        # Draw title block
        draw.rectangle([50, 50, 350, 150], outline='black', width=2)
        draw.text((60, 60), "PART: ABC-123", fill='black')
        draw.text((60, 80), "MATERIAL: STEEL", fill='black')
        draw.text((60, 100), "QTY: 2", fill='black')
        
        # Draw main view
        draw.rectangle([400, 200, 800, 600], outline='black', width=3)
        draw.text((500, 180), "MAIN VIEW", fill='black')
        
        # Add dimensions
        draw.text((420, 620), "400mm", fill='black')
        draw.text((820, 300), "400mm", fill='black')
        
        # Draw parts list
        draw.rectangle([900, 100, 1150, 300], outline='black', width=2)
        draw.text((910, 110), "PARTS LIST", fill='black')
        draw.text((910, 130), "1. ABC-123 - PLATE", fill='black')
        draw.text((910, 150), "2. DEF-456 - BOLT", fill='black')
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        img.save(temp_file.name)
        return temp_file.name
    
    def test_complete_user_workflow(self, client, auth_headers, sample_drawing):
        """Test complete user workflow from registration to final results"""
        
        # Step 1: Upload file
        with open(sample_drawing, 'rb') as f:
            files = {"file": ("test_drawing.png", f, "image/png")}
            response = client.post(
                "/api/files/upload",
                files=files,
                headers=auth_headers
            )
        
        # File upload might return different status codes depending on implementation
        assert response.status_code in [200, 201]
        file_data = response.json()
        
        # Step 2: Check user files/designs
        response = client.get(
            "/api/files/",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        files_data = response.json()
        assert "designs" in files_data
        
        # Step 3: Test basic API endpoints
        response = client.get(
            "/api/auth/me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        user_data = response.json()
        assert "username" in user_data
        
        # Step 4: Test sharing endpoint
        response = client.get(
            "/api/sharing/shared-with-me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        shared_data = response.json()
        assert "shared_designs" in shared_data
        
        # Cleanup
        os.unlink(sample_drawing)
    
    def test_sharing_workflow(self, client, auth_headers):
        """Test design sharing workflow"""
        
        # Create second user
        user2_data = {
            "username": "testuser2",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        response = client.post("/api/auth/register", json=user2_data)
        assert response.status_code == 201
        
        # Login second user
        login_data = {
            "username": "testuser2",
            "password": "testpassword123"
        }
        response = client.post("/api/auth/login", json=login_data)
        assert response.status_code == 200
        user2_token = response.json()["access_token"]
        user2_headers = {"Authorization": f"Bearer {user2_token}"}
        
        # Get user1's designs
        response = client.get("/api/files/", headers=auth_headers)
        assert response.status_code == 200
        files_data = response.json()
        designs = files_data.get("designs", [])
        
        if len(designs) == 0:
            pytest.skip("No designs available for sharing test")
        
        design_id = designs[0]["design_id"]
        
        # Share design with user2
        share_data = {
            "design_id": design_id,
            "shared_with_username": "testuser2",
            "permission_level": "view"
        }
        response = client.post(
            "/api/sharing/grant",
            json=share_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        
        # Check shared designs for user2
        response = client.get(
            "/api/sharing/shared-with-me",
            headers=user2_headers
        )
        
        assert response.status_code == 200
        shared_designs = response.json()
        assert len(shared_designs) >= 1
        
        # Revoke sharing
        response = client.delete(
            f"/api/sharing/revoke",
            json={"design_id": design_id, "shared_with_username": "testuser2"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
    
    def test_fabrication_progress_workflow(self, client, auth_headers):
        """Test fabrication progress tracking workflow"""
        
        # Get user's designs
        response = client.get("/api/files/", headers=auth_headers)
        assert response.status_code == 200
        files_data = response.json()
        designs = files_data.get("designs", [])
        
        if len(designs) == 0:
            pytest.skip("No designs available for fabrication test")
        
        design_id = designs[0]["design_id"]
        
        # Get fabrication progress
        response = client.get(
            f"/api/fabrication/{design_id}/progress",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        progress_data = response.json()
        
        if len(progress_data.get("parts", [])) == 0:
            pytest.skip("No parts available for fabrication test")
        
        part_id = progress_data["parts"][0]["id"]
        
        # Update part status
        status_data = {
            "stage_name": "In Progress",
            "notes": "Started machining operations"
        }
        response = client.put(
            f"/api/fabrication/{design_id}/parts/{part_id}/status",
            json=status_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        
        # Get updated progress
        response = client.get(
            f"/api/fabrication/{design_id}/progress",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        updated_progress = response.json()
        
        # Verify status was updated
        updated_part = next(
            (p for p in updated_progress["parts"] if p["id"] == part_id),
            None
        )
        assert updated_part is not None
        assert updated_part["current_stage"] == "In Progress"
    
    def test_error_handling_workflow(self, client, auth_headers):
        """Test error handling in workflows"""
        
        # Test invalid file upload
        invalid_file_content = b"This is not a valid image"
        files = {"file": ("invalid.txt", invalid_file_content, "text/plain")}
        response = client.post(
            "/api/files/upload",
            files=files,
            headers=auth_headers
        )
        
        assert response.status_code == 400
        error_data = response.json()
        assert "error" in error_data
        
        # Test analysis with non-existent file
        response = client.post(
            "/api/analysis/process",
            json={"file_id": 99999},
            headers=auth_headers
        )
        
        assert response.status_code == 404
        
        # Test unauthorized access
        response = client.get("/api/files/")
        assert response.status_code == 401
    
    def test_performance_workflow(self, client, auth_headers):
        """Test system performance with multiple operations"""
        
        import time
        
        # Measure dashboard load time
        start_time = time.time()
        response = client.get("/api/files/", headers=auth_headers)
        dashboard_time = time.time() - start_time
        
        assert response.status_code == 200
        assert dashboard_time < 2.0  # Should load within 2 seconds
        
        # Test concurrent requests
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def make_request():
            try:
                response = client.get("/api/files/", headers=auth_headers)
                results_queue.put(response.status_code)
            except Exception as e:
                results_queue.put(str(e))
        
        # Create 5 concurrent requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        success_count = 0
        while not results_queue.empty():
            result = results_queue.get()
            if result == 200:
                success_count += 1
        
        assert success_count >= 4  # At least 80% success rate


if __name__ == "__main__":
    pytest.main([__file__, "-v"])