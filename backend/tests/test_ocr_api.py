"""
API tests for OCR endpoints
"""

import pytest
import tempfile
import os
import cv2
import numpy as np
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock
import io

from backend.main import app
from backend.services.ocr_service import OCRService
from processing.ocr.text_extractor import OCRResult, ExtractedText, TextType


client = TestClient(app)


@pytest.fixture
def authenticated_headers():
    """Get authentication headers for API requests"""
    # Mock authentication for testing
    with patch('backend.auth.dependencies.get_current_user') as mock_auth:
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_user.id = 1
        mock_auth.return_value = mock_user
        
        # In a real test, you would get actual JWT token
        return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_image_file():
    """Create a sample image file for testing"""
    image = np.ones((200, 400, 3), dtype=np.uint8) * 255
    cv2.putText(image, "TEST TEXT", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "2.50±0.01", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Convert to bytes
    _, buffer = cv2.imencode('.png', image)
    return io.BytesIO(buffer.tobytes())


class TestOCRAPIEndpoints:
    """Test cases for OCR API endpoints"""
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_extract_text_success(self, mock_auth, sample_image_file):
        """Test successful text extraction endpoint"""
        # Mock authentication
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_user.id = 1
        mock_auth.return_value = mock_user
        
        # Mock OCR service
        with patch.object(OCRService, 'process_image_file') as mock_process:
            mock_process.return_value = {
                'extracted_texts': [
                    {
                        'text': 'TEST TEXT',
                        'confidence': 85.0,
                        'bbox': [50, 80, 100, 20],
                        'text_type': 'general'
                    },
                    {
                        'text': '2.50±0.01',
                        'confidence': 90.0,
                        'bbox': [50, 130, 80, 20],
                        'text_type': 'dimension'
                    }
                ],
                'overall_confidence': 87.5,
                'processing_time': 1.2,
                'errors': [],
                'warnings': []
            }
            
            response = client.post(
                "/api/ocr/extract",
                files={"file": ("test.png", sample_image_file, "image/png")}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert 'extracted_texts' in data
            assert 'overall_confidence' in data
            assert 'processed_by' in data
            assert 'filename' in data
            assert data['overall_confidence'] == 87.5
            assert len(data['extracted_texts']) == 2
            assert data['processed_by'] == 'testuser'
            assert data['filename'] == 'test.png'
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_extract_text_invalid_file_type(self, mock_auth):
        """Test text extraction with invalid file type"""
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_auth.return_value = mock_user
        
        # Create a text file instead of image
        text_file = io.BytesIO(b"This is not an image")
        
        response = client.post(
            "/api/ocr/extract",
            files={"file": ("test.txt", text_file, "text/plain")}
        )
        
        assert response.status_code == 400
        assert "image" in response.json()["detail"].lower()
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_extract_text_processing_error(self, mock_auth, sample_image_file):
        """Test text extraction with processing error"""
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_auth.return_value = mock_user
        
        # Mock OCR service to raise an error
        with patch.object(OCRService, 'process_image_file') as mock_process:
            mock_process.side_effect = Exception("OCR processing failed")
            
            response = client.post(
                "/api/ocr/extract",
                files={"file": ("test.png", sample_image_file, "image/png")}
            )
            
            assert response.status_code == 500
            assert "OCR processing failed" in response.json()["detail"]
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_extract_patterns_success(self, mock_auth, sample_image_file):
        """Test successful pattern extraction endpoint"""
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_auth.return_value = mock_user
        
        with patch.object(OCRService, 'extract_patterns_from_file') as mock_extract:
            mock_extract.return_value = {
                'dimensions': ['2.50±0.01', 'R0.125'],
                'part_numbers': ['ABC-123'],
                'materials': ['STEEL A36'],
                'annotations': ['NOTE: ALL DIMS IN INCHES']
            }
            
            response = client.post(
                "/api/ocr/extract-patterns",
                files={"file": ("test.png", sample_image_file, "image/png")}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert 'dimensions' in data
            assert 'part_numbers' in data
            assert 'materials' in data
            assert 'annotations' in data
            assert len(data['dimensions']) == 2
            assert len(data['part_numbers']) == 1
            assert len(data['materials']) == 1
            assert len(data['annotations']) == 1
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_validate_ocr_results_success(self, mock_auth):
        """Test OCR results validation endpoint"""
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_auth.return_value = mock_user
        
        ocr_results = {
            'extracted_texts': [
                {
                    'text': '2.50±0.01',
                    'confidence': 90.0,
                    'text_type': 'dimension'
                }
            ],
            'overall_confidence': 90.0,
            'errors': []
        }
        
        with patch.object(OCRService, 'validate_ocr_results') as mock_validate:
            mock_validate.return_value = {
                'is_valid': True,
                'quality_score': 90.0,
                'issues': [],
                'recommendations': []
            }
            
            response = client.post("/api/ocr/validate", json=ocr_results)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['is_valid'] is True
            assert data['quality_score'] == 90.0
            assert len(data['issues']) == 0
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_create_correction_session_success(self, mock_auth):
        """Test correction session creation endpoint"""
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_auth.return_value = mock_user
        
        ocr_results = {
            'extracted_texts': [
                {
                    'text': 'unclear_text',
                    'confidence': 25.0,
                    'bbox': [10, 20, 40, 20],
                    'text_type': 'general'
                }
            ],
            'overall_confidence': 25.0,
            'errors': []
        }
        
        with patch('backend.services.ocr_service.ManualCorrectionService.create_correction_session') as mock_create:
            mock_create.return_value = {
                'session_id': 'test-session-123',
                'original_results': ocr_results,
                'corrections_needed': [
                    {
                        'original_text': 'unclear_text',
                        'confidence': 25.0,
                        'bbox': [10, 20, 40, 20],
                        'text_type': 'general',
                        'suggested_corrections': ['clear_text'],
                        'corrected_text': None,
                        'user_verified': False
                    }
                ],
                'status': 'pending'
            }
            
            response = client.post("/api/ocr/correction-session", json=ocr_results)
            
            assert response.status_code == 200
            data = response.json()
            
            assert 'session_id' in data
            assert 'corrections_needed' in data
            assert 'created_by' in data
            assert data['created_by'] == 'testuser'
            assert len(data['corrections_needed']) == 1
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_apply_corrections_success(self, mock_auth):
        """Test applying manual corrections endpoint"""
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_auth.return_value = mock_user
        
        corrections = [
            {
                'original_text': 'unclear_text',
                'corrected_text': '2.50',
                'confidence': 25.0,
                'user_verified': True
            }
        ]
        
        with patch('backend.services.ocr_service.ManualCorrectionService.apply_corrections') as mock_apply:
            mock_apply.return_value = {
                'session_id': 'test-session-123',
                'corrections_applied': 1,
                'status': 'completed',
                'updated_results': {
                    'extracted_texts': [],
                    'overall_confidence': 0.0,
                    'manual_corrections': corrections
                }
            }
            
            response = client.post("/api/ocr/apply-corrections/test-session-123", json=corrections)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['session_id'] == 'test-session-123'
            assert data['corrections_applied'] == 1
            assert data['status'] == 'completed'
            assert 'corrected_by' in data
            assert data['corrected_by'] == 'testuser'
    
    def test_ocr_health_check_success(self):
        """Test OCR health check endpoint"""
        with patch('pytesseract.image_to_string') as mock_tesseract:
            mock_tesseract.return_value = "TEST"
            
            response = client.get("/api/ocr/health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['status'] == 'healthy'
            assert 'operational' in data['message']
    
    def test_ocr_health_check_degraded(self):
        """Test OCR health check with degraded service"""
        with patch('pytesseract.image_to_string') as mock_tesseract:
            mock_tesseract.return_value = "WRONG_TEXT"
            
            response = client.get("/api/ocr/health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['status'] == 'degraded'
            assert 'impaired' in data['message']
    
    def test_ocr_health_check_unhealthy(self):
        """Test OCR health check with service error"""
        with patch('pytesseract.image_to_string') as mock_tesseract:
            mock_tesseract.side_effect = Exception("Tesseract error")
            
            response = client.get("/api/ocr/health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['status'] == 'unhealthy'
            assert 'error' in data['message']
    
    def test_unauthenticated_access(self, sample_image_file):
        """Test that endpoints require authentication"""
        response = client.post(
            "/api/ocr/extract",
            files={"file": ("test.png", sample_image_file, "image/png")}
        )
        
        # Should return 401 or 403 for unauthenticated access
        assert response.status_code in [401, 403]


class TestOCRAPIIntegration:
    """Integration tests for OCR API functionality"""
    
    @patch('backend.auth.dependencies.get_current_user')
    def test_full_ocr_api_workflow(self, mock_auth):
        """Test complete OCR API workflow"""
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_auth.return_value = mock_user
        
        # Create test image
        image = np.ones((200, 400, 3), dtype=np.uint8) * 255
        cv2.putText(image, "2.50±0.01", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(image, "ABC-123", (200, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        _, buffer = cv2.imencode('.png', image)
        image_file = io.BytesIO(buffer.tobytes())
        
        # Mock OCR processing
        with patch.object(OCRService, 'process_image_file') as mock_process, \
             patch.object(OCRService, 'validate_ocr_results') as mock_validate, \
             patch('backend.services.ocr_service.ManualCorrectionService.create_correction_session') as mock_session:
            
            # Mock OCR extraction
            mock_process.return_value = {
                'extracted_texts': [
                    {
                        'text': '2.50±0.01',
                        'confidence': 85.0,
                        'bbox': [50, 80, 80, 20],
                        'text_type': 'dimension'
                    },
                    {
                        'text': 'ABC-123',
                        'confidence': 90.0,
                        'bbox': [200, 80, 70, 20],
                        'text_type': 'part_number'
                    }
                ],
                'overall_confidence': 87.5,
                'processing_time': 1.2,
                'errors': [],
                'warnings': []
            }
            
            # Mock validation
            mock_validate.return_value = {
                'is_valid': True,
                'quality_score': 87.5,
                'issues': [],
                'recommendations': []
            }
            
            # Mock correction session (not needed for high quality results)
            mock_session.return_value = {
                'session_id': 'test-session-123',
                'corrections_needed': [],
                'status': 'pending'
            }
            
            # Step 1: Extract text
            extract_response = client.post(
                "/api/ocr/extract",
                files={"file": ("test.png", image_file, "image/png")}
            )
            
            assert extract_response.status_code == 200
            ocr_results = extract_response.json()
            
            # Step 2: Validate results
            validate_response = client.post("/api/ocr/validate", json=ocr_results)
            
            assert validate_response.status_code == 200
            validation = validate_response.json()
            assert validation['is_valid'] is True
            
            # Step 3: Create correction session (if needed)
            session_response = client.post("/api/ocr/correction-session", json=ocr_results)
            
            assert session_response.status_code == 200
            session = session_response.json()
            assert len(session['corrections_needed']) == 0  # High quality, no corrections needed