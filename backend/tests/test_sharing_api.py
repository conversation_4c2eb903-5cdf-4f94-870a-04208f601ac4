"""
Tests for sharing API endpoints.
"""
import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import Test<PERSON>lient
from fastapi import HTTPException

from main import app
from models.user import User
from models.sharing import DesignShare
from services.sharing_service import SharingService
from auth.dependencies import get_current_user, get_database


class TestSharingAPI:
    """Test cases for sharing API endpoints."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Mock user for authentication
        self.mock_user = User(
            id=1,
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed",
            is_active=True
        )
        
        # Mock database
        self.mock_db = Mock()
        
        # Override dependencies
        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_database] = lambda: self.mock_db
        
        self.client = TestClient(app)
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
    
    def test_grant_access_success(self):
        """Test successful access granting via API."""
        
        from datetime import datetime
        mock_share = DesignShare(
            id=1,
            design_id=1,
            owner_id=1,
            shared_with_id=2,
            permission_level="view",
            created_at=datetime.now()
        )
        
        mock_shared_user = User(
            id=2,
            username="shared_user",
            email="<EMAIL>"
        )
        
        with patch.object(SharingService, 'grant_access', return_value=mock_share):
            with patch.object(self.mock_db, 'query') as mock_query:
                mock_query.return_value.filter.return_value.first.return_value = mock_shared_user
                
                response = self.client.post(
                    "/api/sharing/grant",
                    json={
                        "design_id": 1,
                        "shared_with_email": "<EMAIL>",
                        "permission_level": "view"
                    }
                )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "shared with shared_user" in data["message"]
        assert data["share"]["design_id"] == 1
        assert data["share"]["permission_level"] == "view"
    
    def test_grant_access_invalid_email(self):
        """Test granting access with invalid email format."""
        response = self.client.post(
            "/api/sharing/grant",
            json={
                "design_id": 1,
                "shared_with_email": "invalid-email",
                "permission_level": "view"
            }
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_grant_access_service_error(self):
        """Test handling service errors in grant access."""
        with patch.object(SharingService, 'grant_access', side_effect=HTTPException(status_code=404, detail="Design not found")):
            response = self.client.post(
                "/api/sharing/grant",
                json={
                    "design_id": 999,
                    "shared_with_email": "<EMAIL>",
                    "permission_level": "view"
                }
            )
        
        assert response.status_code == 404
        assert "Design not found" in response.json()["detail"]
    
    def test_revoke_access_success(self):
        """Test successful access revocation via API."""
        with patch.object(SharingService, 'revoke_access', return_value=True):
            response = self.client.delete("/api/sharing/revoke/1/2")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "Access revoked successfully" in data["message"]
    
    def test_get_design_shares_success(self):
        """Test getting design shares via API."""
        mock_shares = [
            {
                "share_id": 1,
                "user_id": 2,
                "username": "user2",
                "email": "<EMAIL>",
                "permission_level": "view",
                "shared_at": "2023-01-01T00:00:00"
            }
        ]
        
        with patch.object(SharingService, 'get_design_shares', return_value=mock_shares):
            response = self.client.get("/api/sharing/design/1/shares")
        
        assert response.status_code == 200
        data = response.json()
        assert data["design_id"] == 1
        assert len(data["shares"]) == 1
        assert data["shares"][0]["username"] == "user2"
    
    def test_get_shared_with_me_success(self):
        """Test getting shared designs via API."""
        mock_shared_designs = [
            {
                "share_id": 1,
                "design_id": 1,
                "design_name": "Shared Design",
                "owner_username": "owner",
                "owner_email": "<EMAIL>",
                "permission_level": "view",
                "shared_at": "2023-01-01T00:00:00",
                "design_status": "completed",
                "design_created_at": "2023-01-01T00:00:00",
                "design_updated_at": "2023-01-01T00:00:00"
            }
        ]
        
        with patch.object(SharingService, 'get_shared_with_me', return_value=mock_shared_designs):
            response = self.client.get("/api/sharing/shared-with-me")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["shared_designs"]) == 1
        assert data["shared_designs"][0]["design_name"] == "Shared Design"
    
    def test_update_permission_success(self):
        """Test updating permission via API."""
        from datetime import datetime
        mock_share = DesignShare(
            id=1,
            design_id=1,
            owner_id=1,
            shared_with_id=2,
            permission_level="edit",
            created_at=datetime.now()
        )
        
        mock_shared_user = User(
            id=2,
            username="shared_user",
            email="<EMAIL>"
        )
        
        with patch.object(SharingService, 'update_permission', return_value=mock_share):
            with patch.object(self.mock_db, 'query') as mock_query:
                mock_query.return_value.filter.return_value.first.return_value = mock_shared_user
                
                response = self.client.put(
                    "/api/sharing/design/1/user/2/permission",
                    json={"permission_level": "edit"}
                )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "Permission updated successfully" in data["message"]
        assert data["share"]["permission_level"] == "edit"
    
    def test_get_sharing_history_success(self):
        """Test getting sharing history via API."""
        mock_history = [
            {
                "action": "granted",
                "user_id": 2,
                "username": "user2",
                "email": "<EMAIL>",
                "permission_level": "view",
                "timestamp": "2023-01-01T00:00:00"
            }
        ]
        
        with patch.object(SharingService, 'get_sharing_history', return_value=mock_history):
            response = self.client.get("/api/sharing/design/1/history")
        
        assert response.status_code == 200
        data = response.json()
        assert data["design_id"] == 1
        assert len(data["history"]) == 1
        assert data["history"][0]["action"] == "granted"
    
    def test_search_users_success(self):
        """Test searching users via API."""
        mock_users = [
            {
                "user_id": 2,
                "username": "testuser2",
                "email": "<EMAIL>"
            }
        ]
        
        with patch.object(SharingService, 'search_users', return_value=mock_users):
            response = self.client.get("/api/sharing/users/search?q=test")
        
        assert response.status_code == 200
        data = response.json()
        assert data["query"] == "test"
        assert len(data["users"]) == 1
        assert data["users"][0]["username"] == "testuser2"
    
    def test_check_design_access_success(self):
        """Test checking design access via API."""
        with patch.object(SharingService, 'check_access', return_value="owner"):
            response = self.client.get("/api/sharing/design/1/access")
        
        assert response.status_code == 200
        data = response.json()
        assert data["design_id"] == 1
        assert data["access_level"] == "owner"
        assert data["has_access"] is True
    
    def test_unauthenticated_access(self):
        """Test that unauthenticated requests are rejected."""
        response = self.client.post(
            "/api/sharing/grant",
            json={
                "design_id": 1,
                "shared_with_email": "<EMAIL>",
                "permission_level": "view"
            }
        )
        
        # Should return 401 or 403 depending on auth implementation
        assert response.status_code in [401, 403]