"""
Tests for authentication security utilities.
"""
import pytest
from datetime import datetime, timedelta

from auth.security import (
    verify_password, get_password_hash, validate_password, validate_username,
    create_access_token, create_refresh_token, create_reset_token, verify_token,
    generate_session_token, ValidationError
)


class TestPasswordSecurity:
    """Test password security functions."""
    
    def test_password_hashing(self):
        """Test password hashing and verification."""
        password = "TestPassword123!"
        hashed = get_password_hash(password)
        
        assert hashed != password
        assert verify_password(password, hashed)
        assert not verify_password("wrong_password", hashed)
    
    def test_validate_password_valid(self):
        """Test password validation with valid passwords."""
        valid_passwords = [
            "TestPass123!",
            "MySecure@Pass1",
            "Complex#Password9",
            "Strong$Pass123"
        ]
        
        for password in valid_passwords:
            assert validate_password(password)
    
    def test_validate_password_invalid(self):
        """Test password validation with invalid passwords."""
        invalid_passwords = [
            "short",  # Too short
            "nouppercase123!",  # No uppercase
            "NOLOWERCASE123!",  # No lowercase
            "NoDigits!",  # No digits
            "NoSpecialChar123",  # No special characters
            "password",  # Common password
        ]
        
        for password in invalid_passwords:
            assert not validate_password(password)


class TestUsernameValidation:
    """Test username validation."""
    
    def test_validate_username_valid(self):
        """Test username validation with valid usernames."""
        valid_usernames = [
            "user123",
            "test_user",
            "admin",
            "user_name_123",
            "a1b2c3"
        ]
        
        for username in valid_usernames:
            assert validate_username(username)
    
    def test_validate_username_invalid(self):
        """Test username validation with invalid usernames."""
        invalid_usernames = [
            "ab",  # Too short
            "a" * 51,  # Too long
            "123user",  # Starts with number
            "_user",  # Starts with underscore
            "user-name",  # Contains hyphen
            "user@name",  # Contains special character
            "user name",  # Contains space
        ]
        
        for username in invalid_usernames:
            assert not validate_username(username)


class TestTokenGeneration:
    """Test JWT token generation and verification."""
    
    def test_create_access_token(self):
        """Test access token creation."""
        data = {"sub": "testuser", "user_id": 1}
        token = create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        token_data = verify_token(token, "access")
        assert token_data is not None
        assert token_data.username == "testuser"
        assert token_data.user_id == 1
        assert token_data.token_type == "access"
    
    def test_create_refresh_token(self):
        """Test refresh token creation."""
        data = {"sub": "testuser", "user_id": 1}
        token = create_refresh_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        token_data = verify_token(token, "refresh")
        assert token_data is not None
        assert token_data.username == "testuser"
        assert token_data.user_id == 1
        assert token_data.token_type == "refresh"
    
    def test_create_reset_token(self):
        """Test reset token creation."""
        data = {"sub": "testuser", "user_id": 1, "email": "<EMAIL>"}
        token = create_reset_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        token_data = verify_token(token, "reset")
        assert token_data is not None
        assert token_data.username == "testuser"
        assert token_data.user_id == 1
        assert token_data.token_type == "reset"
    
    def test_verify_token_invalid(self):
        """Test token verification with invalid tokens."""
        # Invalid token
        assert verify_token("invalid_token") is None
        
        # Wrong token type
        data = {"sub": "testuser", "user_id": 1}
        access_token = create_access_token(data)
        assert verify_token(access_token, "refresh") is None
    
    def test_generate_session_token(self):
        """Test session token generation."""
        token1 = generate_session_token()
        token2 = generate_session_token()
        
        assert isinstance(token1, str)
        assert isinstance(token2, str)
        assert len(token1) > 0
        assert len(token2) > 0
        assert token1 != token2  # Should be unique