"""
Integration tests for the drawing analysis orchestration service
"""

import pytest
import asyncio
import tempfile
import os
import json
import cv2
import numpy as np
from unittest.mock import Mock, patch, AsyncMock
import redis
from celery import Celery

from backend.services.drawing_analysis_service import (
    DrawingAnalysisService, AnalysisRequest, AnalysisProgress, AnalysisStatus,
    ConfidenceAssessor, ErrorAggregator, ResultCacheManager
)
from processing.computer_vision.feature_extractor import ComputerVisionEngine, CVResult
from processing.ocr.text_extractor import OCRTextExtractor, OCRResult


@pytest.fixture
def mock_redis():
    """Mock Redis client"""
    return Mock(spec=redis.Redis)


@pytest.fixture
def mock_celery():
    """Mock Celery app"""
    return Mock(spec=Celery)


@pytest.fixture
def test_image():
    """Create a test engineering drawing image"""
    # Create a simple test drawing
    image = np.ones((800, 1200, 3), dtype=np.uint8) * 255
    
    # Add some geometric features
    cv2.rectangle(image, (100, 100), (300, 200), (0, 0, 0), 2)
    cv2.circle(image, (500, 150), 50, (0, 0, 0), 2)
    cv2.line(image, (100, 300), (600, 300), (0, 0, 0), 2)
    
    # Add some text
    cv2.putText(image, "PART-001", (100, 400), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "STEEL A36", (100, 450), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "100.0 ± 0.1", (100, 500), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    return image


@pytest.fixture
def test_image_file(test_image):
    """Create a temporary image file"""
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        cv2.imwrite(temp_file.name, test_image)
        yield temp_file.name
    
    # Cleanup
    try:
        os.unlink(temp_file.name)
    except OSError:
        pass


@pytest.fixture
def analysis_service(mock_redis, mock_celery):
    """Create analysis service instance"""
    return DrawingAnalysisService(mock_redis, mock_celery)


@pytest.fixture
def sample_analysis_request(test_image_file):
    """Create sample analysis request"""
    return AnalysisRequest(
        design_id=1,
        file_path=test_image_file,
        user_id=1,
        analysis_options={}
    )


class TestConfidenceAssessor:
    """Test confidence assessment functionality"""
    
    def test_assess_overall_confidence(self):
        """Test overall confidence assessment"""
        assessor = ConfidenceAssessor()
        
        # Mock CV results
        cv_results = Mock(spec=CVResult)
        cv_results.overall_confidence = 80.0
        cv_results.image_quality_score = 75.0
        cv_results.features = [Mock()] * 6  # 6 features
        cv_results.sections = [Mock()] * 3  # 3 sections
        cv_results.errors = []
        cv_results.warnings = []
        
        # Mock OCR results
        ocr_results = Mock(spec=OCRResult)
        ocr_results.overall_confidence = 70.0
        ocr_results.extracted_texts = [Mock(confidence=80)] * 5  # 5 texts
        ocr_results.errors = []
        ocr_results.warnings = []
        
        # Assess confidence
        assessment = assessor.assess_overall_confidence(cv_results, ocr_results)
        
        # Verify assessment structure
        assert 'overall_confidence' in assessment
        assert 'cv_confidence' in assessment
        assert 'ocr_confidence' in assessment
        assert 'reliability_factors' in assessment
        assert 'confidence_breakdown' in assessment
        
        # Verify confidence calculation
        assert assessment['cv_confidence'] == 80.0
        assert assessment['ocr_confidence'] == 70.0
        assert assessment['overall_confidence'] > 0
        
        # Verify reliability factors
        reliability = assessment['reliability_factors']
        assert reliability['sufficient_features'] is True
        assert reliability['sufficient_text'] is True
        assert reliability['good_image_quality'] is True
        assert reliability['low_error_count'] is True
    
    def test_generate_quality_report(self):
        """Test quality report generation"""
        assessor = ConfidenceAssessor()
        
        # Mock results
        cv_results = Mock(spec=CVResult)
        cv_results.image_quality_score = 85.0
        cv_results.features = [Mock()] * 8
        cv_results.sections = [Mock()] * 3
        cv_results.processing_time = 2.5
        cv_results.errors = []
        cv_results.warnings = []
        
        ocr_results = Mock(spec=OCRResult)
        ocr_results.overall_confidence = 75.0
        ocr_results.extracted_texts = [Mock()] * 6
        ocr_results.processing_time = 1.8
        ocr_results.errors = []
        ocr_results.warnings = []
        
        confidence_assessment = {'overall_confidence': 82.0}
        
        # Generate report
        report = assessor.generate_quality_report(cv_results, ocr_results, confidence_assessment)
        
        # Verify report structure
        assert 'overall_quality' in report
        assert 'quality_score' in report
        assert 'issues' in report
        assert 'recommendations' in report
        assert 'data_completeness' in report
        assert 'processing_summary' in report
        
        # Verify quality assessment
        assert report['overall_quality'] == 'excellent'
        assert report['quality_score'] == 82.0


class TestErrorAggregator:
    """Test error aggregation functionality"""
    
    def test_aggregate_errors(self):
        """Test error aggregation"""
        aggregator = ErrorAggregator()
        
        # Mock results with errors
        cv_results = Mock(spec=CVResult)
        cv_results.errors = ["Image processing failed", "Feature extraction error"]
        cv_results.warnings = ["Low image quality"]
        
        ocr_results = Mock(spec=OCRResult)
        ocr_results.errors = ["OCR text recognition failed"]
        ocr_results.warnings = ["Low confidence text detected"]
        
        # Aggregate errors
        aggregated = aggregator.aggregate_errors(cv_results, ocr_results)
        
        # Verify aggregation
        assert aggregated['total_errors'] == 3
        assert aggregated['total_warnings'] == 2
        assert 'error_categories' in aggregated
        assert 'critical_errors' in aggregated
        assert 'recoverable_errors' in aggregated
        assert 'user_friendly_messages' in aggregated
        
        # Verify categorization
        categories = aggregated['error_categories']
        assert len(categories['image_processing']) > 0
        assert len(categories['text_recognition']) > 0


class TestResultCacheManager:
    """Test result caching functionality"""
    
    def test_cache_and_retrieve_result(self, mock_redis):
        """Test caching and retrieving results"""
        cache_manager = ResultCacheManager(mock_redis)
        
        # Mock analysis output
        from backend.services.drawing_analysis_service import AnalysisOutput
        analysis_output = AnalysisOutput(
            design_id=1,
            cv_results={},
            ocr_results={},
            confidence_assessment={'overall_confidence': 75.0},
            quality_report={'overall_quality': 'good'},
            processing_time=5.2,
            errors=[],
            warnings=[],
            created_at="2025-01-18 10:30:00"
        )
        
        # Mock Redis operations
        mock_redis.setex.return_value = True
        mock_redis.get.return_value = json.dumps({
            'design_id': 1,
            'cv_results': {},
            'ocr_results': {},
            'confidence_assessment': {'overall_confidence': 75.0},
            'quality_report': {'overall_quality': 'good'},
            'processing_time': 5.2,
            'errors': [],
            'warnings': [],
            'created_at': "2025-01-18 10:30:00"
        })
        
        # Test caching
        success = cache_manager.cache_result(1, analysis_output)
        assert success is True
        
        # Test retrieval
        retrieved = cache_manager.get_cached_result(1)
        assert retrieved is not None
        assert retrieved.design_id == 1
        assert retrieved.confidence_assessment['overall_confidence'] == 75.0
    
    def test_progress_tracking(self, mock_redis):
        """Test progress tracking"""
        cache_manager = ResultCacheManager(mock_redis)
        
        # Create progress
        progress = AnalysisProgress(
            status=AnalysisStatus.CV_ANALYSIS,
            progress_percentage=50.0,
            current_stage="Analyzing features",
            estimated_time_remaining=30.0,
            errors=[],
            warnings=[]
        )
        
        # Mock Redis operations
        mock_redis.setex.return_value = True
        mock_redis.get.return_value = json.dumps({
            'status': 'cv_analysis',
            'progress_percentage': 50.0,
            'current_stage': "Analyzing features",
            'estimated_time_remaining': 30.0,
            'errors': [],
            'warnings': []
        })
        
        # Test progress update
        success = cache_manager.update_progress(1, progress)
        assert success is True
        
        # Test progress retrieval
        retrieved_progress = cache_manager.get_progress(1)
        assert retrieved_progress is not None
        assert retrieved_progress.status == AnalysisStatus.CV_ANALYSIS
        assert retrieved_progress.progress_percentage == 50.0


class TestDrawingAnalysisService:
    """Test main drawing analysis service"""
    
    @pytest.mark.asyncio
    async def test_start_analysis(self, analysis_service, sample_analysis_request, mock_redis, mock_celery):
        """Test starting analysis"""
        # Mock cache operations
        mock_redis.get.return_value = None  # No cached result
        mock_redis.setex.return_value = True
        
        # Mock Celery task
        mock_task = Mock()
        mock_task.id = "test-task-123"
        mock_celery.send_task.return_value = mock_task
        
        # Start analysis
        task_id = await analysis_service.start_analysis(sample_analysis_request)
        
        # Verify task was queued
        assert task_id == "test-task-123"
        mock_celery.send_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_perform_analysis(self, analysis_service, sample_analysis_request, test_image):
        """Test complete analysis performance"""
        # Mock the CV and OCR engines
        with patch.object(analysis_service.cv_engine, 'analyze_drawing') as mock_cv, \
             patch.object(analysis_service.ocr_extractor, 'extract_text_from_image') as mock_ocr, \
             patch.object(analysis_service, '_save_to_database', new_callable=AsyncMock) as mock_save:
            
            # Mock CV results
            mock_cv_result = Mock(spec=CVResult)
            mock_cv_result.overall_confidence = 80.0
            mock_cv_result.image_quality_score = 75.0
            mock_cv_result.features = []
            mock_cv_result.sections = []
            mock_cv_result.processing_time = 2.0
            mock_cv_result.errors = []
            mock_cv_result.warnings = []
            mock_cv.return_value = mock_cv_result
            
            # Mock OCR results
            mock_ocr_result = Mock(spec=OCRResult)
            mock_ocr_result.overall_confidence = 70.0
            mock_ocr_result.extracted_texts = []
            mock_ocr_result.processing_time = 1.5
            mock_ocr_result.errors = []
            mock_ocr_result.warnings = []
            mock_ocr.return_value = mock_ocr_result
            
            # Perform analysis
            result = await analysis_service.perform_analysis(sample_analysis_request)
            
            # Verify result
            assert result.design_id == sample_analysis_request.design_id
            assert result.processing_time > 0
            assert 'overall_confidence' in result.confidence_assessment
            assert 'overall_quality' in result.quality_report
            
            # Verify engines were called
            mock_cv.assert_called_once()
            mock_ocr.assert_called_once()
            mock_save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_analysis_status(self, analysis_service, mock_redis):
        """Test getting analysis status"""
        # Mock progress data
        progress_data = {
            'status': 'cv_analysis',
            'progress_percentage': 60.0,
            'current_stage': 'Extracting features',
            'estimated_time_remaining': 20.0,
            'errors': [],
            'warnings': []
        }
        mock_redis.get.return_value = json.dumps(progress_data)
        
        # Get status
        status = await analysis_service.get_analysis_status(1)
        
        # Verify status
        assert status is not None
        assert status.status == AnalysisStatus.CV_ANALYSIS
        assert status.progress_percentage == 60.0
        assert status.current_stage == 'Extracting features'


class TestIntegrationWorkflow:
    """Test complete integration workflow"""
    
    @pytest.mark.asyncio
    async def test_complete_analysis_workflow(self, test_image_file):
        """Test complete analysis workflow from start to finish"""
        # Create real instances (not mocked)
        mock_redis = Mock(spec=redis.Redis)
        mock_celery = Mock(spec=Celery)
        
        # Mock Redis operations
        mock_redis.get.return_value = None
        mock_redis.setex.return_value = True
        mock_redis.ping.return_value = True
        
        # Create service
        service = DrawingAnalysisService(mock_redis, mock_celery)
        
        # Create analysis request
        request = AnalysisRequest(
            design_id=1,
            file_path=test_image_file,
            user_id=1,
            analysis_options={}
        )
        
        # Mock database operations
        with patch.object(service, '_save_to_database', new_callable=AsyncMock) as mock_save:
            # Perform analysis
            result = await service.perform_analysis(request)
            
            # Verify result structure
            assert result.design_id == 1
            assert result.processing_time > 0
            assert isinstance(result.cv_results, dict)
            assert isinstance(result.ocr_results, dict)
            assert isinstance(result.confidence_assessment, dict)
            assert isinstance(result.quality_report, dict)
            
            # Verify confidence assessment
            confidence = result.confidence_assessment
            assert 'overall_confidence' in confidence
            assert 'cv_confidence' in confidence
            assert 'ocr_confidence' in confidence
            
            # Verify quality report
            quality = result.quality_report
            assert 'overall_quality' in quality
            assert 'quality_score' in quality
            assert 'issues' in quality
            assert 'recommendations' in quality
            
            # Verify database save was called
            mock_save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, analysis_service, mock_redis):
        """Test error handling in analysis workflow"""
        # Create request with invalid file path
        request = AnalysisRequest(
            design_id=1,
            file_path="/nonexistent/file.png",
            user_id=1,
            analysis_options={}
        )
        
        # Mock Redis operations
        mock_redis.setex.return_value = True
        
        # Perform analysis (should handle error gracefully)
        result = await analysis_service.perform_analysis(request)
        
        # Verify error handling
        assert result.design_id == 1
        assert len(result.errors) > 0
        assert result.confidence_assessment['overall_confidence'] == 0.0
        assert result.quality_report['overall_quality'] == 'failed'
    
    def test_cv_engine_integration(self, test_image):
        """Test computer vision engine integration"""
        cv_engine = ComputerVisionEngine()
        
        # Analyze test image
        result = cv_engine.analyze_drawing(test_image)
        
        # Verify result structure
        assert isinstance(result, CVResult)
        assert result.processing_time > 0
        assert result.image_quality_score > 0
        assert len(result.sections) > 0
        
        # Should detect some features in our test image
        assert len(result.features) > 0
    
    def test_ocr_engine_integration(self, test_image):
        """Test OCR engine integration"""
        ocr_extractor = OCRTextExtractor()
        
        # Extract text from test image
        result = ocr_extractor.extract_text_from_image(test_image)
        
        # Verify result structure
        assert isinstance(result, OCRResult)
        assert result.processing_time > 0
        
        # Should extract some text from our test image
        assert len(result.extracted_texts) > 0
        
        # Verify text content
        extracted_text = ' '.join([t.text for t in result.extracted_texts])
        assert 'PART' in extracted_text or 'STEEL' in extracted_text


if __name__ == '__main__':
    pytest.main([__file__, '-v'])