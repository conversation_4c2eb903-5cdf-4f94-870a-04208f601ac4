"""
Unit tests for OCR service functionality
"""

import pytest
import asyncio
import tempfile
import os
import cv2
import numpy as np
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from backend.services.ocr_service import OCRService, ManualCorrectionService
from processing.ocr.text_extractor import OCRResult, ExtractedText, TextType


class TestOCRService:
    """Test cases for OCRService class"""
    
    def setup_method(self):
        self.service = OCRService()
    
    def create_test_image_file(self, text: str = "TEST") -> str:
        """Create a temporary test image file"""
        image = np.ones((100, 200, 3), dtype=np.uint8) * 255
        cv2.putText(image, text, (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        cv2.imwrite(temp_file.name, image)
        temp_file.close()
        
        return temp_file.name
    
    @pytest.mark.asyncio
    async def test_process_image_file_success(self):
        """Test successful image file processing"""
        test_file = self.create_test_image_file("TEST TEXT")
        
        try:
            with patch.object(self.service.ocr_extractor, 'extract_text_from_image') as mock_extract:
                # Mock successful OCR result
                mock_result = OCRResult(
                    extracted_texts=[
                        ExtractedText(
                            text="TEST",
                            confidence=85.0,
                            bbox=(10, 20, 40, 20),
                            text_type=TextType.GENERAL
                        )
                    ],
                    overall_confidence=85.0,
                    processing_time=1.5,
                    errors=[],
                    warnings=[]
                )
                mock_extract.return_value = mock_result
                
                result = await self.service.process_image_file(test_file)
                
                assert 'source_file' in result
                assert 'extracted_texts' in result
                assert 'overall_confidence' in result
                assert 'image_dimensions' in result
                assert result['overall_confidence'] == 85.0
                assert len(result['extracted_texts']) == 1
                
        finally:
            os.unlink(test_file)
    
    @pytest.mark.asyncio
    async def test_process_image_file_invalid_path(self):
        """Test processing with invalid file path"""
        invalid_path = "/nonexistent/file.png"
        
        result = await self.service.process_image_file(invalid_path)
        
        assert 'errors' in result
        assert len(result['errors']) > 0
        assert result['overall_confidence'] == 0.0
        assert len(result['extracted_texts']) == 0
    
    @pytest.mark.asyncio
    async def test_extract_patterns_from_file_success(self):
        """Test successful pattern extraction from file"""
        test_file = self.create_test_image_file("2.50±0.01 ABC-123")
        
        try:
            with patch.object(self.service.ocr_extractor, 'extract_specific_patterns') as mock_extract:
                mock_patterns = {
                    'dimensions': ['2.50±0.01'],
                    'part_numbers': ['ABC-123'],
                    'materials': [],
                    'annotations': []
                }
                mock_extract.return_value = mock_patterns
                
                result = await self.service.extract_patterns_from_file(test_file)
                
                assert 'dimensions' in result
                assert 'part_numbers' in result
                assert 'materials' in result
                assert 'annotations' in result
                assert len(result['dimensions']) == 1
                assert len(result['part_numbers']) == 1
                
        finally:
            os.unlink(test_file)
    
    @pytest.mark.asyncio
    async def test_extract_patterns_from_file_error(self):
        """Test pattern extraction with file error"""
        invalid_path = "/nonexistent/file.png"
        
        result = await self.service.extract_patterns_from_file(invalid_path)
        
        assert all(len(result[key]) == 0 for key in ['dimensions', 'part_numbers', 'materials', 'annotations'])
    
    @pytest.mark.asyncio
    async def test_validate_ocr_results_high_quality(self):
        """Test validation of high quality OCR results"""
        ocr_results = {
            'extracted_texts': [
                {
                    'text': '2.50±0.01',
                    'confidence': 90.0,
                    'text_type': 'dimension'
                },
                {
                    'text': 'ABC-123',
                    'confidence': 85.0,
                    'text_type': 'part_number'
                },
                {
                    'text': 'STEEL A36',
                    'confidence': 88.0,
                    'text_type': 'material'
                }
            ],
            'overall_confidence': 87.5,
            'errors': []
        }

        result = await self.service.validate_ocr_results(ocr_results)

        assert result['is_valid'] is True
        assert result['quality_score'] == 87.5
        assert len(result['issues']) == 0
    
    @pytest.mark.asyncio
    async def test_validate_ocr_results_low_quality(self):
        """Test validation of low quality OCR results"""
        ocr_results = {
            'extracted_texts': [
                {
                    'text': 'unclear_text',
                    'confidence': 25.0,
                    'text_type': 'general'
                }
            ],
            'overall_confidence': 25.0,
            'errors': []
        }
        
        result = await self.service.validate_ocr_results(ocr_results)
        
        assert result['quality_score'] == 25.0
        assert len(result['issues']) > 0
        assert len(result['recommendations']) > 0
        assert any('low' in issue.lower() for issue in result['issues'])
    
    @pytest.mark.asyncio
    async def test_validate_ocr_results_with_errors(self):
        """Test validation of OCR results with errors"""
        ocr_results = {
            'extracted_texts': [],
            'overall_confidence': 0.0,
            'errors': ['OCR processing failed']
        }
        
        result = await self.service.validate_ocr_results(ocr_results)
        
        assert result['is_valid'] is False
        assert result['quality_score'] == 0.0
        assert len(result['issues']) > 0
    
    @pytest.mark.asyncio
    async def test_validate_ocr_results_no_text(self):
        """Test validation when no text is extracted"""
        ocr_results = {
            'extracted_texts': [],
            'overall_confidence': 0.0,
            'errors': []
        }
        
        result = await self.service.validate_ocr_results(ocr_results)
        
        assert result['quality_score'] == 0.0
        assert any('no text' in issue.lower() for issue in result['issues'])
        assert any('higher quality' in rec.lower() for rec in result['recommendations'])
    
    @pytest.mark.asyncio
    async def test_validate_ocr_results_missing_types(self):
        """Test validation when specific text types are missing"""
        ocr_results = {
            'extracted_texts': [
                {
                    'text': 'some text',
                    'confidence': 70.0,
                    'text_type': 'general'
                }
            ],
            'overall_confidence': 70.0,
            'errors': []
        }
        
        result = await self.service.validate_ocr_results(ocr_results)
        
        assert any('dimensions' in issue.lower() for issue in result['issues'])
        assert any('part numbers' in issue.lower() for issue in result['issues'])
        assert any('material' in issue.lower() for issue in result['issues'])


class TestManualCorrectionService:
    """Test cases for ManualCorrectionService class"""
    
    def setup_method(self):
        self.service = ManualCorrectionService()
    
    @pytest.mark.asyncio
    async def test_create_correction_session_success(self):
        """Test successful creation of correction session"""
        ocr_results = {
            'extracted_texts': [
                {
                    'text': 'unclear_text',
                    'confidence': 25.0,
                    'bbox': [10, 20, 40, 20],
                    'text_type': 'general'
                },
                {
                    'text': 'clear_text',
                    'confidence': 85.0,
                    'bbox': [50, 20, 40, 20],
                    'text_type': 'dimension'
                }
            ]
        }
        
        session = await self.service.create_correction_session(ocr_results)
        
        assert 'session_id' in session
        assert 'original_results' in session
        assert 'corrections_needed' in session
        assert session['status'] == 'pending'
        
        # Should only include low confidence text for correction
        assert len(session['corrections_needed']) == 1
        assert session['corrections_needed'][0]['original_text'] == 'unclear_text'
        assert session['corrections_needed'][0]['confidence'] == 25.0
    
    @pytest.mark.asyncio
    async def test_create_correction_session_no_corrections_needed(self):
        """Test correction session when no corrections are needed"""
        ocr_results = {
            'extracted_texts': [
                {
                    'text': 'clear_text',
                    'confidence': 85.0,
                    'bbox': [10, 20, 40, 20],
                    'text_type': 'dimension'
                }
            ]
        }
        
        session = await self.service.create_correction_session(ocr_results)
        
        assert len(session['corrections_needed']) == 0
    
    @pytest.mark.asyncio
    async def test_apply_corrections_success(self):
        """Test successful application of corrections"""
        session_id = "test-session-123"
        corrections = [
            {
                'original_text': 'unclear_text',
                'corrected_text': '2.50',
                'confidence': 25.0,
                'user_verified': True
            }
        ]
        
        result = await self.service.apply_corrections(session_id, corrections)
        
        assert 'session_id' in result
        assert 'corrections_applied' in result
        assert 'status' in result
        assert result['session_id'] == session_id
        assert result['corrections_applied'] == 1
        assert result['status'] == 'completed'
    
    def test_generate_suggestions(self):
        """Test generation of correction suggestions"""
        # Test common OCR error corrections
        suggestions = self.service._generate_suggestions("0CR")  # O mistaken for 0
        assert len(suggestions) > 0
        assert any('OCR' in suggestion for suggestion in suggestions)
        
        suggestions = self.service._generate_suggestions("1NCHES")  # I mistaken for 1
        assert len(suggestions) > 0
        assert any('INCHES' in suggestion for suggestion in suggestions)
    
    def test_generate_session_id(self):
        """Test session ID generation"""
        session_id = self.service._generate_session_id()
        
        assert isinstance(session_id, str)
        assert len(session_id) > 0
        
        # Should generate unique IDs
        another_id = self.service._generate_session_id()
        assert session_id != another_id


class TestOCRServiceIntegration:
    """Integration tests for OCR service functionality"""
    
    @pytest.mark.asyncio
    async def test_full_ocr_workflow(self):
        """Test complete OCR workflow from file to validation"""
        service = OCRService()
        
        # Create test image file
        image = np.ones((200, 400, 3), dtype=np.uint8) * 255
        cv2.putText(image, "2.50±0.01", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(image, "ABC-123", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        cv2.imwrite(temp_file.name, image)
        temp_file.close()
        
        try:
            # Mock the OCR extractor to avoid dependency on Tesseract
            with patch.object(service.ocr_extractor, 'extract_text_from_image') as mock_extract:
                mock_result = OCRResult(
                    extracted_texts=[
                        ExtractedText(
                            text="2.50±0.01",
                            confidence=85.0,
                            bbox=(50, 80, 80, 20),
                            text_type=TextType.DIMENSION
                        ),
                        ExtractedText(
                            text="ABC-123",
                            confidence=90.0,
                            bbox=(50, 130, 70, 20),
                            text_type=TextType.PART_NUMBER
                        )
                    ],
                    overall_confidence=87.5,
                    processing_time=1.2,
                    errors=[],
                    warnings=[]
                )
                mock_extract.return_value = mock_result
                
                # Process image
                ocr_results = await service.process_image_file(temp_file.name)
                
                # Validate results
                validation = await service.validate_ocr_results(ocr_results)
                
                assert ocr_results['overall_confidence'] == 87.5
                assert len(ocr_results['extracted_texts']) == 2
                assert validation['is_valid'] is True
                assert validation['quality_score'] == 87.5
                
        finally:
            os.unlink(temp_file.name)
    
    @pytest.mark.asyncio
    async def test_correction_workflow(self):
        """Test manual correction workflow"""
        correction_service = ManualCorrectionService()
        
        # Create OCR results with low confidence text
        ocr_results = {
            'extracted_texts': [
                {
                    'text': 'Z.S0',  # Unclear dimension
                    'confidence': 30.0,
                    'bbox': [10, 20, 40, 20],
                    'text_type': 'dimension'
                }
            ],
            'overall_confidence': 30.0,
            'errors': []
        }
        
        # Create correction session
        session = await correction_service.create_correction_session(ocr_results)
        
        assert len(session['corrections_needed']) == 1
        
        # Apply corrections
        corrections = [
            {
                'original_text': 'Z.S0',
                'corrected_text': '2.50',
                'confidence': 30.0,
                'user_verified': True
            }
        ]
        
        result = await correction_service.apply_corrections(session['session_id'], corrections)
        
        assert result['corrections_applied'] == 1
        assert result['status'] == 'completed'