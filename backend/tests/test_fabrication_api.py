"""
Tests for fabrication progress tracking API endpoints
"""
import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from models.user import User
from models.design import Design, BOMItem
from models.fabrication import FabricationStage, FabricationTeam, PartFabricationProgress
from services.auth_service import AuthService


@pytest.fixture
def test_design_with_bom(db_session: Session, test_user: User):
    """Create a test design with BOM items"""
    design = Design(
        user_id=test_user.id,
        name="Test Fabrication Design",
        original_filename="test.pdf",
        status="uploaded"
    )
    db_session.add(design)
    db_session.commit()
    db_session.refresh(design)
    
    # Add BOM items
    bom_items = [
        BOMItem(
            design_id=design.id,
            part_number="PART-001",
            description="Main Housing",
            quantity=1,
            material="Steel A36",
            volume=100.0,
            weight=785.0
        ),
        BOMItem(
            design_id=design.id,
            part_number="PART-002",
            description="Cover Plate",
            quantity=2,
            material="Aluminum 6061",
            volume=50.0,
            weight=135.0
        )
    ]
    
    for item in bom_items:
        db_session.add(item)
    
    db_session.commit()
    return design


@pytest.fixture
def auth_headers(user_token):
    """Get authentication headers for test user"""
    return {"Authorization": f"Bearer {user_token}"}


@pytest.fixture
def admin_auth_headers(admin_token):
    """Get authentication headers for admin user"""
    return {"Authorization": f"Bearer {admin_token}"}


class TestFabricationStages:
    """Test fabrication stage management"""
    
    def test_get_fabrication_stages(self, auth_headers):
        """Test getting fabrication stages"""
        response = client.get("/api/fabrication/stages", headers=auth_headers)
        assert response.status_code == 200
        
        stages = response.json()
        assert isinstance(stages, list)
        assert len(stages) > 0
        
        # Check default stages exist
        stage_names = [stage["name"] for stage in stages]
        assert "Not Started" in stage_names
        assert "Completed" in stage_names
    
    def test_create_custom_stage(self, auth_headers):
        """Test creating a custom fabrication stage"""
        stage_data = {
            "name": "Custom Welding",
            "description": "Custom welding operations",
            "order_index": 10
        }
        
        response = client.post("/api/fabrication/stages", json=stage_data, headers=auth_headers)
        assert response.status_code == 200
        
        stage = response.json()
        assert stage["name"] == "Custom Welding"
        assert stage["description"] == "Custom welding operations"
        assert stage["order_index"] == 10
        assert stage["is_default"] == False
    
    def test_update_fabrication_stage(self, auth_headers, test_db: Session, test_user: User):
        """Test updating a fabrication stage"""
        # Create a custom stage first
        stage = FabricationStage(
            name="Test Stage",
            description="Test description",
            order_index=15,
            is_default=False,
            created_by=test_user.id
        )
        test_db.add(stage)
        test_db.commit()
        test_db.refresh(stage)
        
        update_data = {
            "name": "Updated Stage",
            "description": "Updated description"
        }
        
        response = client.put(f"/api/fabrication/stages/{stage.id}", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        
        updated_stage = response.json()
        assert updated_stage["name"] == "Updated Stage"
        assert updated_stage["description"] == "Updated description"
    
    def test_delete_fabrication_stage(self, auth_headers, test_db: Session, test_user: User):
        """Test deleting a custom fabrication stage"""
        # Create a custom stage first
        stage = FabricationStage(
            name="Delete Me",
            description="Stage to be deleted",
            order_index=20,
            is_default=False,
            created_by=test_user.id
        )
        test_db.add(stage)
        test_db.commit()
        test_db.refresh(stage)
        
        response = client.delete(f"/api/fabrication/stages/{stage.id}", headers=auth_headers)
        assert response.status_code == 200
        assert response.json()["message"] == "Stage deleted successfully"
    
    def test_cannot_delete_default_stage(self, auth_headers, test_db: Session):
        """Test that default stages cannot be deleted"""
        # Get a default stage
        default_stage = test_db.query(FabricationStage).filter(
            FabricationStage.is_default == True
        ).first()
        
        response = client.delete(f"/api/fabrication/stages/{default_stage.id}", headers=auth_headers)
        assert response.status_code == 404


class TestFabricationTeams:
    """Test fabrication team management"""
    
    def test_get_fabrication_teams(self, auth_headers):
        """Test getting fabrication teams"""
        response = client.get("/api/fabrication/teams", headers=auth_headers)
        assert response.status_code == 200
        
        teams = response.json()
        assert isinstance(teams, list)
    
    def test_create_fabrication_team(self, auth_headers, test_user: User):
        """Test creating a fabrication team"""
        team_data = {
            "name": "Welding Team",
            "description": "Specialized welding operations",
            "lead_user_id": test_user.id
        }
        
        response = client.post("/api/fabrication/teams", json=team_data, headers=auth_headers)
        assert response.status_code == 200
        
        team = response.json()
        assert team["name"] == "Welding Team"
        assert team["description"] == "Specialized welding operations"
        assert team["lead_user_id"] == test_user.id
    
    def test_add_team_member(self, auth_headers, test_db: Session, test_user: User):
        """Test adding a member to a team"""
        # Create a team first
        team = FabricationTeam(
            name="Test Team",
            description="Test team",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        # Create another user to add as member
        member_user = create_test_user(test_db, "member_user", "<EMAIL>")
        
        member_data = {
            "user_id": member_user.id,
            "role": "Welder"
        }
        
        response = client.post(f"/api/fabrication/teams/{team.id}/members", json=member_data, headers=auth_headers)
        assert response.status_code == 200
        assert response.json()["message"] == "Member added successfully"
    
    def test_remove_team_member(self, auth_headers, test_db: Session, test_user: User):
        """Test removing a member from a team"""
        # Create a team and add a member
        team = FabricationTeam(
            name="Test Team",
            description="Test team",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        member_user = create_test_user(test_db, "remove_user", "<EMAIL>")
        
        # Add member first
        client.post(f"/api/fabrication/teams/{team.id}/members", 
                   json={"user_id": member_user.id}, headers=auth_headers)
        
        # Remove member
        response = client.delete(f"/api/fabrication/teams/{team.id}/members/{member_user.id}", headers=auth_headers)
        assert response.status_code == 200
        assert response.json()["message"] == "Member removed successfully"


class TestProgressTracking:
    """Test fabrication progress tracking"""
    
    def test_get_design_progress(self, auth_headers, test_design_with_bom: Design):
        """Test getting design progress"""
        response = client.get(f"/api/fabrication/{test_design_with_bom.id}/progress", headers=auth_headers)
        assert response.status_code == 200
        
        progress = response.json()
        assert progress["design_id"] == test_design_with_bom.id
        assert progress["total_parts"] == 2
        assert "parts_progress" in progress
        assert len(progress["parts_progress"]) == 2
    
    def test_update_part_status(self, auth_headers, test_design_with_bom: Design, test_db: Session):
        """Test updating part fabrication status"""
        # Get a BOM item
        bom_item = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).first()
        
        # Get a fabrication stage
        stage = test_db.query(FabricationStage).filter(FabricationStage.name == "Machining").first()
        
        status_data = {
            "stage_id": stage.id,
            "notes": "Started machining operations",
            "estimated_completion": (datetime.utcnow() + timedelta(days=3)).isoformat()
        }
        
        response = client.put(
            f"/api/fabrication/{test_design_with_bom.id}/parts/{bom_item.id}/status",
            json=status_data,
            headers=auth_headers
        )
        assert response.status_code == 200
        
        updated_progress = response.json()
        assert updated_progress["current_stage"]["id"] == stage.id
        assert updated_progress["notes"] == "Started machining operations"
    
    def test_assign_part(self, auth_headers, test_design_with_bom: Design, test_db: Session, test_user: User):
        """Test assigning a part to a user"""
        # Get a BOM item
        bom_item = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).first()
        
        assignment_data = {
            "assigned_to": test_user.id
        }
        
        response = client.put(
            f"/api/fabrication/{test_design_with_bom.id}/parts/{bom_item.id}/assignment",
            json=assignment_data,
            headers=auth_headers
        )
        assert response.status_code == 200
        
        assigned_progress = response.json()
        assert assigned_progress["assigned_to"] == test_user.id
        assert assigned_progress["assigned_user_name"] == test_user.username
    
    def test_get_timeline_data(self, auth_headers, test_design_with_bom: Design):
        """Test getting timeline data for Gantt chart"""
        response = client.get(f"/api/fabrication/{test_design_with_bom.id}/timeline", headers=auth_headers)
        assert response.status_code == 200
        
        timeline = response.json()
        assert timeline["design_id"] == test_design_with_bom.id
        assert "timeline_data" in timeline
        assert len(timeline["timeline_data"]) == 2
        
        # Check timeline item structure
        timeline_item = timeline["timeline_data"][0]
        assert "part_id" in timeline_item
        assert "part_number" in timeline_item
        assert "current_stage" in timeline_item
        assert "stage_history" in timeline_item
    
    def test_get_progress_reports(self, auth_headers, test_design_with_bom: Design):
        """Test getting progress reports"""
        response = client.get(f"/api/fabrication/{test_design_with_bom.id}/reports", headers=auth_headers)
        assert response.status_code == 200
        
        reports = response.json()
        assert "stage_distribution" in reports
        assert "overdue_parts" in reports
        assert "completion_by_day" in reports
        assert "total_parts" in reports
        assert reports["total_parts"] == 2
    
    def test_get_all_progress_reports(self, auth_headers):
        """Test getting progress reports for all designs"""
        response = client.get("/api/fabrication/reports", headers=auth_headers)
        assert response.status_code == 200
        
        reports = response.json()
        assert "stage_distribution" in reports
        assert "overdue_parts" in reports
        assert "total_parts" in reports
    
    def test_get_dashboard_overview(self, auth_headers):
        """Test getting dashboard overview"""
        response = client.get("/api/fabrication/dashboard", headers=auth_headers)
        assert response.status_code == 200
        
        dashboard = response.json()
        assert "projects" in dashboard
        assert "overall_stats" in dashboard
        
        overall_stats = dashboard["overall_stats"]
        assert "total_projects" in overall_stats
        assert "total_parts" in overall_stats
        assert "completed_parts" in overall_stats
        assert "overall_completion" in overall_stats


class TestAuthentication:
    """Test authentication requirements"""
    
    def test_requires_authentication(self):
        """Test that endpoints require authentication"""
        # Test without auth headers
        response = client.get("/api/fabrication/stages")
        assert response.status_code == 401
        
        response = client.get("/api/fabrication/teams")
        assert response.status_code == 401
        
        response = client.get("/api/fabrication/dashboard")
        assert response.status_code == 401


class TestErrorHandling:
    """Test error handling"""
    
    def test_invalid_design_id(self, auth_headers):
        """Test handling of invalid design ID"""
        response = client.get("/api/fabrication/99999/progress", headers=auth_headers)
        assert response.status_code == 404
    
    def test_invalid_stage_id(self, auth_headers):
        """Test handling of invalid stage ID"""
        response = client.put("/api/fabrication/stages/99999", 
                            json={"name": "Updated"}, headers=auth_headers)
        assert response.status_code == 404
    
    def test_invalid_team_member(self, auth_headers):
        """Test removing non-existent team member"""
        response = client.delete("/api/fabrication/teams/1/members/99999", headers=auth_headers)
        assert response.status_code == 404