"""
Tests for admin service layer.
"""
import pytest
from datetime import datetime, timedelta

from services.admin_service import AdminService
from models.material import Material
from models.admin import ErrorLog, UserActivityLog
from models.user import UserSession


@pytest.fixture
def admin_service(db_session):
    """Create an AdminService instance for testing."""
    return AdminService(db_session)


def test_get_system_statistics(admin_service, test_user, admin_user):
    """Test getting system statistics."""
    stats = admin_service.get_system_statistics()
    
    # Check all required fields are present
    required_fields = [
        "total_users", "active_users", "admin_users",
        "total_designs", "designs_this_month",
        "total_analyses", "successful_analyses", "failed_analyses",
        "total_storage_mb", "avg_processing_time_seconds"
    ]
    
    for field in required_fields:
        assert field in stats
        assert isinstance(stats[field], (int, float))
    
    # Verify user counts (should have at least the test users)
    assert stats["total_users"] >= 2
    assert stats["active_users"] >= 2
    assert stats["admin_users"] >= 1


def test_get_user_activity_logs(admin_service, test_user, db_session):
    """Test getting user activity logs."""
    # Create test activity log
    activity_log = UserActivityLog(
        user_id=test_user.id,
        action="login",
        resource_type="session",
        resource_id=1,
        ip_address="127.0.0.1"
    )
    db_session.add(activity_log)
    db_session.commit()
    
    # Get all logs
    logs = admin_service.get_user_activity_logs()
    assert len(logs) >= 1
    
    # Test filtering by user
    logs = admin_service.get_user_activity_logs(user_id=test_user.id)
    assert len(logs) >= 1
    assert all(log["user_id"] == test_user.id for log in logs)
    
    # Test filtering by action
    logs = admin_service.get_user_activity_logs(action="login")
    assert len(logs) >= 1
    assert all(log["action"] == "login" for log in logs)
    
    # Test pagination
    logs = admin_service.get_user_activity_logs(skip=0, limit=1)
    assert len(logs) <= 1


def test_get_system_health_metrics(admin_service):
    """Test getting system health metrics."""
    metrics = admin_service.get_system_health_metrics()
    
    assert isinstance(metrics, list)
    assert len(metrics) > 0
    
    # Check metric structure
    for metric in metrics:
        assert "metric_name" in metric
        assert "value" in metric
        assert "unit" in metric
        assert "status" in metric
        assert "last_updated" in metric
        assert metric["status"] in ["healthy", "warning", "critical"]
    
    # Check for expected metrics
    metric_names = [metric["metric_name"] for metric in metrics]
    expected_metrics = ["Database Connection", "Active Sessions"]
    
    for expected in expected_metrics:
        assert expected in metric_names


def test_get_error_logs(admin_service, test_user, db_session):
    """Test getting error logs."""
    # Create test error logs
    error_log1 = ErrorLog(
        error_type="ValidationError",
        error_message="Test validation error",
        user_id=test_user.id,
        endpoint="/api/test",
        resolved=False
    )
    
    error_log2 = ErrorLog(
        error_type="ProcessingError",
        error_message="Test processing error",
        resolved=True
    )
    
    db_session.add_all([error_log1, error_log2])
    db_session.commit()
    
    # Get all error logs
    logs = admin_service.get_error_logs()
    assert len(logs) >= 2
    
    # Test filtering by error type
    logs = admin_service.get_error_logs(error_type="ValidationError")
    assert len(logs) >= 1
    assert all(log["error_type"] == "ValidationError" for log in logs)
    
    # Test filtering by resolved status
    logs = admin_service.get_error_logs(resolved=False)
    assert len(logs) >= 1
    assert all(not log["resolved"] for log in logs)
    
    logs = admin_service.get_error_logs(resolved=True)
    assert len(logs) >= 1
    assert all(log["resolved"] for log in logs)
    
    # Test pagination
    logs = admin_service.get_error_logs(skip=0, limit=1)
    assert len(logs) <= 1


def test_resolve_error_log(admin_service, db_session):
    """Test resolving error log entries."""
    # Create test error log
    error_log = ErrorLog(
        error_type="TestError",
        error_message="Test error message",
        resolved=False
    )
    db_session.add(error_log)
    db_session.commit()
    db_session.refresh(error_log)
    
    # Resolve the error
    success = admin_service.resolve_error_log(error_log.id)
    assert success is True
    
    # Verify it's resolved
    db_session.refresh(error_log)
    assert error_log.resolved is True
    assert error_log.resolved_at is not None
    
    # Test resolving non-existent error
    success = admin_service.resolve_error_log(99999)
    assert success is False


def test_materials_crud(admin_service):
    """Test materials CRUD operations."""
    # Create material
    material = admin_service.create_material(
        name="Test Steel",
        density=7850.0,
        category="Steel",
        description="Test steel material"
    )
    
    assert material.name == "Test Steel"
    assert material.density == 7850.0
    assert material.category == "Steel"
    assert material.description == "Test steel material"
    
    # Get materials
    materials = admin_service.get_materials()
    assert len(materials) >= 1
    
    material_names = [m.name for m in materials]
    assert "Test Steel" in material_names
    
    # Update material
    updated_material = admin_service.update_material(
        material_id=material.id,
        name="Updated Test Steel",
        density=7900.0
    )
    
    assert updated_material is not None
    assert updated_material.name == "Updated Test Steel"
    assert updated_material.density == 7900.0
    assert updated_material.category == "Steel"  # Unchanged
    
    # Delete material
    success = admin_service.delete_material(material.id)
    assert success is True
    
    # Verify deletion
    materials = admin_service.get_materials()
    material_names = [m.name for m in materials]
    assert "Updated Test Steel" not in material_names
    
    # Test deleting non-existent material
    success = admin_service.delete_material(99999)
    assert success is False


def test_materials_search_and_filter(admin_service):
    """Test materials search and filtering."""
    # Create test materials
    materials_data = [
        ("Steel A36", 7850.0, "Steel", "Structural steel"),
        ("Aluminum 6061", 2700.0, "Aluminum", "Structural aluminum"),
        ("Stainless Steel 304", 8000.0, "Steel", "Corrosion resistant")
    ]
    
    for name, density, category, description in materials_data:
        admin_service.create_material(
            name=name,
            density=density,
            category=category,
            description=description
        )
    
    # Test category filter
    steel_materials = admin_service.get_materials(category="Steel")
    assert len(steel_materials) == 2
    assert all(m.category == "Steel" for m in steel_materials)
    
    # Test search by name
    aluminum_materials = admin_service.get_materials(search="aluminum")
    assert len(aluminum_materials) == 1
    assert "Aluminum" in aluminum_materials[0].name
    
    # Test search by description
    structural_materials = admin_service.get_materials(search="structural")
    assert len(structural_materials) == 2
    
    # Test pagination
    materials = admin_service.get_materials(skip=0, limit=2)
    assert len(materials) <= 2
    
    materials = admin_service.get_materials(skip=1, limit=2)
    assert len(materials) <= 2


def test_material_validation(admin_service):
    """Test material creation and update validation."""
    # Test invalid density
    with pytest.raises(ValueError, match="Density must be positive"):
        admin_service.create_material(
            name="Invalid Material",
            density=-100.0,
            category="Test"
        )
    
    # Test empty name
    with pytest.raises(ValueError, match="Material name cannot be empty"):
        admin_service.create_material(
            name="",
            density=1000.0,
            category="Test"
        )
    
    # Test duplicate name
    admin_service.create_material(
        name="Unique Material",
        density=1000.0,
        category="Test"
    )
    
    with pytest.raises(ValueError, match="Material with this name already exists"):
        admin_service.create_material(
            name="Unique Material",
            density=2000.0,
            category="Test"
        )
    
    # Test update validation
    material = admin_service.create_material(
        name="Update Test",
        density=1000.0,
        category="Test"
    )
    
    # Test invalid density update
    with pytest.raises(ValueError, match="Density must be positive"):
        admin_service.update_material(
            material_id=material.id,
            density=-100.0
        )
    
    # Test empty name update
    with pytest.raises(ValueError, match="Material name cannot be empty"):
        admin_service.update_material(
            material_id=material.id,
            name=""
        )


def test_cleanup_system_data(admin_service, test_user, db_session):
    """Test system data cleanup."""
    # Create expired session
    expired_session = UserSession(
        user_id=test_user.id,
        session_token="expired_token",
        expires_at=datetime.utcnow() - timedelta(days=1)
    )
    db_session.add(expired_session)
    db_session.commit()
    
    # Run cleanup
    results = admin_service.cleanup_system_data()
    
    assert isinstance(results, dict)
    assert "expired_sessions_cleaned" in results
    assert results["expired_sessions_cleaned"] >= 1
    
    # Verify expired session was cleaned
    remaining_sessions = db_session.query(UserSession).filter(
        UserSession.session_token == "expired_token"
    ).count()
    assert remaining_sessions == 0