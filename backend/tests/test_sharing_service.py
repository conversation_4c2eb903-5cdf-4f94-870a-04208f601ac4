"""
Tests for sharing service functionality.
"""
import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from fastapi import HTTPException

from services.sharing_service import SharingService
from models.sharing import DesignShare
from models.design import Design
from models.user import User


class TestSharingService:
    """Test cases for SharingService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.db = Mock(spec=Session)
        self.service = SharingService(self.db)
        
        # Mock users
        self.owner = User(
            id=1,
            username="owner",
            email="<EMAIL>",
            password_hash="hashed",
            is_active=True
        )
        
        self.shared_user = User(
            id=2,
            username="shared_user",
            email="<EMAIL>",
            password_hash="hashed",
            is_active=True
        )
        
        # Mock design
        self.design = Design(
            id=1,
            user_id=1,
            name="Test Design",
            status="completed"
        )
    
    def test_grant_access_success(self):
        """Test successful access granting."""
        # Setup mocks
        self.db.query.return_value.filter.return_value.first.side_effect = [
            self.design,  # Design exists
            self.shared_user,  # User to share with exists
            None  # No existing share
        ]
        
        # Mock database operations
        self.db.add = Mock()
        self.db.commit = Mock()
        self.db.refresh = Mock()
        
        # Create expected share
        expected_share = DesignShare(
            id=1,
            design_id=1,
            owner_id=1,
            shared_with_id=2,
            permission_level="view"
        )
        self.db.refresh.side_effect = lambda obj: setattr(obj, 'id', 1)
        
        # Execute
        result = self.service.grant_access(
            design_id=1,
            owner_id=1,
            shared_with_email="<EMAIL>",
            permission_level="view"
        )
        
        # Verify
        self.db.add.assert_called_once()
        self.db.commit.assert_called_once()
        assert result.design_id == 1
        assert result.owner_id == 1
        assert result.shared_with_id == 2
        assert result.permission_level == "view"
    
    def test_grant_access_invalid_permission(self):
        """Test granting access with invalid permission level."""
        with pytest.raises(HTTPException) as exc_info:
            self.service.grant_access(
                design_id=1,
                owner_id=1,
                shared_with_email="<EMAIL>",
                permission_level="invalid"
            )
        
        assert exc_info.value.status_code == 400
        assert "Permission level must be 'view' or 'edit'" in str(exc_info.value.detail)
    
    def test_grant_access_design_not_found(self):
        """Test granting access to non-existent design."""
        self.db.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(HTTPException) as exc_info:
            self.service.grant_access(
                design_id=999,
                owner_id=1,
                shared_with_email="<EMAIL>",
                permission_level="view"
            )
        
        assert exc_info.value.status_code == 404
        assert "Design not found" in str(exc_info.value.detail)
    
    def test_grant_access_not_owner(self):
        """Test granting access when user doesn't own design."""
        # Design exists but owned by different user
        wrong_design = Design(id=1, user_id=999, name="Test", status="completed")
        self.db.query.return_value.filter.return_value.first.return_value = wrong_design
        
        with pytest.raises(HTTPException) as exc_info:
            self.service.grant_access(
                design_id=1,
                owner_id=1,
                shared_with_email="<EMAIL>",
                permission_level="view"
            )
        
        assert exc_info.value.status_code == 403
        assert "You can only share your own designs" in str(exc_info.value.detail)
    
    def test_grant_access_user_not_found(self):
        """Test granting access to non-existent user."""
        self.db.query.return_value.filter.return_value.first.side_effect = [
            self.design,  # Design exists
            None  # User to share with doesn't exist
        ]
        
        with pytest.raises(HTTPException) as exc_info:
            self.service.grant_access(
                design_id=1,
                owner_id=1,
                shared_with_email="<EMAIL>",
                permission_level="view"
            )
        
        assert exc_info.value.status_code == 404
        assert "User not found" in str(exc_info.value.detail)
    
    def test_grant_access_self_share(self):
        """Test preventing user from sharing with themselves."""
        self.db.query.return_value.filter.return_value.first.side_effect = [
            self.design,  # Design exists
            self.owner  # User trying to share with themselves
        ]
        
        with pytest.raises(HTTPException) as exc_info:
            self.service.grant_access(
                design_id=1,
                owner_id=1,
                shared_with_email="<EMAIL>",
                permission_level="view"
            )
        
        assert exc_info.value.status_code == 400
        assert "Cannot share design with yourself" in str(exc_info.value.detail)
    
    def test_grant_access_update_existing(self):
        """Test updating existing share permission."""
        existing_share = DesignShare(
            id=1,
            design_id=1,
            owner_id=1,
            shared_with_id=2,
            permission_level="view"
        )
        
        self.db.query.return_value.filter.return_value.first.side_effect = [
            self.design,  # Design exists
            self.shared_user,  # User to share with exists
            existing_share  # Existing share
        ]
        
        self.db.commit = Mock()
        self.db.refresh = Mock()
        
        # Execute
        result = self.service.grant_access(
            design_id=1,
            owner_id=1,
            shared_with_email="<EMAIL>",
            permission_level="edit"
        )
        
        # Verify
        assert existing_share.permission_level == "edit"
        self.db.commit.assert_called_once()
        self.db.refresh.assert_called_once_with(existing_share)
    
    def test_revoke_access_success(self):
        """Test successful access revocation."""
        existing_share = DesignShare(
            id=1,
            design_id=1,
            owner_id=1,
            shared_with_id=2,
            permission_level="view"
        )
        
        self.db.query.return_value.filter.return_value.first.side_effect = [
            self.design,  # Design exists
            existing_share  # Share exists
        ]
        
        self.db.delete = Mock()
        self.db.commit = Mock()
        
        # Execute
        result = self.service.revoke_access(
            design_id=1,
            owner_id=1,
            shared_with_id=2
        )
        
        # Verify
        assert result is True
        self.db.delete.assert_called_once_with(existing_share)
        self.db.commit.assert_called_once()
    
    def test_revoke_access_share_not_found(self):
        """Test revoking non-existent share."""
        self.db.query.return_value.filter.return_value.first.side_effect = [
            self.design,  # Design exists
            None  # Share doesn't exist
        ]
        
        with pytest.raises(HTTPException) as exc_info:
            self.service.revoke_access(
                design_id=1,
                owner_id=1,
                shared_with_id=2
            )
        
        assert exc_info.value.status_code == 404
        assert "Share not found" in str(exc_info.value.detail)
    
    def test_get_design_shares_success(self):
        """Test getting design shares successfully."""
        shares = [
            DesignShare(id=1, design_id=1, owner_id=1, shared_with_id=2, permission_level="view"),
            DesignShare(id=2, design_id=1, owner_id=1, shared_with_id=3, permission_level="edit")
        ]
        
        users = [
            User(id=2, username="user2", email="<EMAIL>"),
            User(id=3, username="user3", email="<EMAIL>")
        ]
        
        self.db.query.return_value.filter.return_value.first.return_value = self.design
        self.db.query.return_value.filter.return_value.all.return_value = shares
        
        # Mock user queries
        def mock_user_query(user_id):
            return next((u for u in users if u.id == user_id), None)
        
        with patch.object(self.db, 'query') as mock_query:
            # Setup complex mock for multiple queries
            mock_query.return_value.filter.return_value.first.side_effect = [
                self.design,  # Design query
                users[0],     # First user query
                users[1]      # Second user query
            ]
            mock_query.return_value.filter.return_value.all.return_value = shares
            
            # Execute
            result = self.service.get_design_shares(design_id=1, user_id=1)
            
            # Verify
            assert len(result) == 2
            assert result[0]["username"] == "user2"
            assert result[0]["permission_level"] == "view"
            assert result[1]["username"] == "user3"
            assert result[1]["permission_level"] == "edit"
    
    def test_get_shared_with_me_success(self):
        """Test getting designs shared with user."""
        shares = [
            DesignShare(id=1, design_id=1, owner_id=2, shared_with_id=1, permission_level="view")
        ]
        
        design = Design(id=1, user_id=2, name="Shared Design", status="completed")
        owner = User(id=2, username="owner", email="<EMAIL>")
        
        self.db.query.return_value.filter.return_value.all.return_value = shares
        
        with patch.object(self.db, 'query') as mock_query:
            mock_query.return_value.filter.return_value.all.return_value = shares
            mock_query.return_value.filter.return_value.first.side_effect = [design, owner]
            
            # Execute
            result = self.service.get_shared_with_me(user_id=1)
            
            # Verify
            assert len(result) == 1
            assert result[0]["design_name"] == "Shared Design"
            assert result[0]["owner_username"] == "owner"
            assert result[0]["permission_level"] == "view"
    
    def test_check_access_owner(self):
        """Test checking access for design owner."""
        self.db.query.return_value.filter.return_value.first.return_value = self.design
        
        result = self.service.check_access(design_id=1, user_id=1)
        
        assert result == "owner"
    
    def test_check_access_shared_user(self):
        """Test checking access for shared user."""
        share = DesignShare(
            id=1,
            design_id=1,
            owner_id=2,
            shared_with_id=1,
            permission_level="edit"
        )
        
        design = Design(id=1, user_id=2, name="Test", status="completed")
        
        self.db.query.return_value.filter.return_value.first.side_effect = [
            design,  # Design exists but owned by different user
            share    # Share exists
        ]
        
        result = self.service.check_access(design_id=1, user_id=1)
        
        assert result == "edit"
    
    def test_check_access_no_access(self):
        """Test checking access for user with no access."""
        design = Design(id=1, user_id=2, name="Test", status="completed")
        
        self.db.query.return_value.filter.return_value.first.side_effect = [
            design,  # Design exists but owned by different user
            None     # No share exists
        ]
        
        result = self.service.check_access(design_id=1, user_id=1)
        
        assert result is None
    
    def test_search_users_success(self):
        """Test searching users successfully."""
        users = [
            User(id=2, username="testuser", email="<EMAIL>", is_active=True),
            User(id=3, username="another", email="<EMAIL>", is_active=True)
        ]
        
        self.db.query.return_value.filter.return_value.limit.return_value.all.return_value = users
        
        result = self.service.search_users(query="test", current_user_id=1)
        
        assert len(result) == 2
        assert result[0]["username"] == "testuser"
        assert result[1]["username"] == "another"
    
    def test_search_users_short_query(self):
        """Test searching users with short query."""
        result = self.service.search_users(query="a", current_user_id=1)
        
        assert result == []
    
    def test_update_permission_success(self):
        """Test updating permission successfully."""
        share = DesignShare(
            id=1,
            design_id=1,
            owner_id=1,
            shared_with_id=2,
            permission_level="view"
        )
        
        self.db.query.return_value.filter.return_value.first.side_effect = [
            self.design,  # Design exists
            share         # Share exists
        ]
        
        self.db.commit = Mock()
        self.db.refresh = Mock()
        
        # Execute
        result = self.service.update_permission(
            design_id=1,
            owner_id=1,
            shared_with_id=2,
            permission_level="edit"
        )
        
        # Verify
        assert share.permission_level == "edit"
        self.db.commit.assert_called_once()
        self.db.refresh.assert_called_once_with(share)
    
    def test_get_sharing_history_success(self):
        """Test getting sharing history successfully."""
        shares = [
            DesignShare(id=1, design_id=1, owner_id=1, shared_with_id=2, permission_level="view")
        ]
        
        user = User(id=2, username="user2", email="<EMAIL>")
        
        self.db.query.return_value.filter.return_value.first.return_value = self.design
        self.db.query.return_value.filter.return_value.all.return_value = shares
        
        with patch.object(self.db, 'query') as mock_query:
            mock_query.return_value.filter.return_value.first.side_effect = [
                self.design,  # Design query
                user          # User query
            ]
            mock_query.return_value.filter.return_value.all.return_value = shares
            
            # Execute
            result = self.service.get_sharing_history(design_id=1, user_id=1)
            
            # Verify
            assert len(result) == 1
            assert result[0]["action"] == "granted"
            assert result[0]["username"] == "user2"
            assert result[0]["permission_level"] == "view"