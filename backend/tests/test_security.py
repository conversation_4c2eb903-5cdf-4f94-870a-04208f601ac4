#!/usr/bin/env python3
"""
Security testing and vulnerability assessment
Tests for common security vulnerabilities and attack vectors
"""

import pytest
import time
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import re

from main import app
from auth.security import create_access_token, verify_password, hash_password
from database.connection import get_db


class TestAuthenticationSecurity:
    """Test authentication security measures"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_password_hashing_security(self):
        """Test password hashing implementation"""
        password = "testpassword123"
        
        # Hash the same password multiple times
        hash1 = hash_password(password)
        hash2 = hash_password(password)
        
        # Hashes should be different (salt should be random)
        assert hash1 != hash2
        
        # Both hashes should verify correctly
        assert verify_password(password, hash1)
        assert verify_password(password, hash2)
        
        # Wrong password should not verify
        assert not verify_password("wrongpassword", hash1)
        
        # Hash should be sufficiently long (bcrypt produces 60-character hashes)
        assert len(hash1) >= 60
        
        # Hash should contain bcrypt identifier
        assert hash1.startswith('$2b$')
    
    def test_jwt_token_security(self):
        """Test JWT token security"""
        user_data = {"sub": "testuser", "user_id": 1}
        
        # Create token
        token = create_access_token(user_data)
        
        # Token should be a string
        assert isinstance(token, str)
        
        # Token should have three parts (header.payload.signature)
        parts = token.split('.')
        assert len(parts) == 3
        
        # Decode token to verify structure
        decoded = jwt.decode(token, options={"verify_signature": False})
        assert decoded["sub"] == "testuser"
        assert decoded["user_id"] == 1
        assert "exp" in decoded  # Should have expiration
        assert "iat" in decoded  # Should have issued at
        
        # Token should expire in reasonable time (not too long)
        exp_time = datetime.fromtimestamp(decoded["exp"])
        iat_time = datetime.fromtimestamp(decoded["iat"])
        duration = exp_time - iat_time
        assert duration <= timedelta(hours=24)  # Should not exceed 24 hours
    
    def test_token_expiration(self, client):
        """Test token expiration handling"""
        # Create expired token
        expired_data = {
            "sub": "testuser",
            "user_id": 1,
            "exp": datetime.utcnow() - timedelta(hours=1)  # Expired 1 hour ago
        }
        
        # This would normally be done by create_access_token, but we're creating expired token manually
        from auth.security import SECRET_KEY, ALGORITHM
        expired_token = jwt.encode(expired_data, SECRET_KEY, algorithm=ALGORITHM)
        
        # Try to access protected endpoint with expired token
        headers = {"Authorization": f"Bearer {expired_token}"}
        response = client.get("/api/users/profile", headers=headers)
        
        assert response.status_code == 401
        assert "expired" in response.json()["detail"].lower()
    
    def test_invalid_token_handling(self, client):
        """Test handling of invalid tokens"""
        invalid_tokens = [
            "invalid.token.here",
            "Bearer invalid_token",
            "not_a_token_at_all",
            "",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature"
        ]
        
        for invalid_token in invalid_tokens:
            headers = {"Authorization": f"Bearer {invalid_token}"}
            response = client.get("/api/users/profile", headers=headers)
            
            assert response.status_code == 401
            assert "invalid" in response.json()["detail"].lower() or "malformed" in response.json()["detail"].lower()
    
    def test_brute_force_protection(self, client):
        """Test protection against brute force attacks"""
        # Attempt multiple failed logins
        failed_attempts = 0
        max_attempts = 10
        
        for i in range(max_attempts):
            response = client.post("/api/auth/login", json={
                "username": "testuser",
                "password": f"wrongpassword{i}"
            })
            
            if response.status_code == 429:  # Too Many Requests
                break
            
            failed_attempts += 1
            time.sleep(0.1)  # Small delay between attempts
        
        # Should implement rate limiting after several failed attempts
        assert failed_attempts < max_attempts, "No brute force protection detected"
    
    def test_session_security(self, client):
        """Test session security measures"""
        # Register and login user
        user_data = {
            "username": "sessiontest",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Login
        login_response = client.post("/api/auth/login", json={
            "username": "sessiontest",
            "password": "testpassword123"
        })
        
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Access protected resource
        profile_response = client.get("/api/users/profile", headers=headers)
        assert profile_response.status_code == 200
        
        # Logout should invalidate token
        logout_response = client.post("/api/auth/logout", headers=headers)
        assert logout_response.status_code == 200
        
        # Token should no longer work after logout
        profile_response_after_logout = client.get("/api/users/profile", headers=headers)
        assert profile_response_after_logout.status_code == 401


class TestInputValidationSecurity:
    """Test input validation and sanitization"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self, client):
        """Create authenticated user and return headers"""
        user_data = {
            "username": "securitytest",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", json={
            "username": "securitytest",
            "password": "testpassword123"
        })
        
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_sql_injection_protection(self, client, auth_headers):
        """Test protection against SQL injection attacks"""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "' OR 1=1 --",
            "'; INSERT INTO users VALUES ('hacker', 'hacked'); --"
        ]
        
        for payload in sql_injection_payloads:
            # Test in search parameters
            response = client.get(f"/api/users/designs?search={payload}", headers=auth_headers)
            
            # Should not return 500 error (which might indicate SQL error)
            assert response.status_code != 500
            
            # Should return valid JSON response
            assert response.headers.get("content-type", "").startswith("application/json")
    
    def test_xss_protection(self, client, auth_headers):
        """Test protection against Cross-Site Scripting (XSS)"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//",
            "<svg onload=alert('XSS')>",
            "&#60;script&#62;alert('XSS')&#60;/script&#62;"
        ]
        
        for payload in xss_payloads:
            # Test in user profile update
            response = client.put("/api/users/profile", 
                                json={"username": payload, "email": "<EMAIL>"},
                                headers=auth_headers)
            
            if response.status_code == 200:
                # If update succeeds, check that payload is sanitized
                profile_response = client.get("/api/users/profile", headers=auth_headers)
                profile_data = profile_response.json()
                
                # Username should not contain script tags or javascript
                username = profile_data.get("username", "")
                assert "<script>" not in username.lower()
                assert "javascript:" not in username.lower()
                assert "onerror=" not in username.lower()
    
    def test_file_upload_security(self, client, auth_headers):
        """Test file upload security measures"""
        # Test malicious file types
        malicious_files = [
            ("malware.exe", b"MZ\x90\x00", "application/octet-stream"),
            ("script.php", b"<?php system($_GET['cmd']); ?>", "application/x-php"),
            ("shell.jsp", b"<%@ page import=\"java.io.*\" %>", "application/x-jsp"),
            ("virus.bat", b"@echo off\nformat c: /y", "application/x-msdos-program"),
            ("large_file.txt", b"A" * (50 * 1024 * 1024), "text/plain"),  # 50MB file
        ]
        
        for filename, content, content_type in malicious_files:
            files = {"file": (filename, content, content_type)}
            response = client.post("/api/files/upload", files=files, headers=auth_headers)
            
            # Should reject malicious file types
            if filename.endswith(('.exe', '.php', '.jsp', '.bat')):
                assert response.status_code in [400, 415]  # Bad Request or Unsupported Media Type
            
            # Should reject oversized files
            if len(content) > 10 * 1024 * 1024:  # Assuming 10MB limit
                assert response.status_code == 413  # Payload Too Large
    
    def test_path_traversal_protection(self, client, auth_headers):
        """Test protection against path traversal attacks"""
        path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd"
        ]
        
        for payload in path_traversal_payloads:
            # Test in file access endpoints
            response = client.get(f"/api/files/{payload}", headers=auth_headers)
            
            # Should not return file contents or 200 status
            assert response.status_code in [400, 404, 403]
    
    def test_command_injection_protection(self, client, auth_headers):
        """Test protection against command injection"""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& rm -rf /",
            "`whoami`",
            "$(id)",
            "; ping -c 1 google.com",
        ]
        
        for payload in command_injection_payloads:
            # Test in any endpoint that might process system commands
            response = client.post("/api/analysis/process", 
                                 json={"file_id": 1, "options": {"custom_param": payload}},
                                 headers=auth_headers)
            
            # Should not execute system commands
            # Response should be controlled (not hang or return system output)
            assert response.status_code in [400, 404, 422]  # Client error, not server error


class TestAuthorizationSecurity:
    """Test authorization and access control"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def user1_headers(self, client):
        """Create first test user"""
        user_data = {
            "username": "user1",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", json={
            "username": "user1",
            "password": "testpassword123"
        })
        
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.fixture
    def user2_headers(self, client):
        """Create second test user"""
        user_data = {
            "username": "user2",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", json={
            "username": "user2",
            "password": "testpassword123"
        })
        
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_unauthorized_access_prevention(self, client):
        """Test that protected endpoints require authentication"""
        protected_endpoints = [
            ("GET", "/api/users/profile"),
            ("GET", "/api/users/designs"),
            ("POST", "/api/files/upload"),
            ("GET", "/api/sharing/shared-with-me"),
            ("POST", "/api/analysis/process"),
        ]
        
        for method, endpoint in protected_endpoints:
            if method == "GET":
                response = client.get(endpoint)
            elif method == "POST":
                response = client.post(endpoint, json={})
            
            assert response.status_code == 401
            assert "unauthorized" in response.json()["detail"].lower() or "not authenticated" in response.json()["detail"].lower()
    
    def test_user_data_isolation(self, client, user1_headers, user2_headers):
        """Test that users can only access their own data"""
        # User 1 gets their designs
        user1_designs = client.get("/api/users/designs", headers=user1_headers)
        assert user1_designs.status_code == 200
        
        # User 2 gets their designs
        user2_designs = client.get("/api/users/designs", headers=user2_headers)
        assert user2_designs.status_code == 200
        
        # Users should not see each other's designs (unless shared)
        user1_design_ids = [d["id"] for d in user1_designs.json()]
        user2_design_ids = [d["id"] for d in user2_designs.json()]
        
        # No overlap in design IDs (unless explicitly shared)
        overlap = set(user1_design_ids) & set(user2_design_ids)
        assert len(overlap) == 0
    
    def test_admin_access_control(self, client, user1_headers):
        """Test admin-only endpoints are protected"""
        admin_endpoints = [
            ("GET", "/api/admin/users"),
            ("POST", "/api/admin/users"),
            ("DELETE", "/api/admin/users/1"),
            ("GET", "/api/admin/system/stats"),
        ]
        
        for method, endpoint in admin_endpoints:
            if method == "GET":
                response = client.get(endpoint, headers=user1_headers)
            elif method == "POST":
                response = client.post(endpoint, json={}, headers=user1_headers)
            elif method == "DELETE":
                response = client.delete(endpoint, headers=user1_headers)
            
            # Regular user should not have admin access
            assert response.status_code in [403, 404]  # Forbidden or Not Found
    
    def test_resource_ownership_validation(self, client, user1_headers, user2_headers):
        """Test that users can only modify resources they own"""
        # This test would need actual data setup, but demonstrates the concept
        
        # User 1 creates a design (would need actual file upload)
        # Then User 2 tries to access/modify User 1's design
        
        # For now, test with hypothetical design ID
        design_id = 999  # Non-existent design
        
        # User 2 tries to access User 1's design
        response = client.get(f"/api/designs/{design_id}", headers=user2_headers)
        assert response.status_code in [403, 404]  # Should not have access
        
        # User 2 tries to delete User 1's design
        response = client.delete(f"/api/designs/{design_id}", headers=user2_headers)
        assert response.status_code in [403, 404]  # Should not have access


class TestDataProtectionSecurity:
    """Test data protection and privacy measures"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_password_not_in_responses(self, client):
        """Test that passwords are never returned in API responses"""
        user_data = {
            "username": "passwordtest",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        # Register user
        register_response = client.post("/api/auth/register", json=user_data)
        assert register_response.status_code == 201
        
        # Check that password is not in response
        register_data = register_response.json()
        assert "password" not in register_data
        assert "testpassword123" not in str(register_data)
        
        # Login
        login_response = client.post("/api/auth/login", json={
            "username": "passwordtest",
            "password": "testpassword123"
        })
        
        assert login_response.status_code == 200
        login_data = login_response.json()
        
        # Check that password is not in login response
        assert "password" not in login_data
        assert "testpassword123" not in str(login_data)
        
        # Get user profile
        token = login_data["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        profile_response = client.get("/api/users/profile", headers=headers)
        assert profile_response.status_code == 200
        profile_data = profile_response.json()
        
        # Check that password is not in profile response
        assert "password" not in profile_data
        assert "testpassword123" not in str(profile_data)
    
    def test_sensitive_data_logging(self, client):
        """Test that sensitive data is not logged"""
        with patch('logging.Logger.info') as mock_logger:
            user_data = {
                "username": "logtest",
                "email": "<EMAIL>",
                "password": "secretpassword123"
            }
            
            response = client.post("/api/auth/register", json=user_data)
            
            # Check that password is not in any log calls
            for call in mock_logger.call_args_list:
                log_message = str(call)
                assert "secretpassword123" not in log_message
                assert "password" not in log_message.lower() or "password field" in log_message.lower()
    
    def test_error_message_information_disclosure(self, client):
        """Test that error messages don't disclose sensitive information"""
        # Test with non-existent user
        response = client.post("/api/auth/login", json={
            "username": "nonexistentuser",
            "password": "anypassword"
        })
        
        assert response.status_code == 401
        error_message = response.json()["detail"]
        
        # Error message should not reveal whether user exists or not
        assert "user not found" not in error_message.lower()
        assert "invalid username" not in error_message.lower()
        # Should use generic message like "invalid credentials"
        assert "invalid" in error_message.lower() and "credentials" in error_message.lower()
    
    def test_database_connection_security(self):
        """Test database connection security"""
        # This would test database connection parameters
        # In a real implementation, you'd check:
        # - SSL/TLS encryption is enabled
        # - Connection strings don't contain passwords in logs
        # - Database user has minimal required permissions
        
        # Mock test for demonstration
        with patch.dict('os.environ', {'DATABASE_URL': 'postgresql://user:pass@localhost/db?sslmode=require'}):
            # Database URL should require SSL
            db_url = os.environ.get('DATABASE_URL', '')
            assert 'sslmode=require' in db_url or 'ssl=true' in db_url


class TestSecurityHeaders:
    """Test security headers and HTTPS enforcement"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_security_headers_present(self, client):
        """Test that security headers are present"""
        response = client.get("/")
        
        # Check for important security headers
        headers = response.headers
        
        # Content Security Policy
        assert "content-security-policy" in headers or "x-content-security-policy" in headers
        
        # X-Frame-Options (clickjacking protection)
        assert "x-frame-options" in headers
        frame_options = headers.get("x-frame-options", "").lower()
        assert frame_options in ["deny", "sameorigin"]
        
        # X-Content-Type-Options (MIME sniffing protection)
        assert "x-content-type-options" in headers
        assert headers["x-content-type-options"].lower() == "nosniff"
        
        # X-XSS-Protection
        assert "x-xss-protection" in headers
        
        # Referrer Policy
        assert "referrer-policy" in headers
    
    def test_cors_configuration(self, client):
        """Test CORS configuration security"""
        # Test preflight request
        response = client.options("/api/users/profile", headers={
            "Origin": "https://malicious-site.com",
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "Authorization"
        })
        
        # Should not allow arbitrary origins
        cors_origin = response.headers.get("access-control-allow-origin", "")
        assert cors_origin != "*" or cors_origin == ""  # Should not allow all origins for authenticated endpoints
    
    def test_content_type_validation(self, client):
        """Test content type validation"""
        # Try to send malicious content with wrong content type
        response = client.post("/api/auth/login", 
                             data="<script>alert('xss')</script>",
                             headers={"Content-Type": "text/html"})
        
        # Should reject non-JSON content for JSON endpoints
        assert response.status_code in [400, 415, 422]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])