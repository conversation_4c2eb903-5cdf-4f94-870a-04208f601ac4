#!/usr/bin/env python3
"""
Database optimization and query performance testing
Tests for database query optimization and API response times
"""

import pytest
import time
import asyncio
from datetime import datetime, timedelta
from sqlalchemy import text, create_engine, inspect
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import psutil
import statistics

from main import app
from database.connection import get_database, engine
from models.user import User
from models.design import Design
from models.sharing import DesignShare
from models.fabrication import PartFabricationProgress


class TestDatabaseQueryOptimization:
    """Test database query performance and optimization"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def db_session(self):
        """Create database session for direct queries"""
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        try:
            yield session
        finally:
            session.close()
    
    @pytest.fixture
    def auth_headers(self, client):
        """Create authenticated user and return headers"""
        user_data = {
            "username": "perftest",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", json={
            "username": "perftest",
            "password": "testpassword123"
        })
        
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_database_indexes_exist(self, db_session):
        """Test that proper database indexes exist"""
        inspector = inspect(engine)
        
        # Check indexes on critical tables
        critical_indexes = {
            'users': ['username', 'email'],
            'designs': ['user_id', 'created_at'],
            'design_shares': ['design_id', 'shared_with_id'],
            'bom_items': ['design_id'],
            'models_3d': ['design_id'],
            'part_fabrication_progress': ['design_id', 'part_id'],
        }
        
        for table_name, expected_columns in critical_indexes.items():
            try:
                indexes = inspector.get_indexes(table_name)
                indexed_columns = set()
                
                for index in indexes:
                    indexed_columns.update(index['column_names'])
                
                for column in expected_columns:
                    assert column in indexed_columns, f"Missing index on {table_name}.{column}"
                    
            except Exception as e:
                # Table might not exist in test environment
                print(f"Warning: Could not check indexes for {table_name}: {e}")
    
    def test_query_execution_time(self, db_session):
        """Test query execution times"""
        # Test common queries and measure execution time
        queries_to_test = [
            # User lookup by username (login)
            "SELECT * FROM users WHERE username = 'testuser'",
            
            # User designs query
            "SELECT * FROM designs WHERE user_id = 1 ORDER BY created_at DESC LIMIT 20",
            
            # Shared designs query
            """
            SELECT d.* FROM designs d 
            JOIN design_shares ds ON d.id = ds.design_id 
            WHERE ds.shared_with_id = 1
            """,
            
            # BOM items for a design
            "SELECT * FROM bom_items WHERE design_id = 1",
            
            # Fabrication progress query
            """
            SELECT pfp.*, bi.part_number, bi.description 
            FROM part_fabrication_progress pfp
            JOIN bom_items bi ON pfp.part_id = bi.id
            WHERE pfp.design_id = 1
            """,
        ]
        
        for query in queries_to_test:
            start_time = time.time()
            
            try:
                result = db_session.execute(text(query))
                result.fetchall()  # Ensure query is fully executed
                
                execution_time = time.time() - start_time
                
                print(f"Query execution time: {execution_time:.4f}s")
                print(f"Query: {query[:50]}...")
                
                # Queries should execute quickly
                assert execution_time < 1.0, f"Query too slow: {execution_time:.4f}s"
                
            except Exception as e:
                print(f"Query failed (might be expected in test env): {e}")
    
    def test_n_plus_one_query_prevention(self, client, auth_headers):
        """Test that N+1 query problems are avoided"""
        # This test would ideally use a query counter
        # For now, we'll test response times as a proxy
        
        # Get user designs (should use joins, not separate queries for each design)
        start_time = time.time()
        response = client.get("/api/files/", headers=auth_headers)
        response_time = time.time() - start_time
        
        assert response.status_code == 200
        
        # Response should be fast even with multiple designs
        assert response_time < 2.0
        
        files_data = response.json()
        designs = files_data.get("designs", [])
        print(f"Retrieved {len(designs)} designs in {response_time:.4f}s")
    
    def test_pagination_performance(self, client, auth_headers):
        """Test pagination performance"""
        # Test different page sizes and offsets
        page_sizes = [10, 25, 50, 100]
        
        for page_size in page_sizes:
            start_time = time.time()
            
            response = client.get(
                f"/api/files/?limit={page_size}&skip=0",
                headers=auth_headers
            )
            
            response_time = time.time() - start_time
            
            assert response.status_code == 200
            assert response_time < 3.0  # Should be fast regardless of page size
            
            print(f"Page size {page_size}: {response_time:.4f}s")
    
    def test_search_query_performance(self, client, auth_headers):
        """Test search query performance"""
        search_terms = [
            "test",
            "design",
            "part",
            "steel",
            "aluminum"
        ]
        
        for search_term in search_terms:
            start_time = time.time()
            
            response = client.get(
                f"/api/files/?search={search_term}",
                headers=auth_headers
            )
            
            response_time = time.time() - start_time
            
            assert response.status_code == 200
            assert response_time < 2.0  # Search should be fast
            
            print(f"Search '{search_term}': {response_time:.4f}s")
    
    def test_complex_query_optimization(self, db_session):
        """Test optimization of complex queries"""
        # Test a complex query that joins multiple tables
        complex_query = """
        SELECT 
            d.id,
            d.name,
            d.created_at,
            u.username,
            COUNT(bi.id) as part_count,
            SUM(bi.weight) as total_weight,
            COUNT(pfp.id) as progress_entries
        FROM designs d
        JOIN users u ON d.user_id = u.id
        LEFT JOIN bom_items bi ON d.id = bi.design_id
        LEFT JOIN part_fabrication_progress pfp ON d.id = pfp.design_id
        GROUP BY d.id, d.name, d.created_at, u.username
        ORDER BY d.created_at DESC
        LIMIT 20
        """
        
        start_time = time.time()
        
        try:
            result = db_session.execute(text(complex_query))
            rows = result.fetchall()
            
            execution_time = time.time() - start_time
            
            print(f"Complex query returned {len(rows)} rows in {execution_time:.4f}s")
            
            # Complex query should still be reasonably fast
            assert execution_time < 5.0
            
        except Exception as e:
            print(f"Complex query failed (might be expected in test env): {e}")
    
    def test_database_connection_pooling(self, client, auth_headers):
        """Test database connection pooling efficiency"""
        # Make multiple concurrent requests to test connection pooling
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def make_request():
            start_time = time.time()
            response = client.get("/api/files/", headers=auth_headers)
            response_time = time.time() - start_time
            results_queue.put((response.status_code, response_time))
        
        # Create multiple threads to simulate concurrent requests
        threads = []
        num_threads = 10
        
        for _ in range(num_threads):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Collect results
        response_times = []
        success_count = 0
        
        while not results_queue.empty():
            status_code, response_time = results_queue.get()
            if status_code == 200:
                success_count += 1
                response_times.append(response_time)
        
        # All requests should succeed
        assert success_count == num_threads
        
        # Response times should be consistent (good connection pooling)
        if response_times:
            avg_time = statistics.mean(response_times)
            max_time = max(response_times)
            
            print(f"Concurrent requests - Avg: {avg_time:.4f}s, Max: {max_time:.4f}s")
            
            # No request should be significantly slower than average
            assert max_time < avg_time * 3


class TestAPIResponseTimeOptimization:
    """Test API response time optimization"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self, client):
        """Create authenticated user and return headers"""
        user_data = {
            "username": "apitest",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_response = client.post("/api/auth/login", json={
            "username": "apitest",
            "password": "testpassword123"
        })
        
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_api_response_time_benchmarks(self, client, auth_headers):
        """Test API response time benchmarks"""
        endpoints_to_test = [
            ("GET", "/api/auth/me", None),
            ("GET", "/api/files/", None),
            ("GET", "/api/sharing/shared-with-me", None),
            ("GET", "/api/files/?limit=10", None),
        ]
        
        response_times = {}
        
        for method, endpoint, data in endpoints_to_test:
            times = []
            
            # Test each endpoint multiple times for consistency
            for _ in range(5):
                start_time = time.time()
                
                if method == "GET":
                    response = client.get(endpoint, headers=auth_headers)
                elif method == "POST":
                    response = client.post(endpoint, json=data or {}, headers=auth_headers)
                
                response_time = time.time() - start_time
                times.append(response_time)
                
                # Basic response validation
                assert response.status_code in [200, 201, 404]  # 404 is OK for empty results
            
            avg_time = statistics.mean(times)
            max_time = max(times)
            min_time = min(times)
            
            response_times[endpoint] = {
                'avg': avg_time,
                'max': max_time,
                'min': min_time
            }
            
            print(f"{method} {endpoint}:")
            print(f"  Avg: {avg_time:.4f}s, Min: {min_time:.4f}s, Max: {max_time:.4f}s")
            
            # Performance assertions
            assert avg_time < 2.0, f"Average response time too slow for {endpoint}"
            assert max_time < 5.0, f"Maximum response time too slow for {endpoint}"
        
        return response_times
    
    def test_response_size_optimization(self, client, auth_headers):
        """Test response size optimization"""
        response = client.get("/api/files/", headers=auth_headers)
        
        if response.status_code == 200:
            content_length = len(response.content)
            
            print(f"Response size: {content_length} bytes")
            
            # Response should not be excessively large
            assert content_length < 1024 * 1024  # Less than 1MB
            
            # Check if response is compressed (if server supports it)
            content_encoding = response.headers.get('content-encoding', '')
            if content_encoding:
                print(f"Content encoding: {content_encoding}")
    
    def test_caching_effectiveness(self, client, auth_headers):
        """Test caching effectiveness"""
        # Make the same request multiple times
        endpoint = "/api/auth/me"
        
        times = []
        for i in range(3):
            start_time = time.time()
            response = client.get(endpoint, headers=auth_headers)
            response_time = time.time() - start_time
            times.append(response_time)
            
            assert response.status_code == 200
            
            # Check for cache headers
            cache_control = response.headers.get('cache-control', '')
            etag = response.headers.get('etag', '')
            
            if i == 0:
                print(f"Cache-Control: {cache_control}")
                print(f"ETag: {etag}")
        
        # Subsequent requests might be faster due to caching
        if len(times) >= 2:
            first_time = times[0]
            later_times = times[1:]
            avg_later_time = statistics.mean(later_times)
            
            print(f"First request: {first_time:.4f}s")
            print(f"Average later requests: {avg_later_time:.4f}s")
    
    def test_memory_usage_during_requests(self, client, auth_headers):
        """Test memory usage during API requests"""
        process = psutil.Process()
        
        # Get initial memory usage
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        # Make multiple requests
        for _ in range(10):
            response = client.get("/api/files/", headers=auth_headers)
            assert response.status_code in [200, 404]
        
        # Get final memory usage
        final_memory = process.memory_info().rss / (1024 * 1024)  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"Memory usage - Initial: {initial_memory:.1f}MB, Final: {final_memory:.1f}MB")
        print(f"Memory increase: {memory_increase:.1f}MB")
        
        # Memory usage should not increase significantly
        assert memory_increase < 50  # Less than 50MB increase
    
    def test_concurrent_request_performance(self, client, auth_headers):
        """Test performance under concurrent requests"""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def make_concurrent_request(request_id):
            start_time = time.time()
            response = client.get("/api/files/", headers=auth_headers)
            response_time = time.time() - start_time
            results_queue.put((request_id, response.status_code, response_time))
        
        # Create concurrent requests
        threads = []
        num_concurrent = 5
        
        start_time = time.time()
        
        for i in range(num_concurrent):
            thread = threading.Thread(target=make_concurrent_request, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all requests to complete
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # Collect results
        response_times = []
        success_count = 0
        
        while not results_queue.empty():
            request_id, status_code, response_time = results_queue.get()
            if status_code in [200, 404]:
                success_count += 1
                response_times.append(response_time)
        
        # All requests should succeed
        assert success_count == num_concurrent
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            
            print(f"Concurrent requests completed in {total_time:.4f}s")
            print(f"Average response time: {avg_response_time:.4f}s")
            print(f"Maximum response time: {max_response_time:.4f}s")
            
            # Concurrent requests should not be significantly slower
            assert avg_response_time < 3.0
            assert max_response_time < 5.0


class TestDatabaseOptimizationRecommendations:
    """Test and provide database optimization recommendations"""
    
    @pytest.fixture
    def db_session(self):
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        try:
            yield session
        finally:
            session.close()
    
    def test_query_plan_analysis(self, db_session):
        """Analyze query execution plans"""
        queries_to_analyze = [
            "SELECT * FROM users WHERE username = 'testuser'",
            "SELECT * FROM designs WHERE user_id = 1 ORDER BY created_at DESC",
            """
            SELECT d.*, u.username 
            FROM designs d 
            JOIN users u ON d.user_id = u.id 
            WHERE d.created_at > NOW() - INTERVAL '30 days'
            """,
        ]
        
        for query in queries_to_analyze:
            try:
                # Get query execution plan
                explain_query = f"EXPLAIN ANALYZE {query}"
                result = db_session.execute(text(explain_query))
                plan = result.fetchall()
                
                print(f"\nQuery: {query}")
                print("Execution Plan:")
                for row in plan:
                    print(f"  {row[0]}")
                
                # Look for potential issues in the plan
                plan_text = ' '.join([str(row[0]) for row in plan])
                
                # Check for sequential scans on large tables
                if "Seq Scan" in plan_text:
                    print("  WARNING: Sequential scan detected - consider adding index")
                
                # Check for expensive operations
                if "cost=" in plan_text:
                    import re
                    costs = re.findall(r'cost=(\d+\.\d+)', plan_text)
                    if costs:
                        max_cost = max(float(cost) for cost in costs)
                        if max_cost > 1000:
                            print(f"  WARNING: High cost operation detected: {max_cost}")
                
            except Exception as e:
                print(f"Could not analyze query plan: {e}")
    
    def test_table_statistics(self, db_session):
        """Check table statistics for optimization insights"""
        try:
            # Get table sizes
            size_query = """
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE schemaname = 'public'
            ORDER BY tablename, attname
            """
            
            result = db_session.execute(text(size_query))
            stats = result.fetchall()
            
            print("\nTable Statistics:")
            current_table = None
            for row in stats:
                schema, table, column, n_distinct, correlation = row
                
                if table != current_table:
                    print(f"\nTable: {table}")
                    current_table = table
                
                print(f"  {column}: distinct={n_distinct}, correlation={correlation}")
                
                # Provide optimization suggestions
                if n_distinct is not None and n_distinct > 100:
                    print(f"    -> Consider index on {column} (high cardinality)")
                
                if correlation is not None and abs(correlation) > 0.8:
                    print(f"    -> {column} has high correlation - good for range queries")
                    
        except Exception as e:
            print(f"Could not retrieve table statistics: {e}")
    
    def test_missing_indexes_detection(self, db_session):
        """Detect potentially missing indexes"""
        try:
            # Query to find tables without indexes on foreign keys
            missing_indexes_query = """
            SELECT 
                t.table_name,
                c.column_name,
                c.data_type
            FROM information_schema.tables t
            JOIN information_schema.columns c ON t.table_name = c.table_name
            WHERE t.table_schema = 'public'
            AND c.column_name LIKE '%_id'
            AND NOT EXISTS (
                SELECT 1 FROM pg_indexes i 
                WHERE i.tablename = t.table_name 
                AND i.indexdef LIKE '%' || c.column_name || '%'
            )
            ORDER BY t.table_name, c.column_name
            """
            
            result = db_session.execute(text(missing_indexes_query))
            missing_indexes = result.fetchall()
            
            if missing_indexes:
                print("\nPotentially Missing Indexes:")
                for table, column, data_type in missing_indexes:
                    print(f"  {table}.{column} ({data_type})")
                    print(f"    -> Consider: CREATE INDEX idx_{table}_{column} ON {table}({column});")
            else:
                print("\nNo obviously missing indexes detected.")
                
        except Exception as e:
            print(f"Could not check for missing indexes: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])