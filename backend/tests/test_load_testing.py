#!/usr/bin/env python3
"""
Load testing for concurrent user scenarios and system stress testing
"""

import pytest
import time
import threading
import queue
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from fastapi.testclient import TestClient
import psutil
import gc

from main import app


class LoadTestMetrics:
    """Collect and analyze load test metrics"""
    
    def __init__(self):
        self.response_times = []
        self.success_count = 0
        self.error_count = 0
        self.errors = []
        self.start_time = None
        self.end_time = None
        self.lock = threading.Lock()
    
    def record_response(self, response_time, success, error=None):
        """Record a response result"""
        with self.lock:
            self.response_times.append(response_time)
            if success:
                self.success_count += 1
            else:
                self.error_count += 1
                if error:
                    self.errors.append(error)
    
    def start_test(self):
        """Mark test start time"""
        self.start_time = time.time()
    
    def end_test(self):
        """Mark test end time"""
        self.end_time = time.time()
    
    def get_summary(self):
        """Get test summary statistics"""
        if not self.response_times:
            return {"error": "No response times recorded"}
        
        total_requests = self.success_count + self.error_count
        success_rate = (self.success_count / total_requests) * 100 if total_requests > 0 else 0
        
        return {
            "total_requests": total_requests,
            "successful_requests": self.success_count,
            "failed_requests": self.error_count,
            "success_rate_percent": success_rate,
            "total_duration": self.end_time - self.start_time if self.end_time and self.start_time else 0,
            "requests_per_second": total_requests / (self.end_time - self.start_time) if self.end_time and self.start_time else 0,
            "response_times": {
                "min": min(self.response_times),
                "max": max(self.response_times),
                "mean": statistics.mean(self.response_times),
                "median": statistics.median(self.response_times),
                "p95": statistics.quantiles(self.response_times, n=20)[18] if len(self.response_times) >= 20 else max(self.response_times),
                "p99": statistics.quantiles(self.response_times, n=100)[98] if len(self.response_times) >= 100 else max(self.response_times)
            },
            "errors": self.errors[:10]  # First 10 errors
        }


class TestConcurrentLoad:
    """Test system under concurrent load"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def create_test_user(self, client, user_id):
        """Create a test user and return auth headers"""
        user_data = {
            "username": f"loaduser_{user_id}",
            "email": f"load{user_id}@example.com",
            "password": "testpassword123"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        if response.status_code != 201:
            return None
        
        login_data = {
            "username": f"loaduser_{user_id}",
            "password": "testpassword123"
        }
        
        response = client.post("/api/auth/login", json=login_data)
        if response.status_code != 200:
            return None
        
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_concurrent_authentication(self):
        """Test concurrent user authentication"""
        
        metrics = LoadTestMetrics()
        
        def authenticate_user(user_id):
            """Authenticate a single user"""
            client = TestClient(app)
            start_time = time.time()
            
            try:
                # Register
                user_data = {
                    "username": f"auth_test_{user_id}",
                    "email": f"auth{user_id}@example.com",
                    "password": "testpassword123"
                }
                
                response = client.post("/api/auth/register", json=user_data)
                if response.status_code != 201:
                    response_time = time.time() - start_time
                    metrics.record_response(response_time, False, f"Registration failed: {response.status_code}")
                    return
                
                # Login
                login_data = {
                    "username": f"auth_test_{user_id}",
                    "password": "testpassword123"
                }
                
                response = client.post("/api/auth/login", json=login_data)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    metrics.record_response(response_time, True)
                else:
                    metrics.record_response(response_time, False, f"Login failed: {response.status_code}")
                    
            except Exception as e:
                response_time = time.time() - start_time
                metrics.record_response(response_time, False, str(e))
        
        # Test with 20 concurrent authentication requests
        num_concurrent = 20
        
        metrics.start_test()
        
        with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(authenticate_user, i) for i in range(num_concurrent)]
            
            for future in as_completed(futures):
                future.result()  # Wait for completion
        
        metrics.end_test()
        
        # Analyze results
        summary = metrics.get_summary()
        
        print(f"Concurrent Authentication Test Results:")
        print(f"  Total Requests: {summary['total_requests']}")
        print(f"  Success Rate: {summary['success_rate_percent']:.1f}%")
        print(f"  Requests/Second: {summary['requests_per_second']:.1f}")
        print(f"  Response Times (ms):")
        print(f"    Mean: {summary['response_times']['mean']*1000:.1f}")
        print(f"    Median: {summary['response_times']['median']*1000:.1f}")
        print(f"    95th percentile: {summary['response_times']['p95']*1000:.1f}")
        
        # Assertions
        assert summary['success_rate_percent'] >= 90  # At least 90% success rate
        assert summary['response_times']['mean'] < 5.0  # Average response under 5 seconds
        assert summary['response_times']['p95'] < 10.0  # 95th percentile under 10 seconds
    
    def test_concurrent_dashboard_access(self):
        """Test concurrent dashboard access"""
        
        # Pre-create users
        client = TestClient(app)
        auth_headers = []
        
        for i in range(10):
            headers = self.create_test_user(client, f"dashboard_{i}")
            if headers:
                auth_headers.append(headers)
        
        if len(auth_headers) < 5:
            pytest.skip("Could not create enough test users")
        
        metrics = LoadTestMetrics()
        
        def access_dashboard(headers):
            """Access dashboard for a user"""
            client = TestClient(app)
            start_time = time.time()
            
            try:
                response = client.get("/api/users/designs", headers=headers)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    metrics.record_response(response_time, True)
                else:
                    metrics.record_response(response_time, False, f"Dashboard access failed: {response.status_code}")
                    
            except Exception as e:
                response_time = time.time() - start_time
                metrics.record_response(response_time, False, str(e))
        
        metrics.start_test()
        
        # Test multiple rounds of concurrent access
        for round_num in range(3):
            with ThreadPoolExecutor(max_workers=len(auth_headers)) as executor:
                futures = [executor.submit(access_dashboard, headers) for headers in auth_headers]
                
                for future in as_completed(futures):
                    future.result()
            
            time.sleep(1)  # Brief pause between rounds
        
        metrics.end_test()
        
        # Analyze results
        summary = metrics.get_summary()
        
        print(f"Concurrent Dashboard Access Test Results:")
        print(f"  Total Requests: {summary['total_requests']}")
        print(f"  Success Rate: {summary['success_rate_percent']:.1f}%")
        print(f"  Requests/Second: {summary['requests_per_second']:.1f}")
        print(f"  Response Times (ms):")
        print(f"    Mean: {summary['response_times']['mean']*1000:.1f}")
        print(f"    95th percentile: {summary['response_times']['p95']*1000:.1f}")
        
        # Assertions
        assert summary['success_rate_percent'] >= 95  # Dashboard should be very reliable
        assert summary['response_times']['mean'] < 3.0  # Fast dashboard access
        assert summary['response_times']['p95'] < 5.0  # Consistent performance
    
    def test_sustained_load(self):
        """Test system under sustained load"""
        
        # Pre-create users
        client = TestClient(app)
        auth_headers = []
        
        for i in range(5):
            headers = self.create_test_user(client, f"sustained_{i}")
            if headers:
                auth_headers.append(headers)
        
        if len(auth_headers) < 3:
            pytest.skip("Could not create enough test users")
        
        metrics = LoadTestMetrics()
        stop_flag = threading.Event()
        
        def sustained_user_activity(headers, user_id):
            """Simulate sustained user activity"""
            client = TestClient(app)
            
            endpoints = [
                "/api/users/profile",
                "/api/users/designs",
                "/api/sharing/shared-with-me"
            ]
            
            while not stop_flag.is_set():
                for endpoint in endpoints:
                    if stop_flag.is_set():
                        break
                    
                    start_time = time.time()
                    
                    try:
                        response = client.get(endpoint, headers=headers)
                        response_time = time.time() - start_time
                        
                        if response.status_code == 200:
                            metrics.record_response(response_time, True)
                        else:
                            metrics.record_response(response_time, False, f"{endpoint} failed: {response.status_code}")
                            
                    except Exception as e:
                        response_time = time.time() - start_time
                        metrics.record_response(response_time, False, str(e))
                    
                    time.sleep(0.5)  # Brief pause between requests
        
        # Start sustained load test
        metrics.start_test()
        
        threads = []
        for i, headers in enumerate(auth_headers):
            thread = threading.Thread(target=sustained_user_activity, args=(headers, i))
            threads.append(thread)
            thread.start()
        
        # Run for 30 seconds
        time.sleep(30)
        
        # Stop all threads
        stop_flag.set()
        for thread in threads:
            thread.join()
        
        metrics.end_test()
        
        # Analyze results
        summary = metrics.get_summary()
        
        print(f"Sustained Load Test Results:")
        print(f"  Duration: {summary['total_duration']:.1f} seconds")
        print(f"  Total Requests: {summary['total_requests']}")
        print(f"  Success Rate: {summary['success_rate_percent']:.1f}%")
        print(f"  Requests/Second: {summary['requests_per_second']:.1f}")
        print(f"  Response Times (ms):")
        print(f"    Mean: {summary['response_times']['mean']*1000:.1f}")
        print(f"    95th percentile: {summary['response_times']['p95']*1000:.1f}")
        
        # Assertions for sustained load
        assert summary['success_rate_percent'] >= 85  # Should maintain good success rate
        assert summary['response_times']['mean'] < 5.0  # Reasonable response times
        assert summary['requests_per_second'] > 1.0  # Minimum throughput
    
    def test_memory_under_load(self):
        """Test memory usage under load"""
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        # Pre-create users
        client = TestClient(app)
        auth_headers = []
        
        for i in range(8):
            headers = self.create_test_user(client, f"memory_{i}")
            if headers:
                auth_headers.append(headers)
        
        if len(auth_headers) < 5:
            pytest.skip("Could not create enough test users")
        
        memory_samples = []
        stop_flag = threading.Event()
        
        def monitor_memory():
            """Monitor memory usage during test"""
            while not stop_flag.is_set():
                current_memory = process.memory_info().rss / (1024 * 1024)
                memory_samples.append(current_memory)
                time.sleep(1)
        
        def generate_load(headers):
            """Generate load for memory testing"""
            client = TestClient(app)
            
            for _ in range(20):  # 20 requests per user
                if stop_flag.is_set():
                    break
                
                try:
                    response = client.get("/api/users/designs", headers=headers)
                    time.sleep(0.1)  # Small delay between requests
                except:
                    pass  # Ignore errors for memory test
        
        # Start memory monitoring
        memory_thread = threading.Thread(target=monitor_memory)
        memory_thread.start()
        
        # Generate load
        with ThreadPoolExecutor(max_workers=len(auth_headers)) as executor:
            futures = [executor.submit(generate_load, headers) for headers in auth_headers]
            
            for future in as_completed(futures):
                future.result()
        
        # Stop monitoring
        stop_flag.set()
        memory_thread.join()
        
        # Force garbage collection
        gc.collect()
        time.sleep(2)
        
        final_memory = process.memory_info().rss / (1024 * 1024)
        
        if memory_samples:
            max_memory = max(memory_samples)
            avg_memory = sum(memory_samples) / len(memory_samples)
            
            print(f"Memory Usage Under Load:")
            print(f"  Initial: {initial_memory:.1f} MB")
            print(f"  Peak: {max_memory:.1f} MB")
            print(f"  Average: {avg_memory:.1f} MB")
            print(f"  Final: {final_memory:.1f} MB")
            print(f"  Peak Increase: {max_memory - initial_memory:.1f} MB")
            
            # Memory assertions
            memory_increase = max_memory - initial_memory
            assert memory_increase < 500  # Should not increase by more than 500MB
            
            # Memory should return close to initial after load
            memory_leak = final_memory - initial_memory
            assert memory_leak < 100  # Should not leak more than 100MB
    
    def test_database_connection_pool(self):
        """Test database connection pool under load"""
        
        # Pre-create users
        client = TestClient(app)
        auth_headers = []
        
        for i in range(15):  # More users to stress connection pool
            headers = self.create_test_user(client, f"dbpool_{i}")
            if headers:
                auth_headers.append(headers)
        
        if len(auth_headers) < 10:
            pytest.skip("Could not create enough test users")
        
        metrics = LoadTestMetrics()
        
        def database_intensive_operation(headers):
            """Perform database-intensive operations"""
            client = TestClient(app)
            
            operations = [
                "/api/users/profile",
                "/api/users/designs",
                "/api/sharing/shared-with-me"
            ]
            
            for operation in operations:
                start_time = time.time()
                
                try:
                    response = client.get(operation, headers=headers)
                    response_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        metrics.record_response(response_time, True)
                    else:
                        metrics.record_response(response_time, False, f"DB operation failed: {response.status_code}")
                        
                except Exception as e:
                    response_time = time.time() - start_time
                    metrics.record_response(response_time, False, str(e))
        
        metrics.start_test()
        
        # Test with high concurrency to stress connection pool
        with ThreadPoolExecutor(max_workers=len(auth_headers)) as executor:
            futures = [executor.submit(database_intensive_operation, headers) for headers in auth_headers]
            
            for future in as_completed(futures):
                future.result()
        
        metrics.end_test()
        
        # Analyze results
        summary = metrics.get_summary()
        
        print(f"Database Connection Pool Test Results:")
        print(f"  Total Requests: {summary['total_requests']}")
        print(f"  Success Rate: {summary['success_rate_percent']:.1f}%")
        print(f"  Response Times (ms):")
        print(f"    Mean: {summary['response_times']['mean']*1000:.1f}")
        print(f"    95th percentile: {summary['response_times']['p95']*1000:.1f}")
        
        # Database connection pool assertions
        assert summary['success_rate_percent'] >= 90  # Should handle connection pool well
        assert summary['response_times']['mean'] < 3.0  # Database operations should be fast
        
        # Check for connection pool errors
        connection_errors = [e for e in summary['errors'] if 'connection' in e.lower()]
        assert len(connection_errors) == 0  # Should not have connection pool issues


class TestStressScenarios:
    """Test system under stress conditions"""
    
    def test_rapid_user_registration(self):
        """Test rapid user registration scenario"""
        
        metrics = LoadTestMetrics()
        
        def register_user(user_id):
            """Register a single user"""
            client = TestClient(app)
            start_time = time.time()
            
            try:
                user_data = {
                    "username": f"rapid_{user_id}_{int(time.time())}",
                    "email": f"rapid{user_id}_{int(time.time())}@example.com",
                    "password": "testpassword123"
                }
                
                response = client.post("/api/auth/register", json=user_data)
                response_time = time.time() - start_time
                
                if response.status_code == 201:
                    metrics.record_response(response_time, True)
                else:
                    metrics.record_response(response_time, False, f"Registration failed: {response.status_code}")
                    
            except Exception as e:
                response_time = time.time() - start_time
                metrics.record_response(response_time, False, str(e))
        
        # Test rapid registration of 25 users
        num_users = 25
        
        metrics.start_test()
        
        with ThreadPoolExecutor(max_workers=num_users) as executor:
            futures = [executor.submit(register_user, i) for i in range(num_users)]
            
            for future in as_completed(futures):
                future.result()
        
        metrics.end_test()
        
        # Analyze results
        summary = metrics.get_summary()
        
        print(f"Rapid User Registration Test Results:")
        print(f"  Total Registrations: {summary['total_requests']}")
        print(f"  Success Rate: {summary['success_rate_percent']:.1f}%")
        print(f"  Registrations/Second: {summary['requests_per_second']:.1f}")
        
        # Assertions
        assert summary['success_rate_percent'] >= 80  # Should handle rapid registration
        assert summary['response_times']['mean'] < 10.0  # Reasonable registration time


if __name__ == "__main__":
    pytest.main([__file__, "-v"])