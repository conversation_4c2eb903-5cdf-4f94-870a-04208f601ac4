"""
Tests for authentication API endpoints.
"""
import pytest
from fastapi.testclient import TestClient


class TestUserRegistration:
    """Test user registration endpoints."""
    
    def test_register_user_success(self, client):
        """Test successful user registration."""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "NewUserPass123!"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert "password" not in data
        assert not data["is_admin"]
        assert data["is_active"]
    
    def test_register_user_duplicate_username(self, client, test_user):
        """Test registration with duplicate username."""
        user_data = {
            "username": test_user.username,
            "email": "<EMAIL>",
            "password": "TestPass123!"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "Username already registered" in response.json()["detail"]
    
    def test_register_user_duplicate_email(self, client, test_user):
        """Test registration with duplicate email."""
        user_data = {
            "username": "differentuser",
            "email": test_user.email,
            "password": "TestPass123!"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "Email already registered" in response.json()["detail"]
    
    def test_register_user_invalid_data(self, client):
        """Test registration with invalid data."""
        # Invalid username
        response = client.post("/api/auth/register", json={
            "username": "ab",  # Too short
            "email": "<EMAIL>",
            "password": "TestPass123!"
        })
        assert response.status_code == 400
        
        # Invalid password
        response = client.post("/api/auth/register", json={
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "weak"  # Too weak
        })
        assert response.status_code == 400
        
        # Invalid email
        response = client.post("/api/auth/register", json={
            "username": "testuser",
            "email": "invalid-email",
            "password": "TestPass123!"
        })
        assert response.status_code == 422  # Pydantic validation error


class TestUserLogin:
    """Test user login endpoints."""
    
    def test_login_success(self, client, test_user, test_user_data):
        """Test successful user login."""
        login_data = {
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        }
        
        response = client.post("/api/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    def test_login_wrong_password(self, client, test_user, test_user_data):
        """Test login with wrong password."""
        login_data = {
            "username": test_user_data["username"],
            "password": "wrong_password"
        }
        
        response = client.post("/api/auth/login", json=login_data)
        
        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]
    
    def test_login_nonexistent_user(self, client):
        """Test login with nonexistent user."""
        login_data = {
            "username": "nonexistent",
            "password": "password"
        }
        
        response = client.post("/api/auth/login", json=login_data)
        
        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]
    
    def test_login_inactive_user(self, client, test_user, test_user_data, db_session):
        """Test login with inactive user."""
        # Deactivate user
        test_user.is_active = False
        db_session.commit()
        
        login_data = {
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        }
        
        response = client.post("/api/auth/login", json=login_data)
        
        assert response.status_code == 401


class TestTokenRefresh:
    """Test token refresh endpoints."""
    
    def test_refresh_token_success(self, client, test_user, test_user_data):
        """Test successful token refresh."""
        # Login to get tokens
        login_response = client.post("/api/auth/login", json={
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        })
        tokens = login_response.json()
        
        # Refresh token
        response = client.post("/api/auth/refresh", json={
            "refresh_token": tokens["refresh_token"]
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_refresh_token_invalid(self, client):
        """Test refresh with invalid token."""
        response = client.post("/api/auth/refresh", json={
            "refresh_token": "invalid_token"
        })
        
        assert response.status_code == 401


class TestUserProfile:
    """Test user profile endpoints."""
    
    def test_get_current_user_profile(self, client, user_token, test_user):
        """Test getting current user profile."""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get("/api/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == test_user.username
        assert data["email"] == test_user.email
        assert "password" not in data
    
    def test_get_profile_unauthorized(self, client):
        """Test getting profile without authentication."""
        response = client.get("/api/auth/me")
        assert response.status_code == 403  # No authorization header
    
    def test_get_profile_invalid_token(self, client):
        """Test getting profile with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/auth/me", headers=headers)
        assert response.status_code == 401
    
    def test_update_user_profile_email(self, client, user_token):
        """Test updating user email."""
        headers = {"Authorization": f"Bearer {user_token}"}
        update_data = {"email": "<EMAIL>"}
        
        response = client.put("/api/auth/me", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"
    
    def test_update_user_profile_password(self, client, user_token, test_user_data):
        """Test updating user password."""
        headers = {"Authorization": f"Bearer {user_token}"}
        update_data = {
            "current_password": test_user_data["password"],
            "new_password": "NewPassword123!"
        }
        
        response = client.put("/api/auth/me", json=update_data, headers=headers)
        
        assert response.status_code == 200
    
    def test_update_password_wrong_current(self, client, user_token):
        """Test updating password with wrong current password."""
        headers = {"Authorization": f"Bearer {user_token}"}
        update_data = {
            "current_password": "wrong_password",
            "new_password": "NewPassword123!"
        }
        
        response = client.put("/api/auth/me", json=update_data, headers=headers)
        
        assert response.status_code == 400
        assert "Current password is incorrect" in response.json()["detail"]


class TestPasswordReset:
    """Test password reset endpoints."""
    
    def test_request_password_reset(self, client, test_user):
        """Test password reset request."""
        reset_data = {"email": test_user.email}
        
        response = client.post("/api/auth/password-reset", json=reset_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        # In development, token is returned
        assert "reset_token" in data
    
    def test_request_password_reset_nonexistent_email(self, client):
        """Test password reset with nonexistent email."""
        reset_data = {"email": "<EMAIL>"}
        
        response = client.post("/api/auth/password-reset", json=reset_data)
        
        # Should still return success to prevent email enumeration
        assert response.status_code == 200
    
    def test_confirm_password_reset(self, client, test_user):
        """Test password reset confirmation."""
        # Request reset token
        reset_request = client.post("/api/auth/password-reset", json={
            "email": test_user.email
        })
        reset_token = reset_request.json()["reset_token"]
        
        # Confirm reset
        confirm_data = {
            "token": reset_token,
            "new_password": "NewResetPass123!"
        }
        
        response = client.post("/api/auth/password-reset/confirm", json=confirm_data)
        
        assert response.status_code == 200
        assert "Password reset successfully" in response.json()["message"]
    
    def test_confirm_password_reset_invalid_token(self, client):
        """Test password reset confirmation with invalid token."""
        confirm_data = {
            "token": "invalid_token",
            "new_password": "NewPassword123!"
        }
        
        response = client.post("/api/auth/password-reset/confirm", json=confirm_data)
        
        assert response.status_code == 400
        assert "Invalid or expired reset token" in response.json()["detail"]


class TestAdminEndpoints:
    """Test admin-only endpoints."""
    
    def test_get_all_users_admin(self, client, admin_token, test_user, admin_user):
        """Test getting all users as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/auth/users", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2
        
        usernames = [user["username"] for user in data]
        assert test_user.username in usernames
        assert admin_user.username in usernames
    
    def test_get_all_users_non_admin(self, client, user_token):
        """Test getting all users as non-admin."""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get("/api/auth/users", headers=headers)
        
        assert response.status_code == 403
        assert "Admin access required" in response.json()["detail"]
    
    def test_create_user_admin(self, client, admin_token):
        """Test creating user as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        user_data = {
            "username": "admincreatednewuser",
            "email": "<EMAIL>",
            "password": "AdminCreated123!"
        }
        
        response = client.post("/api/auth/users", json=user_data, headers=headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == user_data["username"]
        assert not data["is_admin"]  # Default is False
    
    def test_create_admin_user(self, client, admin_token):
        """Test creating admin user."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        user_data = {
            "username": "newadmin",
            "email": "<EMAIL>",
            "password": "NewAdmin123!"
        }
        
        response = client.post(
            "/api/auth/users?is_admin=true", 
            json=user_data, 
            headers=headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["is_admin"]
    
    def test_update_user_status_admin(self, client, admin_token, test_user):
        """Test updating user status as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Deactivate user
        response = client.put(
            f"/api/auth/users/{test_user.id}/status?is_active=false",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert not data["is_active"]
    
    def test_update_user_admin_status(self, client, admin_token, test_user):
        """Test updating user admin status."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Make user admin
        response = client.put(
            f"/api/auth/users/{test_user.id}/admin?is_admin=true",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_admin"]
    
    def test_delete_user_admin(self, client, admin_token, test_user):
        """Test deleting user as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.delete(f"/api/auth/users/{test_user.id}", headers=headers)
        
        assert response.status_code == 200
        assert "User deleted successfully" in response.json()["message"]
    
    def test_delete_self_admin(self, client, admin_token, admin_user):
        """Test admin trying to delete themselves."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.delete(f"/api/auth/users/{admin_user.id}", headers=headers)
        
        assert response.status_code == 400
        assert "Cannot delete your own account" in response.json()["detail"]
    
    def test_admin_endpoints_unauthorized(self, client, user_token, test_user):
        """Test admin endpoints with non-admin user."""
        headers = {"Authorization": f"Bearer {user_token}"}
        
        # Try to get all users
        response = client.get("/api/auth/users", headers=headers)
        assert response.status_code == 403
        
        # Try to create user
        response = client.post("/api/auth/users", json={
            "username": "test",
            "email": "<EMAIL>",
            "password": "Test123!"
        }, headers=headers)
        assert response.status_code == 403
        
        # Try to update user status
        response = client.put(
            f"/api/auth/users/{test_user.id}/status?is_active=false",
            headers=headers
        )
        assert response.status_code == 403
        
        # Try to delete user
        response = client.delete(f"/api/auth/users/{test_user.id}", headers=headers)
        assert response.status_code == 403