"""
Unit tests for BOM Service

Tests BOM extraction, validation, export, and management functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
import json
from datetime import datetime

from services.bom_service import (
    BOMService, BOMExtractor, BOMValidator, BOMExporter,
    BOMValidationStatus, BOMValidationIssue, BOMValidationResult
)
from models.design import Design, AnalysisResult, BOMItem
from models.user import User


class TestBOMExtractor:
    """Test BOM extraction functionality"""
    
    def setup_method(self):
        self.extractor = BOMExtractor()
    
    def test_extract_part_number_standard_format(self):
        """Test extraction of standard part numbers"""
        test_cases = [
            ("Part A-123456", "A-123456"),
            ("P/N: AB-1234", "AB-1234"),
            ("PART #: XYZ-789", "XYZ-789"),
            ("Item 1234-56", "1234-56"),
        ]
        
        for text, expected in test_cases:
            result = self.extractor._extract_part_number(text)
            assert result == expected, f"Failed for text: {text}"
    
    def test_extract_quantity_various_formats(self):
        """Test extraction of quantities in different formats"""
        test_cases = [
            ("QTY: 5", 5),
            ("QUANTITY 10", 10),
            ("2 PCS", 2),
            ("3X", 3),
            ("8 EACH", 8),
        ]
        
        for text, expected in test_cases:
            result = self.extractor._extract_quantity(text)
            assert result == expected, f"Failed for text: {text}"
    
    def test_extract_material_common_types(self):
        """Test extraction of common materials"""
        test_cases = [
            ("Carbon Steel A36", "Carbon Steel A36"),
            ("ALUMINUM 6061", "ALUMINUM 6061"),
            ("Stainless Steel", "Stainless Steel"),
            ("ABS Plastic", "ABS Plastic"),
        ]
        
        for text, expected in test_cases:
            result = self.extractor._extract_material(text)
            assert expected.lower() in result.lower(), f"Failed for text: {text}"
    
    def test_standardize_material_name(self):
        """Test material name standardization"""
        test_cases = [
            ("steel", "Carbon Steel A36"),
            ("aluminum", "Aluminum 6061"),
            ("stainless", "Stainless Steel 304"),
            ("brass", "Brass 360"),
            ("plastic", "ABS Plastic"),
        ]
        
        for input_material, expected in test_cases:
            result = self.extractor._standardize_material_name(input_material)
            assert result == expected, f"Failed for material: {input_material}"
    
    def test_extract_from_parts_list(self):
        """Test extraction from parts list section"""
        ocr_results = {
            'extracted_texts': [
                {
                    'text': 'PARTS LIST',
                    'text_type': 'annotation',
                    'confidence': 95
                },
                {
                    'text': 'A-001 Main Frame',
                    'text_type': 'part_number',
                    'confidence': 85
                },
                {
                    'text': 'QTY: 1',
                    'text_type': 'annotation',
                    'confidence': 90
                },
                {
                    'text': 'Carbon Steel A36',
                    'text_type': 'material',
                    'confidence': 80
                },
                {
                    'text': 'B-002 Support Bracket',
                    'text_type': 'part_number',
                    'confidence': 88
                },
                {
                    'text': '2 PCS',
                    'text_type': 'annotation',
                    'confidence': 92
                }
            ]
        }
        
        parts = self.extractor._extract_from_parts_list(ocr_results)
        
        assert len(parts) >= 1
        assert any(part['part_number'] == 'A-001' for part in parts)
        assert any(part['part_number'] == 'B-002' for part in parts)
    
    def test_cross_reference_parts(self):
        """Test cross-referencing between parts list and views"""
        parts_list_parts = [
            {
                'part_number': 'A-001',
                'description': 'Main Frame',
                'quantity': 1,
                'material': 'Steel',
                'source': 'parts_list',
                'confidence': 85
            }
        ]
        
        view_parts = [
            {
                'part_number': 'A-001',
                'description': 'Main structural frame component',
                'quantity': 1,
                'material': '',
                'source': 'drawing_view',
                'confidence': 75
            }
        ]
        
        merged = self.extractor._cross_reference_parts(parts_list_parts, view_parts)
        
        assert len(merged) == 1
        assert merged[0]['cross_referenced'] == True
        assert merged[0]['confidence'] == 80  # Average of 85 and 75
    
    @patch('services.bom_service.logger')
    def test_extract_bom_from_analysis_success(self, mock_logger):
        """Test successful BOM extraction from analysis results"""
        analysis_result = Mock()
        analysis_result.analysis_data = {
            'cv_results': {
                'features': [
                    {'feature_type': 'rectangle', 'confidence': 80},
                    {'feature_type': 'circle', 'confidence': 75}
                ],
                'sections': [
                    {'section_type': 'main_view'},
                    {'section_type': 'parts_list'}
                ]
            },
            'ocr_results': {
                'extracted_texts': [
                    {
                        'text': 'PARTS LIST',
                        'text_type': 'annotation',
                        'confidence': 95
                    },
                    {
                        'text': 'A-001 Main Frame',
                        'text_type': 'part_number',
                        'confidence': 85
                    }
                ]
            }
        }
        
        parts = self.extractor.extract_bom_from_analysis(analysis_result)
        
        assert isinstance(parts, list)
        # Should have at least some parts extracted
        assert len(parts) >= 0


class TestBOMValidator:
    """Test BOM validation functionality"""
    
    def setup_method(self):
        self.validator = BOMValidator()
    
    def test_validate_empty_bom(self):
        """Test validation of empty BOM"""
        result = self.validator.validate_bom([])
        
        assert result.status == BOMValidationStatus.MISSING_CRITICAL
        assert result.completeness_score == 0.0
        assert len(result.issues) > 0
        assert result.issues[0].severity == 'error'
    
    def test_validate_complete_bom(self):
        """Test validation of complete BOM"""
        bom_items = [
            {
                'part_number': 'A-001',
                'description': 'Main Frame',
                'quantity': 1,
                'material': 'Carbon Steel A36',
                'confidence': 90,
                'cross_referenced': True
            },
            {
                'part_number': 'B-002',
                'description': 'Support Bracket',
                'quantity': 2,
                'material': 'Aluminum 6061',
                'confidence': 85,
                'cross_referenced': True
            }
        ]
        
        result = self.validator.validate_bom(bom_items)
        
        assert result.status in [BOMValidationStatus.COMPLETE, BOMValidationStatus.NEEDS_REVIEW]
        assert result.completeness_score > 80
        assert len([i for i in result.issues if i.severity == 'error']) == 0
    
    def test_validate_incomplete_bom(self):
        """Test validation of incomplete BOM"""
        bom_items = [
            {
                'part_number': 'A-001',
                'description': '',  # Missing description
                'quantity': 1,
                'material': '',  # Missing material
                'confidence': 30,  # Low confidence
                'cross_referenced': False
            }
        ]
        
        result = self.validator.validate_bom(bom_items)
        
        assert result.status in [BOMValidationStatus.INCOMPLETE, BOMValidationStatus.MISSING_CRITICAL]
        assert result.completeness_score < 50
        assert len([i for i in result.issues if i.severity == 'error']) > 0
        assert 'description' in result.missing_fields
    
    def test_validate_critical_part_missing_material(self):
        """Test validation flags critical parts missing materials"""
        bom_items = [
            {
                'part_number': 'FRAME-001',
                'description': 'Main structural frame',  # Critical part
                'quantity': 1,
                'material': '',  # Missing material for critical part
                'confidence': 80,
                'cross_referenced': True
            }
        ]
        
        result = self.validator.validate_bom(bom_items)
        
        # Should have error for missing material on critical part
        critical_errors = [i for i in result.issues if i.severity == 'error' and 'critical' in i.message.lower()]
        assert len(critical_errors) > 0
    
    def test_validate_low_confidence_flagging(self):
        """Test that low confidence items are flagged"""
        bom_items = [
            {
                'part_number': 'A-001',
                'description': 'Some part',
                'quantity': 1,
                'material': 'Steel',
                'confidence': 30,  # Low confidence
                'cross_referenced': True
            }
        ]
        
        result = self.validator.validate_bom(bom_items)
        
        assert 'A-001' in result.flagged_parts
        confidence_warnings = [i for i in result.issues if 'confidence' in i.field]
        assert len(confidence_warnings) > 0


class TestBOMExporter:
    """Test BOM export functionality"""
    
    def setup_method(self):
        self.exporter = BOMExporter()
    
    def test_export_to_csv_basic(self):
        """Test basic CSV export"""
        bom_items = [
            Mock(
                part_number='A-001',
                description='Main Frame',
                quantity=1,
                material='Carbon Steel A36',
                unit_weight=10.5,
                weight=10.5,
                volume=100.0
            ),
            Mock(
                part_number='B-002',
                description='Support Bracket',
                quantity=2,
                material='Aluminum 6061',
                unit_weight=2.3,
                weight=4.6,
                volume=25.0
            )
        ]
        
        csv_content = self.exporter.export_to_csv(bom_items, include_metadata=False)
        
        assert 'Part Number' in csv_content
        assert 'A-001' in csv_content
        assert 'B-002' in csv_content
        assert 'Main Frame' in csv_content
        assert 'Carbon Steel A36' in csv_content
    
    def test_export_to_csv_with_metadata(self):
        """Test CSV export with metadata"""
        bom_items = [
            Mock(
                part_number='A-001',
                description='Main Frame',
                quantity=1,
                material='Steel',
                unit_weight=10.0,
                weight=10.0,
                volume=100.0
            )
        ]
        
        csv_content = self.exporter.export_to_csv(bom_items, include_metadata=True)
        
        assert 'Confidence' in csv_content
        assert 'Cross Referenced' in csv_content
        assert 'Source' in csv_content
    
    def test_prepare_export_data(self):
        """Test preparation of structured export data"""
        bom_items = [
            Mock(
                part_number='A-001',
                description='Main Frame',
                quantity=1,
                material='Steel',
                unit_weight=10.0,
                weight=10.0,
                volume=100.0
            )
        ]
        
        design = Mock(
            name='Test Design',
            id=123,
            updated_at=datetime.now()
        )
        
        export_data = self.exporter.prepare_export_data(bom_items, design)
        
        assert export_data.headers == self.exporter.standard_headers
        assert len(export_data.rows) == 1
        assert export_data.rows[0][0] == 'A-001'  # Part number
        assert export_data.metadata['design_name'] == 'Test Design'
        assert export_data.metadata['total_parts'] == 1
        assert export_data.metadata['total_weight'] == 10.0


class TestBOMService:
    """Test main BOM service functionality"""
    
    def setup_method(self):
        self.service = BOMService()
    
    @patch('services.bom_service.get_db')
    def test_generate_bom_from_analysis_success(self, mock_get_db):
        """Test successful BOM generation from analysis"""
        # Mock database session
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock analysis result
        mock_analysis = Mock()
        mock_analysis.analysis_data = {
            'cv_results': {'features': [], 'sections': []},
            'ocr_results': {'extracted_texts': [
                {
                    'text': 'A-001 Main Frame',
                    'text_type': 'part_number',
                    'confidence': 85
                }
            ]}
        }
        
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = mock_analysis
        mock_db.query.return_value.filter.return_value.delete.return_value = None
        
        # Test the generation
        bom_items, validation_result = self.service.generate_bom_from_analysis(123)
        
        assert isinstance(bom_items, list)
        assert isinstance(validation_result, BOMValidationResult)
        mock_db.commit.assert_called()
    
    @patch('services.bom_service.get_db')
    def test_generate_bom_no_analysis_results(self, mock_get_db):
        """Test BOM generation when no analysis results exist"""
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # No analysis results found
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="No analysis results found"):
            self.service.generate_bom_from_analysis(123)
    
    @patch('services.bom_service.get_db')
    def test_get_bom_items(self, mock_get_db):
        """Test getting BOM items for a design"""
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_items = [
            Mock(part_number='A-001', description='Frame'),
            Mock(part_number='B-002', description='Bracket')
        ]
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = mock_items
        
        result = self.service.get_bom_items(123)
        
        assert len(result) == 2
        assert result[0].part_number == 'A-001'
    
    @patch('services.bom_service.get_db')
    def test_update_bom_item(self, mock_get_db):
        """Test updating a BOM item"""
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_item = Mock()
        mock_item.part_number = 'A-001'
        mock_item.description = 'Old Description'
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_item
        
        updates = {'description': 'New Description', 'quantity': 2}
        result = self.service.update_bom_item(1, updates)
        
        assert result.description == 'New Description'
        mock_db.commit.assert_called()
    
    @patch('services.bom_service.get_db')
    def test_update_bom_item_not_found(self, mock_get_db):
        """Test updating non-existent BOM item"""
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="BOM item .* not found"):
            self.service.update_bom_item(999, {'description': 'New'})
    
    @patch('services.bom_service.get_db')
    def test_delete_bom_item(self, mock_get_db):
        """Test deleting a BOM item"""
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_item = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_item
        
        result = self.service.delete_bom_item(1)
        
        assert result == True
        mock_db.delete.assert_called_with(mock_item)
        mock_db.commit.assert_called()
    
    @patch('services.bom_service.get_db')
    def test_delete_bom_item_not_found(self, mock_get_db):
        """Test deleting non-existent BOM item"""
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = self.service.delete_bom_item(999)
        
        assert result == False
    
    def test_export_bom_csv(self):
        """Test CSV export functionality"""
        with patch.object(self.service, 'get_bom_items') as mock_get_items:
            mock_items = [
                Mock(
                    part_number='A-001',
                    description='Frame',
                    quantity=1,
                    material='Steel',
                    unit_weight=10.0,
                    weight=10.0,
                    volume=100.0
                )
            ]
            mock_get_items.return_value = mock_items
            
            csv_content = self.service.export_bom_csv(123)
            
            assert 'Part Number' in csv_content
            assert 'A-001' in csv_content
    
    @patch('services.bom_service.get_db')
    def test_get_bom_export_data(self, mock_get_db):
        """Test getting structured export data"""
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_design = Mock(name='Test Design', id=123, updated_at=datetime.now())
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        
        with patch.object(self.service, 'get_bom_items') as mock_get_items:
            mock_items = [Mock(
                part_number='A-001',
                description='Frame',
                quantity=1,
                material='Steel',
                unit_weight=10.0,
                weight=10.0,
                volume=100.0
            )]
            mock_get_items.return_value = mock_items
            
            export_data = self.service.get_bom_export_data(123)
            
            assert export_data.metadata['design_name'] == 'Test Design'
            assert len(export_data.rows) == 1


# Integration test fixtures
@pytest.fixture
def sample_analysis_data():
    """Sample analysis data for testing"""
    return {
        'cv_results': {
            'features': [
                {'feature_type': 'rectangle', 'confidence': 80},
                {'feature_type': 'circle', 'confidence': 75}
            ],
            'sections': [
                {'section_type': 'main_view'},
                {'section_type': 'parts_list'}
            ]
        },
        'ocr_results': {
            'extracted_texts': [
                {
                    'text': 'PARTS LIST',
                    'text_type': 'annotation',
                    'confidence': 95
                },
                {
                    'text': 'A-001 Main Frame',
                    'text_type': 'part_number',
                    'confidence': 85
                },
                {
                    'text': 'QTY: 1',
                    'text_type': 'annotation',
                    'confidence': 90
                },
                {
                    'text': 'Carbon Steel A36',
                    'text_type': 'material',
                    'confidence': 80
                },
                {
                    'text': 'B-002 Support Bracket',
                    'text_type': 'part_number',
                    'confidence': 88
                },
                {
                    'text': '2 PCS',
                    'text_type': 'annotation',
                    'confidence': 92
                }
            ]
        }
    }


class TestBOMIntegration:
    """Integration tests for BOM functionality"""
    
    def test_full_bom_extraction_workflow(self, sample_analysis_data):
        """Test complete BOM extraction workflow"""
        extractor = BOMExtractor()
        validator = BOMValidator()
        
        # Mock analysis result
        analysis_result = Mock()
        analysis_result.analysis_data = sample_analysis_data
        
        # Extract BOM
        parts = extractor.extract_bom_from_analysis(analysis_result)
        
        # Validate BOM
        validation_result = validator.validate_bom(parts)
        
        assert len(parts) >= 1
        assert isinstance(validation_result, BOMValidationResult)
        assert validation_result.completeness_score >= 0
    
    def test_bom_export_workflow(self, sample_analysis_data):
        """Test BOM export workflow"""
        extractor = BOMExtractor()
        exporter = BOMExporter()
        
        # Mock analysis result
        analysis_result = Mock()
        analysis_result.analysis_data = sample_analysis_data
        
        # Extract parts
        parts = extractor.extract_bom_from_analysis(analysis_result)
        
        # Create mock BOM items
        mock_items = []
        for part in parts:
            mock_item = Mock()
            mock_item.part_number = part.get('part_number', '')
            mock_item.description = part.get('description', '')
            mock_item.quantity = part.get('quantity', 1)
            mock_item.material = part.get('material', '')
            mock_item.unit_weight = 0.0
            mock_item.weight = 0.0
            mock_item.volume = 0.0
            mock_items.append(mock_item)
        
        # Export to CSV
        csv_content = exporter.export_to_csv(mock_items)
        
        assert 'Part Number' in csv_content
        assert len(csv_content.split('\n')) >= 2  # Header + at least one data row