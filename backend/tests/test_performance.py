#!/usr/bin/env python3
"""
Performance testing for large file processing and system optimization
"""

import pytest
import time
import psutil
import os
import tempfile
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from PIL import Image, ImageDraw
import numpy as np
from httpx import AsyncClient
from fastapi.testclient import TestClient

from main import app
from services.file_service import FileService
from services.drawing_analysis_service import DrawingAnalysisService
from services.bom_service import BOMService
from services.model_3d_service import Model3DService


class TestPerformance:
    """Performance testing for system components"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self, client):
        """Create test user and auth headers"""
        # Register and login test user
        user_data = {
            "username": "perftest",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        client.post("/api/auth/register", json=user_data)
        
        login_data = {
            "username": "perftest",
            "password": "testpassword123"
        }
        response = client.post("/api/auth/login", json=login_data)
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def create_large_drawing(self, width=3000, height=2000):
        """Create a large engineering drawing for performance testing"""
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add complex content
        for i in range(50):
            x = i * 60
            y = i * 40
            draw.rectangle([x, y, x+50, y+30], outline='black', width=2)
            draw.text((x+5, y+5), f"PART-{i:03d}", fill='black')
        
        # Add detailed technical drawing elements
        for i in range(100):
            x = np.random.randint(0, width-100)
            y = np.random.randint(0, height-100)
            draw.line([x, y, x+50, y+50], fill='black', width=1)
            draw.circle([x+25, y+25], 10, outline='black')
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        img.save(temp_file.name, quality=95)
        return temp_file.name
    
    def test_large_file_upload_performance(self, client, auth_headers):
        """Test performance of large file uploads"""
        
        # Create files of different sizes
        test_sizes = [
            (1200, 800),    # Small: ~1MB
            (2400, 1600),   # Medium: ~4MB
            (3600, 2400),   # Large: ~9MB
            (4800, 3200),   # Very Large: ~16MB
        ]
        
        performance_results = []
        
        for width, height in test_sizes:
            drawing_file = self.create_large_drawing(width, height)
            file_size = os.path.getsize(drawing_file)
            
            # Measure upload time
            start_time = time.time()
            
            with open(drawing_file, 'rb') as f:
                files = {"file": (f"test_{width}x{height}.png", f, "image/png")}
                response = client.post(
                    "/api/files/upload",
                    files=files,
                    headers=auth_headers
                )
            
            upload_time = time.time() - start_time
            
            assert response.status_code == 201
            
            performance_results.append({
                "dimensions": f"{width}x{height}",
                "file_size_mb": file_size / (1024 * 1024),
                "upload_time": upload_time,
                "throughput_mbps": (file_size / (1024 * 1024)) / upload_time
            })
            
            # Cleanup
            os.unlink(drawing_file)
        
        # Performance assertions
        for result in performance_results:
            print(f"File {result['dimensions']}: "
                  f"{result['file_size_mb']:.1f}MB in {result['upload_time']:.2f}s "
                  f"({result['throughput_mbps']:.1f} MB/s)")
            
            # Upload should complete within reasonable time
            assert result['upload_time'] < 30.0  # Max 30 seconds
            assert result['throughput_mbps'] > 0.1  # Min 0.1 MB/s
    
    def test_analysis_performance(self, client, auth_headers):
        """Test performance of drawing analysis"""
        
        drawing_file = self.create_large_drawing(2400, 1600)
        
        # Upload file
        with open(drawing_file, 'rb') as f:
            files = {"file": ("perf_test.png", f, "image/png")}
            response = client.post(
                "/api/files/upload",
                files=files,
                headers=auth_headers
            )
        
        assert response.status_code == 201
        file_id = response.json()["id"]
        
        # Measure analysis time
        start_time = time.time()
        
        response = client.post(
            "/api/analysis/process",
            json={"file_id": file_id},
            headers=auth_headers
        )
        
        assert response.status_code == 202
        analysis_id = response.json()["analysis_id"]
        
        # Wait for completion and measure total time
        max_wait_time = 300  # 5 minutes max
        check_interval = 5   # Check every 5 seconds
        
        for elapsed in range(0, max_wait_time, check_interval):
            response = client.get(
                f"/api/analysis/{analysis_id}/status",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            status_data = response.json()
            
            if status_data["status"] == "completed":
                total_time = time.time() - start_time
                break
            elif status_data["status"] == "failed":
                pytest.fail(f"Analysis failed: {status_data.get('error')}")
            
            time.sleep(check_interval)
        else:
            pytest.fail("Analysis did not complete within timeout")
        
        print(f"Analysis completed in {total_time:.2f} seconds")
        
        # Performance assertions
        assert total_time < 180  # Should complete within 3 minutes
        
        # Cleanup
        os.unlink(drawing_file)
    
    def test_concurrent_processing(self, client, auth_headers):
        """Test system performance under concurrent load"""
        
        def process_file(file_index):
            """Process a single file and return timing data"""
            drawing_file = self.create_large_drawing(1200, 800)
            
            try:
                start_time = time.time()
                
                # Upload file
                with open(drawing_file, 'rb') as f:
                    files = {"file": (f"concurrent_{file_index}.png", f, "image/png")}
                    response = client.post(
                        "/api/files/upload",
                        files=files,
                        headers=auth_headers
                    )
                
                if response.status_code != 201:
                    return {"error": f"Upload failed: {response.status_code}"}
                
                file_id = response.json()["id"]
                
                # Start analysis
                response = client.post(
                    "/api/analysis/process",
                    json={"file_id": file_id},
                    headers=auth_headers
                )
                
                if response.status_code != 202:
                    return {"error": f"Analysis start failed: {response.status_code}"}
                
                analysis_id = response.json()["analysis_id"]
                
                # Wait for completion (with shorter timeout for concurrent test)
                for _ in range(60):  # 5 minutes max
                    response = client.get(
                        f"/api/analysis/{analysis_id}/status",
                        headers=auth_headers
                    )
                    
                    if response.status_code == 200:
                        status_data = response.json()
                        if status_data["status"] == "completed":
                            total_time = time.time() - start_time
                            return {"success": True, "time": total_time, "file_index": file_index}
                        elif status_data["status"] == "failed":
                            return {"error": f"Analysis failed for file {file_index}"}
                    
                    time.sleep(5)
                
                return {"error": f"Timeout for file {file_index}"}
                
            finally:
                if os.path.exists(drawing_file):
                    os.unlink(drawing_file)
        
        # Test with 3 concurrent files
        num_concurrent = 3
        
        with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(process_file, i) for i in range(num_concurrent)]
            results = []
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        # Analyze results
        successful_results = [r for r in results if r.get("success")]
        failed_results = [r for r in results if "error" in r]
        
        print(f"Concurrent processing results:")
        print(f"  Successful: {len(successful_results)}/{num_concurrent}")
        print(f"  Failed: {len(failed_results)}")
        
        for result in successful_results:
            print(f"  File {result['file_index']}: {result['time']:.2f}s")
        
        for result in failed_results:
            print(f"  Error: {result['error']}")
        
        # Performance assertions
        assert len(successful_results) >= num_concurrent * 0.8  # At least 80% success
        
        if successful_results:
            avg_time = sum(r["time"] for r in successful_results) / len(successful_results)
            assert avg_time < 240  # Average should be under 4 minutes
    
    def test_memory_usage(self, client, auth_headers):
        """Test memory usage during processing"""
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        drawing_file = self.create_large_drawing(3000, 2000)
        
        # Upload and process file
        with open(drawing_file, 'rb') as f:
            files = {"file": ("memory_test.png", f, "image/png")}
            response = client.post(
                "/api/files/upload",
                files=files,
                headers=auth_headers
            )
        
        assert response.status_code == 201
        file_id = response.json()["id"]
        
        # Monitor memory during analysis
        response = client.post(
            "/api/analysis/process",
            json={"file_id": file_id},
            headers=auth_headers
        )
        
        assert response.status_code == 202
        analysis_id = response.json()["analysis_id"]
        
        max_memory = initial_memory
        
        # Monitor memory usage during processing
        for _ in range(60):  # 5 minutes max
            current_memory = process.memory_info().rss / (1024 * 1024)
            max_memory = max(max_memory, current_memory)
            
            response = client.get(
                f"/api/analysis/{analysis_id}/status",
                headers=auth_headers
            )
            
            if response.status_code == 200:
                status_data = response.json()
                if status_data["status"] in ["completed", "failed"]:
                    break
            
            time.sleep(5)
        
        final_memory = process.memory_info().rss / (1024 * 1024)
        memory_increase = max_memory - initial_memory
        
        print(f"Memory usage:")
        print(f"  Initial: {initial_memory:.1f} MB")
        print(f"  Peak: {max_memory:.1f} MB")
        print(f"  Final: {final_memory:.1f} MB")
        print(f"  Max increase: {memory_increase:.1f} MB")
        
        # Memory assertions
        assert memory_increase < 1000  # Should not increase by more than 1GB
        assert final_memory < initial_memory + 200  # Should not leak more than 200MB
        
        # Cleanup
        os.unlink(drawing_file)
    
    def test_database_query_performance(self, client, auth_headers):
        """Test database query performance"""
        
        # Test dashboard query performance
        start_time = time.time()
        response = client.get("/api/users/designs", headers=auth_headers)
        query_time = time.time() - start_time
        
        assert response.status_code == 200
        assert query_time < 2.0  # Should complete within 2 seconds
        
        print(f"Dashboard query time: {query_time:.3f} seconds")
        
        # Test with pagination if many designs exist
        response = client.get("/api/users/designs?limit=10&offset=0", headers=auth_headers)
        assert response.status_code == 200
        
        # Test search query performance
        start_time = time.time()
        response = client.get("/api/users/designs?search=test", headers=auth_headers)
        search_time = time.time() - start_time
        
        assert response.status_code == 200
        assert search_time < 3.0  # Search should complete within 3 seconds
        
        print(f"Search query time: {search_time:.3f} seconds")
    
    def test_api_response_times(self, client, auth_headers):
        """Test API endpoint response times"""
        
        endpoints_to_test = [
            ("GET", "/api/users/profile"),
            ("GET", "/api/users/designs"),
            ("GET", "/api/sharing/shared-with-me"),
        ]
        
        for method, endpoint in endpoints_to_test:
            start_time = time.time()
            
            if method == "GET":
                response = client.get(endpoint, headers=auth_headers)
            elif method == "POST":
                response = client.post(endpoint, headers=auth_headers)
            
            response_time = time.time() - start_time
            
            print(f"{method} {endpoint}: {response_time:.3f}s")
            
            # Response time assertions
            assert response_time < 5.0  # All endpoints should respond within 5 seconds
            
            # Most endpoints should be much faster
            if endpoint in ["/api/users/profile"]:
                assert response_time < 1.0


class TestLoadTesting:
    """Load testing for concurrent user scenarios"""
    
    def test_concurrent_users(self):
        """Test system with multiple concurrent users"""
        
        def simulate_user(user_id):
            """Simulate a single user's actions"""
            client = TestClient(app)
            
            try:
                # Register user
                user_data = {
                    "username": f"loadtest_{user_id}",
                    "email": f"load{user_id}@example.com",
                    "password": "testpassword123"
                }
                response = client.post("/api/auth/register", json=user_data)
                if response.status_code != 201:
                    return {"error": f"Registration failed for user {user_id}"}
                
                # Login
                login_data = {
                    "username": f"loadtest_{user_id}",
                    "password": "testpassword123"
                }
                response = client.post("/api/auth/login", json=login_data)
                if response.status_code != 200:
                    return {"error": f"Login failed for user {user_id}"}
                
                token = response.json()["access_token"]
                headers = {"Authorization": f"Bearer {token}"}
                
                # Perform typical user actions
                actions_completed = 0
                
                # Check dashboard
                response = client.get("/api/users/designs", headers=headers)
                if response.status_code == 200:
                    actions_completed += 1
                
                # Check profile
                response = client.get("/api/users/profile", headers=headers)
                if response.status_code == 200:
                    actions_completed += 1
                
                # Check shared designs
                response = client.get("/api/sharing/shared-with-me", headers=headers)
                if response.status_code == 200:
                    actions_completed += 1
                
                return {"success": True, "user_id": user_id, "actions": actions_completed}
                
            except Exception as e:
                return {"error": f"Exception for user {user_id}: {str(e)}"}
        
        # Test with 10 concurrent users
        num_users = 10
        
        with ThreadPoolExecutor(max_workers=num_users) as executor:
            futures = [executor.submit(simulate_user, i) for i in range(num_users)]
            results = []
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        # Analyze results
        successful_users = [r for r in results if r.get("success")]
        failed_users = [r for r in results if "error" in r]
        
        print(f"Load test results:")
        print(f"  Successful users: {len(successful_users)}/{num_users}")
        print(f"  Failed users: {len(failed_users)}")
        
        for result in failed_users:
            print(f"  Error: {result['error']}")
        
        # Load test assertions
        success_rate = len(successful_users) / num_users
        assert success_rate >= 0.8  # At least 80% success rate
        
        if successful_users:
            avg_actions = sum(r["actions"] for r in successful_users) / len(successful_users)
            assert avg_actions >= 2  # Users should complete at least 2 actions on average


if __name__ == "__main__":
    pytest.main([__file__, "-v"])