"""
Test configuration and fixtures.
"""
import pytest
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# Set testing environment variable
os.environ['TESTING'] = 'true'

from database.connection import Base, get_database
from main import app
from models.user import User, UserSession
from services.auth_service import AuthService

# Test database URL
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False}
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_database():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database override."""
    app.dependency_overrides[get_database] = override_get_database
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def auth_service(db_session):
    """Create an AuthService instance for testing."""
    return AuthService(db_session)


@pytest.fixture
def test_user_data():
    """Test user data."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPass123!"
    }


@pytest.fixture
def admin_user_data():
    """Test admin user data."""
    return {
        "username": "adminuser",
        "email": "<EMAIL>",
        "password": "AdminPass123!"
    }


@pytest.fixture
def test_user(auth_service, test_user_data):
    """Create a test user."""
    return auth_service.create_user(
        username=test_user_data["username"],
        email=test_user_data["email"],
        password=test_user_data["password"]
    )


@pytest.fixture
def admin_user(auth_service, admin_user_data):
    """Create an admin user."""
    return auth_service.create_user(
        username=admin_user_data["username"],
        email=admin_user_data["email"],
        password=admin_user_data["password"],
        is_admin=True
    )


@pytest.fixture
def user_token(auth_service, test_user):
    """Create access token for test user."""
    tokens = auth_service.create_user_session(test_user)
    return tokens["access_token"]


@pytest.fixture
def admin_token(auth_service, admin_user):
    """Create access token for admin user."""
    tokens = auth_service.create_user_session(admin_user)
    return tokens["access_token"]