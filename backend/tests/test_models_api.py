"""
Tests for 3D Models API endpoints
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from models.user import User
from models.design import Design, Model3D
from services.model_3d_service import model_3d_service


client = TestClient(app)


@pytest.fixture
def mock_current_user():
    """Mock current user for authentication."""
    user = Mock(spec=User)
    user.id = 1
    user.username = "testuser"
    user.email = "<EMAIL>"
    return user


@pytest.fixture
def mock_design():
    """Mock design object."""
    design = Mock(spec=Design)
    design.id = 1
    design.user_id = 1
    design.name = "Test Design"
    return design


@pytest.fixture
def mock_model_3d():
    """Mock 3D model object."""
    model = Mock(spec=Model3D)
    model.id = 1
    model.design_id = 1
    model.model_file_path = "/tmp/test_model.stl"
    model.openscad_script = "cube([10, 10, 10]);"
    model.generation_time = 30
    model.file_size = 1024
    model.created_at = Mock()
    model.created_at.isoformat.return_value = "2025-01-18T10:00:00"
    return model


class TestGenerateModel:
    """Test cases for 3D model generation endpoint."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_by_design_id')
    @patch('services.model_3d_service.model_3d_service.generate_3d_model')
    def test_generate_model_success(self, mock_generate, mock_get_existing, 
                                  mock_get_db, mock_get_user, mock_current_user, 
                                  mock_design, mock_model_3d):
        """Test successful 3D model generation."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        mock_get_existing.return_value = None  # No existing model
        mock_generate.return_value = mock_model_3d
        
        # Make request
        response = client.post("/api/models/generate", json={"design_id": 1})
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "3D model generated successfully"
        assert data["model_id"] == 1
        
        # Verify service calls
        mock_generate.assert_called_once_with(1, mock_db)
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_by_design_id')
    def test_generate_model_already_exists(self, mock_get_existing, mock_get_db, 
                                         mock_get_user, mock_current_user, 
                                         mock_design, mock_model_3d):
        """Test 3D model generation when model already exists."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        mock_get_existing.return_value = mock_model_3d  # Existing model
        
        # Make request
        response = client.post("/api/models/generate", json={"design_id": 1})
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "3D model already exists for this design"
        assert data["model_id"] == 1
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    def test_generate_model_design_not_found(self, mock_get_db, mock_get_user, 
                                           mock_current_user):
        """Test 3D model generation with non-existent design."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Make request
        response = client.post("/api/models/generate", json={"design_id": 999})
        
        # Verify response
        assert response.status_code == 404
        assert "Design not found" in response.json()["detail"]
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_by_design_id')
    @patch('services.model_3d_service.model_3d_service.generate_3d_model')
    def test_generate_model_generation_failure(self, mock_generate, mock_get_existing,
                                             mock_get_db, mock_get_user, 
                                             mock_current_user, mock_design):
        """Test 3D model generation failure."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        mock_get_existing.return_value = None
        mock_generate.return_value = None  # Generation failed
        
        # Make request
        response = client.post("/api/models/generate", json={"design_id": 1})
        
        # Verify response
        assert response.status_code == 500
        assert "Failed to generate 3D model" in response.json()["detail"]


class TestGetModel:
    """Test cases for getting 3D model information."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    def test_get_model_success(self, mock_get_db, mock_get_user, 
                             mock_current_user, mock_model_3d):
        """Test successful model retrieval."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
        
        # Make request
        response = client.get("/api/models/1")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["design_id"] == 1
        assert data["model_file_path"] == "/tmp/test_model.stl"
        assert data["generation_time"] == 30
        assert data["file_size"] == 1024
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    def test_get_model_not_found(self, mock_get_db, mock_get_user, mock_current_user):
        """Test model retrieval when model not found."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = None
        
        # Make request
        response = client.get("/api/models/999")
        
        # Verify response
        assert response.status_code == 404
        assert "3D model not found" in response.json()["detail"]


class TestGetModelByDesign:
    """Test cases for getting model by design ID."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_by_design_id')
    def test_get_model_by_design_success(self, mock_get_model, mock_get_db, 
                                       mock_get_user, mock_current_user, 
                                       mock_design, mock_model_3d):
        """Test successful model retrieval by design ID."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        mock_get_model.return_value = mock_model_3d
        
        # Make request
        response = client.get("/api/models/design/1")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["design_id"] == 1
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_by_design_id')
    def test_get_model_by_design_no_model(self, mock_get_model, mock_get_db, 
                                        mock_get_user, mock_current_user, mock_design):
        """Test model retrieval by design ID when no model exists."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        mock_get_model.return_value = None
        
        # Make request
        response = client.get("/api/models/design/1")
        
        # Verify response
        assert response.status_code == 200
        assert response.json() is None


class TestDownloadSTL:
    """Test cases for STL file download."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_file_path')
    def test_download_stl_success(self, mock_get_path, mock_get_db, 
                                mock_get_user, mock_current_user, mock_model_3d):
        """Test successful STL file download."""
        # Create temporary STL file
        with tempfile.NamedTemporaryFile(suffix='.stl', delete=False) as temp_file:
            temp_file.write(b'STL test data')
            temp_path = temp_file.name
        
        try:
            # Setup mocks
            mock_get_user.return_value = mock_current_user
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = mock_db
            mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
            mock_get_path.return_value = temp_path
            
            # Make request
            response = client.get("/api/models/1/download/stl")
            
            # Verify response
            assert response.status_code == 200
            assert response.headers["content-type"] == "application/octet-stream"
            assert "model_1.stl" in response.headers.get("content-disposition", "")
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_file_path')
    def test_download_stl_file_not_found(self, mock_get_path, mock_get_db, 
                                       mock_get_user, mock_current_user, mock_model_3d):
        """Test STL download when file not found."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
        mock_get_path.return_value = None  # File not found
        
        # Make request
        response = client.get("/api/models/1/download/stl")
        
        # Verify response
        assert response.status_code == 404
        assert "Model file not found" in response.json()["detail"]


class TestGetOpenSCADScript:
    """Test cases for OpenSCAD script retrieval."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    def test_get_script_success(self, mock_get_db, mock_get_user, 
                              mock_current_user, mock_model_3d):
        """Test successful OpenSCAD script retrieval."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
        
        # Make request
        response = client.get("/api/models/1/script")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["script"] == "cube([10, 10, 10]);"
        assert data["design_id"] == 1
        assert data["model_id"] == 1
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    def test_get_script_no_script(self, mock_get_db, mock_get_user, 
                                mock_current_user, mock_model_3d):
        """Test OpenSCAD script retrieval when no script available."""
        # Setup mocks
        mock_model_3d.openscad_script = None
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
        
        # Make request
        response = client.get("/api/models/1/script")
        
        # Verify response
        assert response.status_code == 404
        assert "OpenSCAD script not available" in response.json()["detail"]


class TestValidateModel:
    """Test cases for model validation."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.validate_model_file')
    def test_validate_model_success(self, mock_validate, mock_get_db, 
                                  mock_get_user, mock_current_user, mock_model_3d):
        """Test successful model validation."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
        mock_validate.return_value = {
            'is_valid': True,
            'file_exists': True,
            'file_size': 1024,
            'errors': []
        }
        
        # Make request
        response = client.get("/api/models/1/validate")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["is_valid"] is True
        assert data["file_exists"] is True
        assert data["file_size"] == 1024
        assert len(data["errors"]) == 0


class TestDeleteModel:
    """Test cases for model deletion."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.delete_model')
    def test_delete_model_success(self, mock_delete, mock_get_db, 
                                mock_get_user, mock_current_user, mock_model_3d):
        """Test successful model deletion."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
        mock_delete.return_value = True
        
        # Make request
        response = client.delete("/api/models/1")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "3D model deleted successfully"
        
        # Verify service call
        mock_delete.assert_called_once_with(1, mock_db)
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.delete_model')
    def test_delete_model_failure(self, mock_delete, mock_get_db, 
                                mock_get_user, mock_current_user, mock_model_3d):
        """Test model deletion failure."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_model_3d
        mock_delete.return_value = False
        
        # Make request
        response = client.delete("/api/models/1")
        
        # Verify response
        assert response.status_code == 500
        assert "Failed to delete 3D model" in response.json()["detail"]


class TestRegenerateModel:
    """Test cases for model regeneration."""
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_by_design_id')
    @patch('services.model_3d_service.model_3d_service.delete_model')
    @patch('services.model_3d_service.model_3d_service.generate_3d_model')
    def test_regenerate_model_success(self, mock_generate, mock_delete, 
                                    mock_get_existing, mock_get_db, mock_get_user,
                                    mock_current_user, mock_design, mock_model_3d):
        """Test successful model regeneration."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        mock_get_existing.return_value = mock_model_3d  # Existing model
        mock_delete.return_value = True
        mock_generate.return_value = mock_model_3d
        
        # Make request
        response = client.post("/api/models/regenerate/1")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "3D model regenerated successfully"
        assert data["model_id"] == 1
        
        # Verify service calls
        mock_delete.assert_called_once_with(1, mock_db)
        mock_generate.assert_called_once_with(1, mock_db)
    
    @patch('api.models.get_current_user')
    @patch('api.models.get_db')
    @patch('services.model_3d_service.model_3d_service.get_model_by_design_id')
    @patch('services.model_3d_service.model_3d_service.generate_3d_model')
    def test_regenerate_model_no_existing(self, mock_generate, mock_get_existing,
                                        mock_get_db, mock_get_user, mock_current_user,
                                        mock_design, mock_model_3d):
        """Test model regeneration when no existing model."""
        # Setup mocks
        mock_get_user.return_value = mock_current_user
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = mock_design
        mock_get_existing.return_value = None  # No existing model
        mock_generate.return_value = mock_model_3d
        
        # Make request
        response = client.post("/api/models/regenerate/1")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "3D model regenerated successfully"
        
        # Verify only generate was called (no delete)
        mock_generate.assert_called_once_with(1, mock_db)