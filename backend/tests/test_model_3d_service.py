"""
Tests for 3D Model Generation Service
"""
import pytest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from services.model_3d_service import Model3DService, OpenSCADGenerator
from models.design import Design, Model3D, AnalysisResult
from models.user import User


class TestOpenSCADGenerator:
    """Test cases for OpenSCAD script generation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        try:
            self.generator = OpenSCADGenerator()
        except RuntimeError:
            # OpenSCAD not found, create mock generator
            self.generator = OpenSCADGenerator.__new__(OpenSCADGenerator)
            self.generator.openscad_executable = "openscad"
    
    @patch('services.model_3d_service.subprocess.run')
    def test_find_openscad_executable_success(self, mock_run):
        """Test successful OpenSCAD executable detection."""
        mock_run.return_value = Mock(returncode=0)
        
        generator = OpenSCADGenerator()
        assert generator.openscad_executable == "openscad"
    
    @patch('services.model_3d_service.subprocess.run')
    def test_find_openscad_executable_failure(self, mock_run):
        """Test OpenSCAD executable not found."""
        mock_run.side_effect = FileNotFoundError()
        
        with pytest.raises(RuntimeError, match="OpenSCAD executable not found"):
            OpenSCADGenerator()
    
    def test_generate_part_script_with_features(self):
        """Test OpenSCAD script generation for part with features."""
        part_data = {
            'part_number': 'TEST-001',
            'features': [
                {
                    'type': 'contour',
                    'closed': True,
                    'area': 1000,
                    'points': [[0, 0], [50, 0], [50, 30], [0, 30]]
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [25, 15],
                    'radius': 5.0
                }
            ],
            'dimensions': {
                'thickness': 10.0,
                'width': 50.0,
                'height': 30.0
            }
        }
        
        script = self.generator.generate_part_script(part_data)
        
        # Check script contains expected elements
        assert "// Part: TEST-001" in script
        assert "linear_extrude(height=10.0)" in script
        assert "polygon([[0, 0], [50, 0], [50, 30], [0, 30]])" in script
        assert "cylinder(h=10.2, r=5.0)" in script
        assert "translate([25, 15, -0.1])" in script
    
    def test_generate_part_script_fallback(self):
        """Test OpenSCAD script generation fallback for part without features."""
        part_data = {
            'part_number': 'TEST-002',
            'features': [],
            'dimensions': {
                'width': 40.0,
                'height': 20.0,
                'thickness': 5.0
            }
        }
        
        script = self.generator.generate_part_script(part_data)
        
        # Check fallback cube generation
        assert "cube([40.0, 20.0, 5.0])" in script
    
    def test_generate_assembly_script(self):
        """Test OpenSCAD assembly script generation."""
        parts_data = [
            {
                'part_number': 'PART-001',
                'features': [],
                'dimensions': {'width': 30, 'height': 30, 'thickness': 5}
            },
            {
                'part_number': 'PART-002',
                'features': [],
                'dimensions': {'width': 20, 'height': 20, 'thickness': 3}
            }
        ]
        
        script = self.generator.generate_assembly_script(parts_data)
        
        # Check assembly structure
        assert "// Generated OpenSCAD assembly script" in script
        assert "// Part: PART-001" in script
        assert "// Part: PART-002" in script
        assert "translate([0.0, 0.0, 0.0])" in script
        assert "translate([100.0, 0.0, 0.0])" in script
    
    def test_extract_main_profile(self):
        """Test main profile extraction from features."""
        features = [
            {'type': 'contour', 'closed': True, 'area': 500},
            {'type': 'contour', 'closed': True, 'area': 1000},
            {'type': 'line', 'closed': False}
        ]
        
        profile = self.generator._extract_main_profile(features)
        
        assert profile is not None
        assert profile['area'] == 1000
    
    def test_extract_holes(self):
        """Test hole extraction from features."""
        features = [
            {'type': 'circle', 'is_hole': True, 'radius': 5},
            {'type': 'circle', 'is_hole': False, 'radius': 10},
            {'type': 'contour', 'closed': True}
        ]
        
        holes = self.generator._extract_holes(features)
        
        assert len(holes) == 1
        assert holes[0]['radius'] == 5
    
    @patch('services.model_3d_service.subprocess.run')
    def test_execute_openscad_success(self, mock_run):
        """Test successful OpenSCAD execution."""
        mock_run.return_value = Mock(returncode=0)
        
        with tempfile.NamedTemporaryFile(suffix='.stl', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            script = "cube([10, 10, 10]);"
            result = self.generator.execute_openscad(script, temp_path)
            
            assert result is True
            mock_run.assert_called_once()
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    @patch('services.model_3d_service.subprocess.run')
    def test_execute_openscad_failure(self, mock_run):
        """Test OpenSCAD execution failure."""
        mock_run.return_value = Mock(returncode=1, stderr="Syntax error")
        
        script = "invalid_openscad_code"
        result = self.generator.execute_openscad(script, "/tmp/test.stl")
        
        assert result is False
    
    @patch('services.model_3d_service.subprocess.run')
    def test_execute_openscad_timeout(self, mock_run):
        """Test OpenSCAD execution timeout."""
        from subprocess import TimeoutExpired
        mock_run.side_effect = TimeoutExpired("openscad", 300)
        
        script = "cube([10, 10, 10]);"
        result = self.generator.execute_openscad(script, "/tmp/test.stl")
        
        assert result is False


class TestModel3DService:
    """Test cases for 3D Model Service."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.service = Model3DService()
        self.mock_db = Mock(spec=Session)
    
    def test_extract_parts_from_analysis_with_bom(self):
        """Test parts extraction from analysis data with BOM."""
        analysis_data = {
            'bom': {
                'items': [
                    {
                        'part_number': 'PART-001',
                        'description': 'Main plate',
                        'material': 'Steel'
                    },
                    {
                        'part_number': 'PART-002',
                        'description': 'Support bracket',
                        'material': 'Aluminum'
                    }
                ]
            },
            'computer_vision': {
                'features': [
                    {'type': 'contour', 'area': 1000}
                ],
                'dimensions': {'width': 100, 'height': 50}
            }
        }
        
        parts = self.service._extract_parts_from_analysis(analysis_data)
        
        assert len(parts) == 2
        assert parts[0]['part_number'] == 'PART-001'
        assert parts[0]['material'] == 'Steel'
        assert parts[0]['features'] == [{'type': 'contour', 'area': 1000}]
        assert parts[1]['part_number'] == 'PART-002'
        assert parts[1]['material'] == 'Aluminum'
    
    def test_extract_parts_from_analysis_without_bom(self):
        """Test parts extraction from analysis data without BOM."""
        analysis_data = {
            'computer_vision': {
                'features': [
                    {'type': 'contour', 'area': 1000},
                    {'type': 'circle', 'radius': 10}
                ],
                'dimensions': {'width': 100, 'height': 50, 'thickness': 10}
            }
        }
        
        parts = self.service._extract_parts_from_analysis(analysis_data)
        
        assert len(parts) == 1
        assert parts[0]['part_number'] == 'main_part'
        assert parts[0]['material'] == 'Steel'
        assert len(parts[0]['features']) == 2
        assert parts[0]['dimensions']['width'] == 100
    
    def test_extract_parts_from_analysis_empty(self):
        """Test parts extraction from empty analysis data."""
        analysis_data = {}
        
        parts = self.service._extract_parts_from_analysis(analysis_data)
        
        assert len(parts) == 0
    
    @patch('services.model_3d_service.time.time')
    def test_generate_3d_model_success(self, mock_time):
        """Test successful 3D model generation."""
        mock_time.return_value = 1000.0
        
        # Mock database objects
        mock_design = Mock(spec=Design)
        mock_design.id = 1
        
        mock_analysis = Mock(spec=AnalysisResult)
        mock_analysis.analysis_data = {
            'computer_vision': {
                'features': [{'type': 'contour', 'area': 1000}],
                'dimensions': {'width': 50, 'height': 30, 'thickness': 10}
            }
        }
        
        # Configure database queries
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            mock_design,  # Design query
            mock_analysis  # AnalysisResult query
        ]
        
        # Mock OpenSCAD generator
        with patch.object(self.service.openscad_generator, 'generate_part_script') as mock_generate:
            mock_generate.return_value = "cube([50, 30, 10]);"
            
            with patch.object(self.service.openscad_generator, 'execute_openscad') as mock_execute:
                mock_execute.return_value = True
                
                with patch('services.model_3d_service.Path.stat') as mock_stat:
                    mock_stat.return_value.st_size = 1024
                    
                    with patch('services.model_3d_service.Path.exists') as mock_exists:
                        mock_exists.return_value = True
                        
                        result = self.service.generate_3d_model(1, self.mock_db)
        
        # Verify result
        assert result is not None
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
    
    def test_generate_3d_model_no_design(self):
        """Test 3D model generation with non-existent design."""
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = self.service.generate_3d_model(999, self.mock_db)
        
        assert result is None
    
    def test_generate_3d_model_no_analysis(self):
        """Test 3D model generation with no analysis data."""
        mock_design = Mock(spec=Design)
        mock_design.id = 1
        
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            mock_design,  # Design query
            None  # AnalysisResult query
        ]
        
        result = self.service.generate_3d_model(1, self.mock_db)
        
        assert result is None
    
    @patch('services.model_3d_service.time.time')
    def test_generate_3d_model_openscad_failure(self, mock_time):
        """Test 3D model generation with OpenSCAD execution failure."""
        mock_time.return_value = 1000.0
        
        # Mock database objects
        mock_design = Mock(spec=Design)
        mock_design.id = 1
        
        mock_analysis = Mock(spec=AnalysisResult)
        mock_analysis.analysis_data = {
            'computer_vision': {
                'features': [{'type': 'contour', 'area': 1000}],
                'dimensions': {'width': 50, 'height': 30, 'thickness': 10}
            }
        }
        
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            mock_design,
            mock_analysis
        ]
        
        # Mock OpenSCAD generator failure
        with patch.object(self.service.openscad_generator, 'generate_part_script') as mock_generate:
            mock_generate.return_value = "cube([50, 30, 10]);"
            
            with patch.object(self.service.openscad_generator, 'execute_openscad') as mock_execute:
                mock_execute.return_value = False
                
                result = self.service.generate_3d_model(1, self.mock_db)
        
        assert result is None
    
    def test_get_model_by_design_id(self):
        """Test getting model by design ID."""
        mock_model = Mock(spec=Model3D)
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_model
        
        result = self.service.get_model_by_design_id(1, self.mock_db)
        
        assert result == mock_model
    
    def test_get_model_file_path_exists(self):
        """Test getting model file path when file exists."""
        mock_model = Mock(spec=Model3D)
        mock_model.model_file_path = "/tmp/test_model.stl"
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_model
        
        with patch('services.model_3d_service.Path.exists') as mock_exists:
            mock_exists.return_value = True
            
            result = self.service.get_model_file_path(1, self.mock_db)
            
            assert result == "/tmp/test_model.stl"
    
    def test_get_model_file_path_not_exists(self):
        """Test getting model file path when file doesn't exist."""
        mock_model = Mock(spec=Model3D)
        mock_model.model_file_path = "/tmp/nonexistent.stl"
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_model
        
        with patch('services.model_3d_service.Path.exists') as mock_exists:
            mock_exists.return_value = False
            
            result = self.service.get_model_file_path(1, self.mock_db)
            
            assert result is None
    
    def test_delete_model_success(self):
        """Test successful model deletion."""
        mock_model = Mock(spec=Model3D)
        mock_model.model_file_path = "/tmp/test_model.stl"
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_model
        
        with patch('services.model_3d_service.Path.exists') as mock_exists:
            mock_exists.return_value = True
            
            with patch('services.model_3d_service.Path.unlink') as mock_unlink:
                result = self.service.delete_model(1, self.mock_db)
                
                assert result is True
                mock_unlink.assert_called_once()
                self.mock_db.delete.assert_called_once_with(mock_model)
                self.mock_db.commit.assert_called_once()
    
    def test_delete_model_not_found(self):
        """Test model deletion when model not found."""
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = self.service.delete_model(999, self.mock_db)
        
        assert result is False
    
    def test_validate_model_file_valid(self):
        """Test model file validation for valid file."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            # Write STL header (80 bytes) + some data
            temp_file.write(b'0' * 80)  # STL header
            temp_file.write(b'test data')
            temp_path = temp_file.name
        
        try:
            result = self.service.validate_model_file(temp_path)
            
            assert result['is_valid'] is True
            assert result['file_exists'] is True
            assert result['file_size'] > 80
            assert len(result['errors']) == 0
            
        finally:
            os.unlink(temp_path)
    
    def test_validate_model_file_not_exists(self):
        """Test model file validation for non-existent file."""
        result = self.service.validate_model_file("/tmp/nonexistent.stl")
        
        assert result['is_valid'] is False
        assert result['file_exists'] is False
        assert "does not exist" in result['errors'][0]
    
    def test_validate_model_file_empty(self):
        """Test model file validation for empty file."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            result = self.service.validate_model_file(temp_path)
            
            assert result['is_valid'] is False
            assert result['file_exists'] is True
            assert result['file_size'] == 0
            assert "empty" in result['errors'][0]
            
        finally:
            os.unlink(temp_path)
    
    def test_validate_model_file_invalid_format(self):
        """Test model file validation for invalid STL format."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b'short')  # Less than 80 bytes
            temp_path = temp_file.name
        
        try:
            result = self.service.validate_model_file(temp_path)
            
            assert result['is_valid'] is False
            assert "Invalid STL file format" in result['errors'][0]
            
        finally:
            os.unlink(temp_path)


@pytest.fixture
def sample_geometric_data():
    """Sample geometric data for testing."""
    return {
        'computer_vision': {
            'features': [
                {
                    'type': 'contour',
                    'closed': True,
                    'area': 2500,
                    'points': [[0, 0], [50, 0], [50, 50], [0, 50]]
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [25, 25],
                    'radius': 8.0
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [10, 10],
                    'radius': 3.0
                }
            ],
            'dimensions': {
                'width': 50.0,
                'height': 50.0,
                'thickness': 12.0
            }
        },
        'bom': {
            'items': [
                {
                    'part_number': 'PLATE-001',
                    'description': 'Main mounting plate',
                    'material': 'Steel A36',
                    'quantity': 1
                }
            ]
        }
    }


class TestIntegration:
    """Integration tests for 3D model generation with sample data."""
    
    def test_complete_workflow_with_sample_data(self, sample_geometric_data):
        """Test complete 3D model generation workflow with sample data."""
        try:
            generator = OpenSCADGenerator()
        except RuntimeError:
            # OpenSCAD not found, create mock generator
            generator = OpenSCADGenerator.__new__(OpenSCADGenerator)
            generator.openscad_executable = "openscad"
        service = Model3DService()
        
        # Extract parts from sample data
        parts = service._extract_parts_from_analysis(sample_geometric_data)
        
        assert len(parts) == 1
        part = parts[0]
        
        # Generate OpenSCAD script
        script = generator.generate_part_script(part)
        
        # Verify script contains expected elements
        assert "PLATE-001" in script
        assert "linear_extrude(height=12.0)" in script
        assert "polygon([[0, 0], [50, 0], [50, 50], [0, 50]])" in script
        assert "cylinder(h=12.2, r=8.0)" in script
        assert "translate([25, 25, -0.1])" in script
        assert "cylinder(h=12.2, r=3.0)" in script
        assert "translate([10, 10, -0.1])" in script
    
    def test_assembly_generation_with_multiple_parts(self):
        """Test assembly generation with multiple parts."""
        try:
            generator = OpenSCADGenerator()
        except RuntimeError:
            # OpenSCAD not found, create mock generator
            generator = OpenSCADGenerator.__new__(OpenSCADGenerator)
            generator.openscad_executable = "openscad"
        
        parts_data = [
            {
                'part_number': 'BASE-001',
                'features': [
                    {
                        'type': 'contour',
                        'closed': True,
                        'area': 4000,
                        'points': [[0, 0], [80, 0], [80, 50], [0, 50]]
                    }
                ],
                'dimensions': {'thickness': 15.0}
            },
            {
                'part_number': 'BRACKET-001',
                'features': [
                    {
                        'type': 'contour',
                        'closed': True,
                        'area': 1000,
                        'points': [[0, 0], [30, 0], [30, 30], [0, 30]]
                    }
                ],
                'dimensions': {'thickness': 8.0}
            }
        ]
        
        script = generator.generate_assembly_script(parts_data)
        
        # Verify assembly structure
        assert "// Generated OpenSCAD assembly script" in script
        assert "// Part: BASE-001" in script
        assert "// Part: BRACKET-001" in script
        assert "translate([0.0, 0.0, 0.0])" in script
        assert "translate([100.0, 0.0, 0.0])" in script
        assert "linear_extrude(height=15.0)" in script
        assert "linear_extrude(height=8.0)" in script