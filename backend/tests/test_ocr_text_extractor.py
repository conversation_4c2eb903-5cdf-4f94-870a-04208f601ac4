"""
Unit tests for OCR text extraction functionality
"""

import pytest
import numpy as np
import cv2
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os

from processing.ocr.text_extractor import (
    OCRTextExtractor, TextPreprocessor, PatternMatcher, TextValidator,
    ExtractedText, OCRResult, TextType
)


class TestTextPreprocessor:
    """Test cases for TextPreprocessor class"""
    
    def setup_method(self):
        self.preprocessor = TextPreprocessor()
    
    def test_preprocess_for_ocr_grayscale_conversion(self):
        """Test grayscale conversion in preprocessing"""
        # Create a color test image
        color_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        result = self.preprocessor.preprocess_for_ocr(color_image)
        
        assert len(result.shape) == 2  # Should be grayscale
        assert result.dtype == np.uint8
    
    def test_preprocess_for_ocr_already_grayscale(self):
        """Test preprocessing with already grayscale image"""
        gray_image = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        
        result = self.preprocessor.preprocess_for_ocr(gray_image)
        
        assert len(result.shape) == 2
        assert result.shape == gray_image.shape
    
    def test_enhance_text_regions_small_text(self):
        """Test enhancement of small text regions"""
        image = np.ones((200, 200), dtype=np.uint8) * 255
        small_bbox = (10, 10, 50, 20)  # Small text region
        
        result = self.preprocessor.enhance_text_regions(image, small_bbox)
        
        # Should be upscaled
        assert result.shape[0] > 20
        assert result.shape[1] > 50
    
    def test_enhance_text_regions_large_text(self):
        """Test enhancement of large text regions"""
        image = np.ones((200, 200), dtype=np.uint8) * 255
        large_bbox = (10, 10, 150, 80)  # Large text region
        
        result = self.preprocessor.enhance_text_regions(image, large_bbox)
        
        # Should maintain similar size
        assert result.shape[0] == 80
        assert result.shape[1] == 150


class TestPatternMatcher:
    """Test cases for PatternMatcher class"""
    
    def setup_method(self):
        self.matcher = PatternMatcher()
    
    def test_classify_dimension_text(self):
        """Test dimension text classification"""
        dimension_texts = [
            "2.50±0.01",
            "1/4",
            "3.25\"",
            "R0.125",
            "Ø1.5",
            "2.0 X 3.0"
        ]
        
        for text in dimension_texts:
            result = self.matcher.classify_text(text)
            assert result == TextType.DIMENSION, f"Failed to classify '{text}' as dimension"
    
    def test_classify_part_number_text(self):
        """Test part number text classification"""
        part_number_texts = [
            "ABC-123456",
            "1234-AB",
            "P/N: XYZ789",
            "PART #: DEF456"
        ]
        
        for text in part_number_texts:
            result = self.matcher.classify_text(text)
            assert result == TextType.PART_NUMBER, f"Failed to classify '{text}' as part number"
    
    def test_classify_material_text(self):
        """Test material text classification"""
        material_texts = [
            "STEEL A36",
            "ALUMINUM 6061",
            "STAINLESS 304",
            "BRASS 360"
        ]
        
        for text in material_texts:
            result = self.matcher.classify_text(text)
            assert result == TextType.MATERIAL, f"Failed to classify '{text}' as material"
    
    def test_classify_annotation_text(self):
        """Test annotation text classification"""
        annotation_texts = [
            "NOTE: ALL DIMENSIONS IN INCHES",
            "SCALE 1:1",
            "TITLE BLOCK",
            "DWG NO. 12345",
            "REV A"
        ]
        
        for text in annotation_texts:
            result = self.matcher.classify_text(text)
            assert result == TextType.ANNOTATION, f"Failed to classify '{text}' as annotation"
    
    def test_extract_dimensions(self):
        """Test dimension extraction from text"""
        text = "Dimensions: 2.50±0.01, 1/4, and R0.125"
        
        dimensions = self.matcher.extract_dimensions(text)
        
        assert len(dimensions) >= 2  # Should find at least 2 dimensions
        assert any("2.50±0.01" in dim for dim in dimensions)
    
    def test_extract_part_numbers(self):
        """Test part number extraction from text"""
        text = "Part numbers: ABC-123456 and P/N: XYZ789"
        
        part_numbers = self.matcher.extract_part_numbers(text)
        
        assert len(part_numbers) >= 1
        assert any("ABC-123456" in pn for pn in part_numbers)
    
    def test_extract_materials(self):
        """Test material extraction from text"""
        text = "Materials: STEEL A36 and ALUMINUM 6061"
        
        materials = self.matcher.extract_materials(text)
        
        assert len(materials) >= 2
        assert any("STEEL A36" in mat for mat in materials)
        assert any("ALUMINUM 6061" in mat for mat in materials)


class TestTextValidator:
    """Test cases for TextValidator class"""
    
    def setup_method(self):
        self.validator = TextValidator()
    
    def test_validate_text_empty(self):
        """Test validation of empty text"""
        is_valid, confidence, warnings = self.validator.validate_text("", 50.0, TextType.GENERAL)
        
        assert not is_valid
        assert confidence == 0.0
        assert len(warnings) > 0
    
    def test_validate_text_high_confidence(self):
        """Test validation of high confidence text"""
        is_valid, confidence, warnings = self.validator.validate_text("ABC123", 85.0, TextType.PART_NUMBER)
        
        assert is_valid
        assert confidence == 85.0
    
    def test_validate_text_with_artifacts(self):
        """Test validation of text with OCR artifacts"""
        text_with_artifacts = "ABC|123~"
        is_valid, confidence, warnings = self.validator.validate_text(text_with_artifacts, 70.0, TextType.GENERAL)
        
        assert confidence < 70.0  # Should be reduced
        assert any("artifact" in warning.lower() for warning in warnings)
    
    def test_validate_dimension_format(self):
        """Test validation of dimension text format"""
        valid_dimension = "2.50"
        is_valid, confidence, warnings = self.validator.validate_text(valid_dimension, 60.0, TextType.DIMENSION)
        
        assert is_valid
        
        invalid_dimension = "ABC"
        is_valid, confidence, warnings = self.validator.validate_text(invalid_dimension, 60.0, TextType.DIMENSION)
        
        assert confidence < 60.0  # Should be reduced for invalid format
    
    def test_validate_short_text(self):
        """Test validation of very short text"""
        short_text = "A"
        is_valid, confidence, warnings = self.validator.validate_text(short_text, 60.0, TextType.GENERAL)
        
        assert confidence < 60.0  # Should be reduced
        assert any("short" in warning.lower() for warning in warnings)


class TestOCRTextExtractor:
    """Test cases for OCRTextExtractor class"""
    
    def setup_method(self):
        self.extractor = OCRTextExtractor()
    
    def create_test_image_with_text(self, text: str, size: tuple = (200, 400)) -> np.ndarray:
        """Create a test image with specified text"""
        image = np.ones((size[0], size[1], 3), dtype=np.uint8) * 255
        cv2.putText(image, text, (20, size[0]//2), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        return image
    
    @patch('pytesseract.image_to_data')
    def test_extract_text_from_image_success(self, mock_tesseract):
        """Test successful text extraction from image"""
        # Mock Tesseract response
        mock_tesseract.return_value = {
            'text': ['', 'TEST', 'TEXT'],
            'conf': [0, 85, 90],
            'left': [0, 10, 50],
            'top': [0, 20, 20],
            'width': [0, 40, 40],
            'height': [0, 20, 20]
        }
        
        test_image = self.create_test_image_with_text("TEST TEXT")
        
        result = self.extractor.extract_text_from_image(test_image)
        
        assert isinstance(result, OCRResult)
        assert len(result.extracted_texts) == 2  # Should extract 2 valid texts
        assert result.overall_confidence > 0
        assert result.processing_time > 0
    
    @patch('pytesseract.image_to_data')
    def test_extract_text_from_image_no_text(self, mock_tesseract):
        """Test text extraction from image with no text"""
        mock_tesseract.return_value = {
            'text': [''],
            'conf': [0],
            'left': [0],
            'top': [0],
            'width': [0],
            'height': [0]
        }
        
        blank_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        result = self.extractor.extract_text_from_image(blank_image)
        
        assert isinstance(result, OCRResult)
        assert len(result.extracted_texts) == 0
        assert result.overall_confidence == 0.0
        assert len(result.warnings) > 0
    
    @patch('pytesseract.image_to_data')
    def test_extract_text_from_image_error(self, mock_tesseract):
        """Test text extraction with Tesseract error"""
        mock_tesseract.side_effect = Exception("Tesseract error")
        
        test_image = self.create_test_image_with_text("TEST")
        
        result = self.extractor.extract_text_from_image(test_image)
        
        assert isinstance(result, OCRResult)
        assert len(result.extracted_texts) == 0
        assert result.overall_confidence == 0.0
        assert len(result.errors) > 0
        assert "Tesseract error" in result.errors[0]
    
    @patch('pytesseract.image_to_data')
    def test_extract_specific_patterns(self, mock_tesseract):
        """Test extraction of specific patterns"""
        mock_tesseract.return_value = {
            'text': ['2.50±0.01', 'ABC-123', 'STEEL A36'],
            'conf': [85, 90, 80],
            'left': [10, 50, 90],
            'top': [20, 20, 20],
            'width': [40, 40, 40],
            'height': [20, 20, 20]
        }
        
        test_image = self.create_test_image_with_text("2.50±0.01 ABC-123 STEEL A36")
        
        patterns = self.extractor.extract_specific_patterns(test_image)
        
        assert 'dimensions' in patterns
        assert 'part_numbers' in patterns
        assert 'materials' in patterns
        assert 'annotations' in patterns
        
        # Should have extracted at least one of each type
        assert len(patterns['dimensions']) >= 1
        assert len(patterns['part_numbers']) >= 1
        assert len(patterns['materials']) >= 1


@pytest.fixture
def sample_engineering_drawing():
    """Create a sample engineering drawing image for testing"""
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # Add some typical engineering drawing text
    cv2.putText(image, "2.50±0.01", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "PART NO: ABC-123", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "MATERIAL: STEEL A36", (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "R0.125", (300, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "NOTE: ALL DIMS IN INCHES", (50, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    return image


class TestOCRIntegration:
    """Integration tests for OCR functionality"""
    
    def test_full_ocr_pipeline(self, sample_engineering_drawing):
        """Test complete OCR pipeline with sample drawing"""
        extractor = OCRTextExtractor()
        
        # This test requires actual Tesseract installation
        try:
            result = extractor.extract_text_from_image(sample_engineering_drawing)
            
            assert isinstance(result, OCRResult)
            assert result.processing_time > 0
            
            # Should extract some text (exact results depend on Tesseract)
            if result.extracted_texts:
                assert result.overall_confidence > 0
                
                # Check that different text types are identified
                text_types = [et.text_type for et in result.extracted_texts]
                assert len(set(text_types)) > 1  # Should have multiple types
                
        except Exception as e:
            # Skip test if Tesseract is not properly installed
            pytest.skip(f"Tesseract not available: {str(e)}")
    
    def test_pattern_extraction_integration(self, sample_engineering_drawing):
        """Test pattern extraction integration"""
        extractor = OCRTextExtractor()
        
        try:
            patterns = extractor.extract_specific_patterns(sample_engineering_drawing)
            
            assert isinstance(patterns, dict)
            assert 'dimensions' in patterns
            assert 'part_numbers' in patterns
            assert 'materials' in patterns
            assert 'annotations' in patterns
            
        except Exception as e:
            pytest.skip(f"Tesseract not available: {str(e)}")


class TestOCRErrorHandling:
    """Test error handling in OCR functionality"""
    
    def test_invalid_image_handling(self):
        """Test handling of invalid image input"""
        extractor = OCRTextExtractor()
        
        # Test with None image
        with pytest.raises(Exception):
            extractor.extract_text_from_image(None)
        
        # Test with invalid image array
        invalid_image = np.array([])
        result = extractor.extract_text_from_image(invalid_image)
        
        assert len(result.errors) > 0
    
    def test_corrupted_image_handling(self):
        """Test handling of corrupted image data"""
        extractor = OCRTextExtractor()
        
        # Create corrupted image data
        corrupted_image = np.random.randint(0, 255, (10, 10), dtype=np.uint8)
        
        result = extractor.extract_text_from_image(corrupted_image)
        
        # Should handle gracefully without crashing
        assert isinstance(result, OCRResult)