#!/usr/bin/env python3
"""
Simplified integration tests for core functionality
Tests basic API endpoints and system integration
"""

import pytest
import time
from fastapi.testclient import TestClient

from main import app


class TestBasicIntegration:
    """Basic integration tests for core functionality"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_health_endpoints(self, client):
        """Test basic health check endpoints"""
        # Test root endpoint
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Test health check endpoint
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "version" in data
    
    def test_api_endpoints_require_auth(self, client):
        """Test that protected endpoints require authentication"""
        protected_endpoints = [
            "/api/files/",
            "/api/auth/me",
            "/api/sharing/shared-with-me",
        ]
        
        for endpoint in protected_endpoints:
            response = client.get(endpoint)
            assert response.status_code in [401, 403], f"Endpoint {endpoint} should require authentication (got {response.status_code})"
    
    def test_cors_headers(self, client):
        """Test CORS headers are present"""
        response = client.get("/")
        
        # Check for CORS headers (may not be present in test environment)
        # This is more of a smoke test
        assert response.status_code == 200
    
    def test_api_response_format(self, client):
        """Test API response format consistency"""
        # Test health endpoint response format
        response = client.get("/health")
        assert response.status_code == 200
        assert response.headers.get("content-type", "").startswith("application/json")
        
        data = response.json()
        assert isinstance(data, dict)
        assert "status" in data
    
    def test_error_handling(self, client):
        """Test error handling for invalid requests"""
        # Test non-existent endpoint
        response = client.get("/api/nonexistent")
        assert response.status_code == 404
        
        # Test invalid method
        response = client.post("/")
        assert response.status_code == 405  # Method Not Allowed
    
    def test_performance_basic(self, client):
        """Test basic performance metrics"""
        # Measure response time for health check
        start_time = time.time()
        response = client.get("/health")
        response_time = time.time() - start_time
        
        assert response.status_code == 200
        assert response_time < 1.0  # Should respond within 1 second
        
        print(f"Health check response time: {response_time:.4f}s")
    
    def test_concurrent_requests(self, client):
        """Test handling of concurrent requests"""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def make_request():
            try:
                response = client.get("/health")
                results_queue.put(response.status_code)
            except Exception as e:
                results_queue.put(str(e))
        
        # Create 5 concurrent requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        success_count = 0
        while not results_queue.empty():
            result = results_queue.get()
            if result == 200:
                success_count += 1
        
        assert success_count == 5  # All requests should succeed
    
    def test_memory_usage(self, client):
        """Test memory usage during requests"""
        import psutil
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        # Make multiple requests
        for _ in range(10):
            response = client.get("/health")
            assert response.status_code == 200
        
        final_memory = process.memory_info().rss / (1024 * 1024)  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"Memory usage - Initial: {initial_memory:.1f}MB, Final: {final_memory:.1f}MB")
        print(f"Memory increase: {memory_increase:.1f}MB")
        
        # Memory usage should not increase significantly for simple requests
        assert memory_increase < 10  # Less than 10MB increase


class TestAPIIntegration:
    """Test API integration without authentication"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_openapi_schema(self, client):
        """Test OpenAPI schema is available"""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        schema = response.json()
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema
        
        # Check that our API endpoints are documented
        paths = schema["paths"]
        assert "/health" in paths
        assert "/" in paths
    
    def test_api_documentation(self, client):
        """Test API documentation endpoints"""
        # Test Swagger UI
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
        
        # Test ReDoc
        response = client.get("/redoc")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
    
    def test_api_versioning(self, client):
        """Test API versioning information"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "version" in data
        assert data["version"] == "1.0.0"
    
    def test_request_validation(self, client):
        """Test request validation for API endpoints"""
        # Test invalid JSON
        response = client.post(
            "/api/auth/register",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_response_headers(self, client):
        """Test response headers are appropriate"""
        response = client.get("/health")
        assert response.status_code == 200
        
        headers = response.headers
        
        # Check content type
        assert "application/json" in headers.get("content-type", "")
        
        # Check for security headers (may not be present in test environment)
        # This is more of a documentation test
        print("Response headers:", dict(headers))


if __name__ == "__main__":
    pytest.main([__file__, "-v"])