"""
Unit tests for file storage service.
"""
import pytest
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from fastapi import UploadFile, HTTPException
from io import BytesIO
from minio.error import S3Error

from services.file_service import FileStorageService, file_storage_service


class TestFileStorageService:
    """Test cases for FileStorageService."""
    
    @pytest.fixture
    def service(self):
        """Create a FileStorageService instance for testing."""
        with patch.object(FileStorageService, '_ensure_bucket_exists'):
            service = FileStorageService()
            service.client = Mock()
            return service
    
    @pytest.fixture
    def mock_upload_file(self):
        """Create a mock UploadFile for testing."""
        from unittest.mock import AsyncMock
        file_content = b"test file content"
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "test_drawing.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.size = len(file_content)
        mock_file.read = AsyncMock(return_value=file_content)
        mock_file.seek = AsyncMock()
        return mock_file
    
    def test_validate_file_success(self, service, mock_upload_file):
        """Test successful file validation."""
        result = service.validate_file(mock_upload_file)
        
        assert result['valid'] is True
        assert len(result['errors']) == 0
        assert result['file_info']['filename'] == "test_drawing.pdf"
        assert result['file_info']['mime_type'] == "application/pdf"
        assert result['file_info']['extension'] == ".pdf"
    
    def test_validate_file_unsupported_format(self, service):
        """Test validation failure for unsupported file format."""
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "test.txt"
        mock_file.content_type = "text/plain"
        mock_file.size = 1000
        
        result = service.validate_file(mock_file)
        
        assert result['valid'] is False
        assert any("Unsupported file format" in error for error in result['errors'])
    
    def test_validate_file_too_large(self, service):
        """Test validation failure for file too large."""
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "large_file.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.size = 60 * 1024 * 1024  # 60MB
        
        result = service.validate_file(mock_file)
        
        assert result['valid'] is False
        assert any("exceeds maximum allowed size" in error for error in result['errors'])
    
    def test_validate_file_suspicious_filename(self, service):
        """Test validation failure for suspicious filename."""
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "../../../etc/passwd.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.size = 1000
        
        result = service.validate_file(mock_file)
        
        assert result['valid'] is False
        assert any("suspicious characters" in error for error in result['errors'])
    
    def test_validate_dxf_file_with_octet_stream(self, service):
        """Test DXF file validation with application/octet-stream MIME type."""
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "drawing.dxf"
        mock_file.content_type = "application/octet-stream"
        mock_file.size = 1000
        
        result = service.validate_file(mock_file)
        
        assert result['valid'] is True
        assert len(result['warnings']) > 0  # Should have warning about unusual MIME type
    
    @pytest.mark.asyncio
    async def test_store_file_success(self, service, mock_upload_file):
        """Test successful file storage."""
        result = await service.store_file(mock_upload_file, user_id=1, design_name="Test Design")
        
        assert 'file_path' in result
        assert 'file_size' in result
        assert result['original_filename'] == "test_drawing.pdf"
        assert result['content_type'] == "application/pdf"
        assert 'file_id' in result
        
        # In test mode, verify file was stored in mock storage
        assert len(service._mock_storage) == 1
        stored_file = list(service._mock_storage.values())[0]
        assert stored_file['content'] == b"test file content"
        assert stored_file['size'] == len(b"test file content")
        assert stored_file['content_type'] == "application/pdf"
    
    @pytest.mark.asyncio
    async def test_store_file_validation_failure(self, service):
        """Test file storage with validation failure."""
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = "test.txt"
        mock_file.content_type = "text/plain"
        mock_file.size = 1000
        
        with pytest.raises(HTTPException) as exc_info:
            await service.store_file(mock_file, user_id=1, design_name="Test")
        
        assert exc_info.value.status_code == 400
        assert "validation failed" in str(exc_info.value.detail).lower()
    
    @pytest.mark.asyncio
    async def test_store_file_minio_error(self, mock_upload_file):
        """Test file storage with MinIO error."""
        # Create a service instance that's not in test mode
        with patch.dict(os.environ, {'TESTING': 'false'}):
            with patch.object(FileStorageService, '_ensure_bucket_exists'):
                service = FileStorageService()
                service.client = Mock()
                service.client.put_object = Mock(side_effect=S3Error("Storage error", None, None, None, None, None))
                
                with pytest.raises(HTTPException) as exc_info:
                    await service.store_file(mock_upload_file, user_id=1, design_name="Test")
                
                assert exc_info.value.status_code == 500
                assert "storage failed" in str(exc_info.value.detail).lower()
    
    @pytest.mark.asyncio
    async def test_retrieve_file_success(self, service):
        """Test successful file retrieval."""
        mock_response = Mock()
        mock_response.read.return_value = b"file content"
        mock_response.close = Mock()
        mock_response.release_conn = Mock()
        
        service.client.get_object = Mock(return_value=mock_response)
        
        result = await service.retrieve_file("test/path/file.pdf")
        
        assert result == b"file content"
        service.client.get_object.assert_called_once_with(service.bucket_name, "test/path/file.pdf")
        mock_response.close.assert_called_once()
        mock_response.release_conn.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_retrieve_file_not_found(self, service):
        """Test file retrieval when file doesn't exist."""
        service.client.get_object = Mock(side_effect=S3Error("NoSuchKey", None, None, None, None, None))
        service.client.get_object.return_value.code = "NoSuchKey"
        
        with pytest.raises(HTTPException) as exc_info:
            await service.retrieve_file("nonexistent/file.pdf")
        
        assert exc_info.value.status_code == 404
        assert "not found" in str(exc_info.value.detail).lower()
    
    @pytest.mark.asyncio
    async def test_delete_file_success(self, service):
        """Test successful file deletion."""
        service.client.remove_object = Mock()
        
        result = await service.delete_file("test/path/file.pdf")
        
        assert result is True
        service.client.remove_object.assert_called_once_with(service.bucket_name, "test/path/file.pdf")
    
    @pytest.mark.asyncio
    async def test_delete_file_not_found(self, service):
        """Test file deletion when file doesn't exist."""
        service.client.remove_object = Mock(side_effect=S3Error("NoSuchKey", None, None, None, None, None))
        
        result = await service.delete_file("nonexistent/file.pdf")
        
        # Should return True even if file doesn't exist
        assert result is True
    
    def test_get_file_url_success(self, service):
        """Test successful presigned URL generation."""
        expected_url = "https://example.com/presigned-url"
        service.client.presigned_get_object = Mock(return_value=expected_url)
        
        result = service.get_file_url("test/path/file.pdf")
        
        assert result == expected_url
        service.client.presigned_get_object.assert_called_once()
    
    def test_get_file_url_error(self, service):
        """Test presigned URL generation with error."""
        service.client.presigned_get_object = Mock(side_effect=S3Error("Error", None, None, None, None, None))
        
        with pytest.raises(HTTPException) as exc_info:
            service.get_file_url("test/path/file.pdf")
        
        assert exc_info.value.status_code == 500
        assert "URL generation failed" in str(exc_info.value.detail)


class TestFileValidation:
    """Test cases for file validation logic."""
    
    def test_supported_formats(self):
        """Test that all supported formats are properly defined."""
        service = FileStorageService()
        
        # Check that PDF is supported
        assert any('.pdf' in extensions for extensions in service.SUPPORTED_FORMATS.values())
        
        # Check that DXF is supported
        assert any('.dxf' in extensions for extensions in service.SUPPORTED_FORMATS.values())
        
        # Check that image formats are supported
        assert any('.png' in extensions for extensions in service.SUPPORTED_FORMATS.values())
        assert any('.jpg' in extensions for extensions in service.SUPPORTED_FORMATS.values())
        assert any('.jpeg' in extensions for extensions in service.SUPPORTED_FORMATS.values())
        assert any('.tiff' in extensions for extensions in service.SUPPORTED_FORMATS.values())
    
    def test_max_file_size(self):
        """Test that maximum file size is reasonable."""
        service = FileStorageService()
        
        # Should be 50MB
        assert service.MAX_FILE_SIZE == 50 * 1024 * 1024
    
    @pytest.mark.parametrize("filename,expected_valid", [
        ("drawing.pdf", True),
        ("plan.dxf", True),
        ("image.png", True),
        ("photo.jpg", True),
        ("scan.tiff", True),
        ("document.doc", False),
        ("spreadsheet.xlsx", False),
        ("../../../etc/passwd", False),
        ("file<script>.pdf", False),
        ("normal_file_name.pdf", True),
    ])
    def test_filename_validation(self, filename, expected_valid):
        """Test filename validation with various inputs."""
        service = FileStorageService()
        
        mock_file = Mock(spec=UploadFile)
        mock_file.filename = filename
        mock_file.content_type = "application/pdf" if filename.endswith('.pdf') else "application/octet-stream"
        mock_file.size = 1000
        
        result = service.validate_file(mock_file)
        
        assert result['valid'] == expected_valid