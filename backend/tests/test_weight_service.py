"""
Tests for Weight Calculation Service

Tests volume calculations, material matching, weight calculations,
and integration with the BOM system.
"""

import pytest
import math
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal

from services.weight_service import (
    WeightCalculationService, VolumeCalculator, MaterialMatcher,
    VolumeCalculationMethod, GeometricDimensions, VolumeCalculationResult,
    WeightCalculationResult, AssemblyWeightSummary
)
from models.design import BOMItem, AnalysisResult
from models.material import Material


class TestVolumeCalculator:
    """Test volume calculation functionality"""
    
    def setup_method(self):
        self.calculator = VolumeCalculator()
    
    def test_rectangular_volume_calculation(self):
        """Test rectangular volume calculation"""
        dimensions = GeometricDimensions(
            length=100.0,  # mm
            width=50.0,    # mm
            height=10.0    # mm
        )
        
        result = self.calculator._calculate_rectangular_volume(dimensions)
        
        # Expected: 100 * 50 * 10 = 50,000 mm³ = 50 cm³
        assert result.volume_cm3 == 50.0
        assert result.calculation_method == VolumeCalculationMethod.RECTANGULAR
        assert result.confidence > 0.7
        assert len(result.assumptions) == 0
    
    def test_rectangular_volume_with_default_thickness(self):
        """Test rectangular volume with default thickness"""
        dimensions = GeometricDimensions(
            length=100.0,  # mm
            width=50.0     # mm
            # No height - should use default thickness
        )
        
        result = self.calculator._calculate_rectangular_volume(dimensions)
        
        # Expected: 100 * 50 * 5 = 25,000 mm³ = 25 cm³
        assert result.volume_cm3 == 25.0
        assert result.calculation_method == VolumeCalculationMethod.RECTANGULAR
        assert result.confidence < 0.7  # Lower confidence due to assumption
        assert any("default thickness" in assumption for assumption in result.assumptions)
    
    def test_cylindrical_volume_calculation(self):
        """Test cylindrical volume calculation"""
        dimensions = GeometricDimensions(
            radius=10.0,   # mm
            height=50.0    # mm
        )
        
        result = self.calculator._calculate_cylindrical_volume(dimensions)
        
        # Expected: π * 10² * 50 = π * 100 * 50 = 15,707.96 mm³ ≈ 15.71 cm³
        expected_volume = math.pi * 100 * 50 / 1000
        assert abs(result.volume_cm3 - expected_volume) < 0.01
        assert result.calculation_method == VolumeCalculationMethod.CYLINDRICAL
        assert result.confidence > 0.7
    
    def test_cylindrical_volume_from_diameter(self):
        """Test cylindrical volume calculation from diameter"""
        dimensions = GeometricDimensions(
            diameter=20.0,  # mm
            height=50.0     # mm
        )
        
        result = self.calculator._calculate_cylindrical_volume(dimensions)
        
        # Expected: π * 10² * 50 = 15,707.96 mm³ ≈ 15.71 cm³
        expected_volume = math.pi * 100 * 50 / 1000
        assert abs(result.volume_cm3 - expected_volume) < 0.01
        assert result.calculation_method == VolumeCalculationMethod.CYLINDRICAL
    
    def test_dimension_parsing(self):
        """Test dimension parsing from text"""
        test_cases = [
            ("25.4 mm", 25.4),
            ("2.54 cm", 25.4),
            ("1.0 in", 25.4),
            ("1.0\"", 25.4),
            ("100", 100.0),
        ]
        
        for text, expected in test_cases:
            result = self.calculator._parse_dimension_text(text)
            assert abs(result - expected) < 0.1, f"Failed for {text}"
    
    def test_dimension_assignment(self):
        """Test dimension assignment to structure"""
        dimensions = GeometricDimensions()
        values = [100.0, 50.0, 25.0]
        
        self.calculator._assign_dimensions_to_structure(dimensions, values)
        
        assert dimensions.length == 100.0
        assert dimensions.width == 50.0
        assert dimensions.height == 25.0
    
    def test_circular_part_detection(self):
        """Test detection of circular parts from dimensions"""
        dimensions = GeometricDimensions()
        values = [50.0, 48.0]  # Nearly equal - suggests circular
        
        self.calculator._assign_dimensions_to_structure(dimensions, values)
        
        assert dimensions.diameter == 50.0
        assert dimensions.radius == 25.0
    
    def test_fallback_volume_result(self):
        """Test fallback volume calculation"""
        result = self.calculator._create_fallback_volume_result("TEST-001")
        
        assert result.volume_cm3 == 10.0
        assert result.calculation_method == VolumeCalculationMethod.MANUAL_INPUT
        assert result.confidence == 0.1
        assert "TEST-001" in result.notes[0]


class TestMaterialMatcher:
    """Test material matching functionality"""
    
    def setup_method(self):
        self.matcher = MaterialMatcher()
    
    @patch('services.weight_service.get_db')
    def test_exact_material_match(self, mock_get_db):
        """Test exact material name matching"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock material
        mock_material = Material(
            id=1,
            name="Carbon Steel A36",
            density=7850.0,
            category="Steel"
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_material
        
        result = self.matcher.get_material_for_part("Carbon Steel A36", mock_db)
        
        assert result == mock_material
        assert result.name == "Carbon Steel A36"
        assert result.density == 7850.0
    
    @patch('services.weight_service.get_db')
    def test_fuzzy_material_match(self, mock_get_db):
        """Test fuzzy material matching"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock no exact match
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Mock materials for fuzzy matching
        steel_material = Material(id=1, name="Carbon Steel A36", density=7850.0, category="Steel")
        aluminum_material = Material(id=2, name="Aluminum 6061", density=2700.0, category="Aluminum")
        
        mock_db.query.return_value.all.return_value = [steel_material, aluminum_material]
        
        result = self.matcher._fuzzy_match_material("steel", mock_db)
        
        assert result == steel_material
    
    @patch('services.weight_service.get_db')
    def test_default_material_fallback(self, mock_get_db):
        """Test fallback to default material"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock no matches
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_db.query.return_value.all.return_value = []
        
        # Mock default material query
        default_material = Material(
            id=1,
            name="Carbon Steel A36",
            density=7850.0,
            category="Steel"
        )
        
        # Set up the mock to return default material on second call
        mock_db.query.return_value.filter.return_value.first.side_effect = [None, default_material]
        
        result = self.matcher.get_material_for_part("unknown_material", mock_db)
        
        assert result == default_material


class TestWeightCalculationService:
    """Test main weight calculation service"""
    
    def setup_method(self):
        self.service = WeightCalculationService()
    
    @patch('services.weight_service.get_db')
    def test_calculate_part_weight(self, mock_get_db):
        """Test individual part weight calculation"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock BOM item
        bom_item = BOMItem(
            part_number="TEST-001",
            description="Test Part",
            quantity=2,
            material="Steel"
        )
        
        # Mock material
        mock_material = Material(
            name="Carbon Steel A36",
            density=7850.0,
            category="Steel"
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_material
        
        # Mock analysis data
        analysis_data = {
            'cv_results': {'features': []},
            'ocr_results': {'extracted_texts': []}
        }
        
        # Mock volume calculation
        with patch.object(self.service.volume_calculator, 'calculate_volume_from_analysis') as mock_volume:
            mock_volume.return_value = VolumeCalculationResult(
                volume_cm3=50.0,
                calculation_method=VolumeCalculationMethod.RECTANGULAR,
                confidence=0.8,
                dimensions_used=GeometricDimensions(),
                notes=[],
                assumptions=[]
            )
            
            result = self.service._calculate_part_weight(bom_item, analysis_data, mock_db)
            
            # Verify calculations
            # Volume: 50 cm³ = 0.00005 m³
            # Weight: 0.00005 m³ * 7850 kg/m³ = 0.3925 kg per unit
            # Total: 0.3925 kg * 2 = 0.785 kg
            
            assert result.part_number == "TEST-001"
            assert result.material_name == "Carbon Steel A36"
            assert result.volume_cm3 == 50.0
            assert abs(result.unit_weight_kg - 0.3925) < 0.001
            assert abs(result.total_weight_kg - 0.785) < 0.001
            assert result.quantity == 2
    
    @patch('services.weight_service.get_db')
    def test_calculate_weights_for_design(self, mock_get_db):
        """Test complete design weight calculation"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock BOM items
        bom_items = [
            BOMItem(id=1, part_number="PART-001", quantity=1, material="Steel"),
            BOMItem(id=2, part_number="PART-002", quantity=2, material="Aluminum")
        ]
        
        # Mock analysis result
        analysis_result = AnalysisResult(
            analysis_data={
                'cv_results': {'features': []},
                'ocr_results': {'extracted_texts': []}
            }
        )
        
        # Mock database queries
        mock_db.query.return_value.filter.return_value.all.return_value = bom_items
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = analysis_result
        
        # Mock materials
        steel_material = Material(name="Carbon Steel A36", density=7850.0)
        aluminum_material = Material(name="Aluminum 6061", density=2700.0)
        
        def material_side_effect(*args, **kwargs):
            if "Steel" in str(args):
                return steel_material
            return aluminum_material
        
        # Mock weight calculations
        with patch.object(self.service, '_calculate_part_weight') as mock_calc:
            mock_calc.side_effect = [
                WeightCalculationResult(
                    part_number="PART-001",
                    material_name="Carbon Steel A36",
                    material_density=7850.0,
                    volume_cm3=100.0,
                    unit_weight_kg=0.785,
                    quantity=1,
                    total_weight_kg=0.785,
                    confidence=80.0,
                    calculation_notes=[]
                ),
                WeightCalculationResult(
                    part_number="PART-002",
                    material_name="Aluminum 6061",
                    material_density=2700.0,
                    volume_cm3=50.0,
                    unit_weight_kg=0.135,
                    quantity=2,
                    total_weight_kg=0.270,
                    confidence=75.0,
                    calculation_notes=[]
                )
            ]
            
            result = self.service.calculate_weights_for_design(1)
            
            # Verify summary
            assert result.part_count == 2
            assert abs(result.total_weight_kg - 1.055) < 0.001  # 0.785 + 0.270
            assert len(result.unique_materials) == 2
            assert result.confidence_score == 77.5  # Average of 80 and 75
            assert result.heaviest_part.part_number == "PART-001"
            assert result.lightest_part.part_number == "PART-002"
    
    @patch('services.weight_service.get_db')
    def test_get_material_options(self, mock_get_db):
        """Test getting material options"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock materials
        materials = [
            Material(id=1, name="Carbon Steel A36", density=7850.0, category="Steel", description="Structural steel"),
            Material(id=2, name="Aluminum 6061", density=2700.0, category="Aluminum", description="Aluminum alloy"),
        ]
        
        mock_db.query.return_value.order_by.return_value.all.return_value = materials
        
        result = self.service.get_material_options()
        
        assert len(result) == 2
        assert result[0]['name'] == "Carbon Steel A36"
        assert result[0]['density'] == 7850.0
        assert result[1]['name'] == "Aluminum 6061"
        assert result[1]['density'] == 2700.0
    
    @patch('services.weight_service.get_db')
    def test_update_part_material(self, mock_get_db):
        """Test updating part material"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock BOM item
        bom_item = BOMItem(
            id=1,
            design_id=1,
            part_number="TEST-001",
            quantity=1,
            material="Old Material"
        )
        
        # Mock analysis result
        analysis_result = AnalysisResult(
            analysis_data={
                'cv_results': {'features': []},
                'ocr_results': {'extracted_texts': []}
            }
        )
        
        mock_db.query.return_value.filter.return_value.first.side_effect = [bom_item, analysis_result]
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = analysis_result
        
        # Mock weight calculation
        with patch.object(self.service, '_calculate_part_weight') as mock_calc:
            mock_calc.return_value = WeightCalculationResult(
                part_number="TEST-001",
                material_name="New Material",
                material_density=5000.0,
                volume_cm3=50.0,
                unit_weight_kg=0.25,
                quantity=1,
                total_weight_kg=0.25,
                confidence=85.0,
                calculation_notes=[]
            )
            
            result = self.service.update_part_material(1, "TEST-001", "New Material")
            
            assert result.material_name == "New Material"
            assert bom_item.material == "New Material"
            assert bom_item.weight == 0.25
    
    @patch('services.weight_service.get_db')
    def test_add_custom_material(self, mock_get_db):
        """Test adding custom material"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock no existing material
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Mock material creation
        new_material = Material(
            id=1,
            name="Custom Steel",
            density=8000.0,
            category="Steel",
            description="Custom steel alloy"
        )
        
        mock_db.refresh.side_effect = lambda x: setattr(x, 'id', 1)
        
        result = self.service.add_custom_material(
            "Custom Steel",
            8000.0,
            "Steel",
            "Custom steel alloy"
        )
        
        assert result['name'] == "Custom Steel"
        assert result['density'] == 8000.0
        assert result['category'] == "Steel"
        assert result['description'] == "Custom steel alloy"
    
    @patch('services.weight_service.get_db')
    def test_add_duplicate_material_error(self, mock_get_db):
        """Test error when adding duplicate material"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock existing material
        existing_material = Material(name="Existing Steel", density=7850.0)
        mock_db.query.return_value.filter.return_value.first.return_value = existing_material
        
        with pytest.raises(ValueError, match="already exists"):
            self.service.add_custom_material("Existing Steel", 8000.0, "Steel")


class TestIntegration:
    """Integration tests for weight calculation system"""
    
    @patch('services.weight_service.get_db')
    def test_complete_weight_calculation_workflow(self, mock_get_db):
        """Test complete workflow from analysis to weight calculation"""
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock complete analysis data
        analysis_data = {
            'cv_results': {
                'features': [
                    {
                        'feature_type': 'rectangle',
                        'width': 100.0,
                        'height': 50.0
                    }
                ],
                'sections': []
            },
            'ocr_results': {
                'extracted_texts': [
                    {
                        'text': '10 mm',
                        'text_type': 'dimension',
                        'confidence': 85
                    },
                    {
                        'text': 'STEEL',
                        'text_type': 'material',
                        'confidence': 90
                    }
                ]
            }
        }
        
        # Mock BOM item
        bom_item = BOMItem(
            part_number="RECT-001",
            description="Rectangular plate",
            quantity=1,
            material="Steel"
        )
        
        # Mock analysis result
        analysis_result = AnalysisResult(analysis_data=analysis_data)
        
        # Mock material
        steel_material = Material(
            name="Carbon Steel A36",
            density=7850.0,
            category="Steel"
        )
        
        # Set up database mocks
        mock_db.query.return_value.filter.return_value.all.return_value = [bom_item]
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = analysis_result
        mock_db.query.return_value.filter.return_value.first.return_value = steel_material
        
        service = WeightCalculationService()
        result = service.calculate_weights_for_design(1)
        
        # Verify the complete calculation
        assert result.part_count == 1
        assert result.total_weight_kg > 0
        assert len(result.weight_breakdown) == 1
        assert result.weight_breakdown[0].material_name == "Carbon Steel A36"
        assert result.confidence_score > 0
    
    def test_volume_calculation_edge_cases(self):
        """Test edge cases in volume calculation"""
        calculator = VolumeCalculator()
        
        # Test zero dimensions
        dimensions = GeometricDimensions(length=0, width=0, height=0)
        result = calculator._calculate_rectangular_volume(dimensions)
        
        # Should use minimum dimensions
        assert result.volume_cm3 > 0
        
        # Test very small dimensions
        dimensions = GeometricDimensions(length=0.01, width=0.01, height=0.01)
        result = calculator._calculate_rectangular_volume(dimensions)
        
        # Should use minimum dimensions
        assert result.volume_cm3 > 0
        assert result.confidence < 0.8  # Lower confidence for small parts
    
    def test_material_density_conversion(self):
        """Test material density unit conversions"""
        service = WeightCalculationService()
        
        # Test calculation with different density values
        volume_cm3 = 100.0  # 100 cm³
        density_kg_m3 = 7850.0  # Steel density
        
        # Manual calculation
        volume_m3 = volume_cm3 / 1_000_000  # Convert cm³ to m³
        expected_weight = volume_m3 * density_kg_m3
        
        # This should equal 0.785 kg
        assert abs(expected_weight - 0.785) < 0.001