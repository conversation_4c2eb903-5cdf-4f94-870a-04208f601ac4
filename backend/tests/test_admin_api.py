"""
Tests for admin API endpoints.
"""
import pytest
from datetime import datetime, timedelta

from models.material import Material
from models.admin import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserActivity<PERSON><PERSON>


def test_get_system_stats_admin_only(client, admin_token, user_token):
    """Test that system stats endpoint requires admin access."""
    # Test with admin user
    response = client.get(
        "/api/admin/dashboard/stats",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    stats = response.json()
    assert "total_users" in stats
    assert "active_users" in stats
    assert "admin_users" in stats
    assert stats["total_users"] >= 2  # At least admin and regular user
    
    # Test with regular user (should fail)
    response = client.get(
        "/api/admin/dashboard/stats",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403


def test_get_system_stats_content(client, admin_token):
    """Test system stats content."""
    response = client.get(
        "/api/admin/dashboard/stats",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    stats = response.json()
    
    # Check all required fields are present
    required_fields = [
        "total_users", "active_users", "admin_users",
        "total_designs", "designs_this_month",
        "total_analyses", "successful_analyses", "failed_analyses",
        "total_storage_mb", "avg_processing_time_seconds"
    ]
    
    for field in required_fields:
        assert field in stats
        assert isinstance(stats[field], (int, float))


def test_get_user_activity_logs(client, admin_token, test_user, db_session):
    """Test user activity logs endpoint."""
    # Create some test activity logs
    activity_log = UserActivityLog(
        user_id=test_user.id,
        action="login",
        resource_type="session",
        resource_id=1,
        ip_address="127.0.0.1"
    )
    db_session.add(activity_log)
    db_session.commit()
    
    response = client.get(
        "/api/admin/users/activity",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    logs = response.json()
    assert isinstance(logs, list)
    
    # Test with filters
    response = client.get(
        "/api/admin/users/activity?action=login&days=1",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200


def test_get_system_health(client, admin_token):
    """Test system health metrics endpoint."""
    response = client.get(
        "/api/admin/system/health",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    metrics = response.json()
    assert isinstance(metrics, list)
    
    # Check that we have some basic metrics
    metric_names = [metric["metric_name"] for metric in metrics]
    expected_metrics = ["Database Connection", "Active Sessions"]
    
    for expected in expected_metrics:
        assert expected in metric_names
    
    # Check metric structure
    if metrics:
        metric = metrics[0]
        assert "metric_name" in metric
        assert "value" in metric
        assert "unit" in metric
        assert "status" in metric
        assert "last_updated" in metric


def test_get_error_logs(client, admin_token, test_user, db_session):
    """Test error logs endpoint."""
    # Create test error log
    error_log = ErrorLog(
        error_type="TestError",
        error_message="Test error message",
        user_id=test_user.id,
        endpoint="/api/test",
        resolved=False
    )
    db_session.add(error_log)
    db_session.commit()
    
    response = client.get(
        "/api/admin/system/errors",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    errors = response.json()
    assert isinstance(errors, list)
    
    # Test with filters
    response = client.get(
        "/api/admin/system/errors?error_type=TestError&resolved=false",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200


def test_resolve_error_log(client, admin_token, db_session):
    """Test resolving error log entries."""
    # Create test error log
    error_log = ErrorLog(
        error_type="TestError",
        error_message="Test error message",
        resolved=False
    )
    db_session.add(error_log)
    db_session.commit()
    db_session.refresh(error_log)
    
    response = client.put(
        f"/api/admin/system/errors/{error_log.id}/resolve",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # Verify error is marked as resolved
    db_session.refresh(error_log)
    assert error_log.resolved is True
    assert error_log.resolved_at is not None


def test_materials_crud(client, admin_token):
    """Test materials CRUD operations."""
    # Create material
    material_data = {
        "name": "Test Steel",
        "density": 7850.0,
        "category": "Steel",
        "description": "Test steel material"
    }
    
    response = client.post(
        "/api/admin/materials",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=material_data
    )
    assert response.status_code == 201
    
    material = response.json()
    assert material["name"] == material_data["name"]
    assert material["density"] == material_data["density"]
    material_id = material["id"]
    
    # Get materials
    response = client.get(
        "/api/admin/materials",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    materials = response.json()
    assert len(materials) >= 1
    
    # Update material
    update_data = {
        "name": "Updated Test Steel",
        "density": 7900.0
    }
    
    response = client.put(
        f"/api/admin/materials/{material_id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=update_data
    )
    assert response.status_code == 200
    
    updated_material = response.json()
    assert updated_material["name"] == update_data["name"]
    assert updated_material["density"] == update_data["density"]
    
    # Delete material
    response = client.delete(
        f"/api/admin/materials/{material_id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # Verify deletion
    response = client.get(
        "/api/admin/materials",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    materials = response.json()
    material_ids = [m["id"] for m in materials]
    assert material_id not in material_ids


def test_materials_search_and_filter(client, admin_token):
    """Test materials search and filtering."""
    # Create test materials
    materials_data = [
        {"name": "Steel A36", "density": 7850.0, "category": "Steel", "description": "Structural steel"},
        {"name": "Aluminum 6061", "density": 2700.0, "category": "Aluminum", "description": "Structural aluminum"},
        {"name": "Stainless Steel 304", "density": 8000.0, "category": "Steel", "description": "Corrosion resistant"}
    ]
    
    for material_data in materials_data:
        response = client.post(
            "/api/admin/materials",
            headers={"Authorization": f"Bearer {admin_token}"},
            json=material_data
        )
        assert response.status_code == 201
    
    # Test category filter
    response = client.get(
        "/api/admin/materials?category=Steel",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    materials = response.json()
    assert len(materials) == 2
    assert all(m["category"] == "Steel" for m in materials)
    
    # Test search
    response = client.get(
        "/api/admin/materials?search=aluminum",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    materials = response.json()
    assert len(materials) == 1
    assert "Aluminum" in materials[0]["name"]


def test_system_cleanup(client, admin_token):
    """Test system cleanup endpoint."""
    response = client.post(
        "/api/admin/system/cleanup",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    result = response.json()
    assert "results" in result
    assert isinstance(result["results"], dict)


def test_admin_endpoints_require_authentication(client):
    """Test that admin endpoints require authentication."""
    endpoints = [
        "/api/admin/dashboard/stats",
        "/api/admin/users/activity",
        "/api/admin/system/health",
        "/api/admin/system/errors",
        "/api/admin/materials"
    ]
    
    for endpoint in endpoints:
        response = client.get(endpoint)
        assert response.status_code == 401


def test_admin_endpoints_require_admin_role(client, user_token):
    """Test that admin endpoints require admin role."""
    endpoints = [
        "/api/admin/dashboard/stats",
        "/api/admin/users/activity",
        "/api/admin/system/health",
        "/api/admin/system/errors",
        "/api/admin/materials"
    ]
    
    for endpoint in endpoints:
        response = client.get(endpoint, headers={"Authorization": f"Bearer {user_token}"})
        assert response.status_code == 403


def test_material_validation(client, admin_token):
    """Test material creation validation."""
    # Test invalid density
    invalid_material = {
        "name": "Invalid Material",
        "density": -100.0,  # Negative density
        "category": "Test"
    }
    
    response = client.post(
        "/api/admin/materials",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=invalid_material
    )
    assert response.status_code == 400
    
    # Test empty name
    invalid_material = {
        "name": "",
        "density": 1000.0,
        "category": "Test"
    }
    
    response = client.post(
        "/api/admin/materials",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=invalid_material
    )
    assert response.status_code == 400
    
    # Test duplicate name
    valid_material = {
        "name": "Unique Material",
        "density": 1000.0,
        "category": "Test"
    }
    
    # Create first material
    response = client.post(
        "/api/admin/materials",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=valid_material
    )
    assert response.status_code == 201
    
    # Try to create duplicate
    response = client.post(
        "/api/admin/materials",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=valid_material
    )
    assert response.status_code == 400