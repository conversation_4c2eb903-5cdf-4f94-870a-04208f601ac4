"""
Unit tests for file management API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session
from datetime import datetime
import io

from main import app
from models.user import User
from models.design import Design
from services.file_service import file_storage_service


class TestFileAPI:
    """Test cases for file management API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user(self):
        """Create mock user for testing."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        user.is_active = True
        return user
    
    @pytest.fixture
    def mock_design(self, mock_user):
        """Create mock design for testing."""
        design = Mock(spec=Design)
        design.id = 1
        design.user_id = mock_user.id
        design.name = "Test Design"
        design.original_filename = "test_drawing.pdf"
        design.file_path = "designs/1/test-file-id.pdf"
        design.status = "uploaded"
        design.created_at = datetime(2025, 1, 18, 10, 0, 0)
        design.updated_at = datetime(2025, 1, 18, 10, 0, 0)
        return design
    
    def test_upload_file_success(self, client, mock_user):
        """Test successful file upload."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        from services.file_service import file_storage_service
        from datetime import datetime
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_db_session.add = Mock()
        mock_db_session.commit = Mock()
        
        # Mock the refresh method to set created_at and id
        def mock_refresh(design):
            design.id = 1
            design.created_at = datetime(2025, 1, 18, 10, 0, 0)
            design.updated_at = datetime(2025, 1, 18, 10, 0, 0)
        
        mock_db_session.refresh = Mock(side_effect=mock_refresh)
        
        # Mock storage service
        original_store_file = file_storage_service.store_file
        file_storage_service.store_file = AsyncMock(return_value={
            'file_path': 'designs/1/test-file-id.pdf',
            'file_size': 1024,
            'original_filename': 'test_drawing.pdf',
            'content_type': 'application/pdf',
            'file_id': 'test-file-id',
            'validation_warnings': []
        })
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Create test file
            test_file_content = b"test pdf content"
            files = {"file": ("test_drawing.pdf", io.BytesIO(test_file_content), "application/pdf")}
            
            # Make request
            response = client.post("/api/files/upload", files=files)
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "File uploaded successfully"
            assert "design_id" in data
            assert data["original_filename"] == "test_drawing.pdf"
            assert data["file_size"] == 1024
            assert data["status"] == "uploaded"
            
            # Verify storage service was called
            file_storage_service.store_file.assert_called_once()
        finally:
            # Clean up overrides
            app.dependency_overrides.clear()
            file_storage_service.store_file = original_store_file
        
        # Verify database operations
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
    
    def test_get_file_info_success(self, client, mock_user, mock_design):
        """Test successful file info retrieval."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_design
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.get("/api/files/1")
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["design_id"] == 1
            assert data["name"] == "Test Design"
            assert data["original_filename"] == "test_drawing.pdf"
            assert data["status"] == "uploaded"
        finally:
            app.dependency_overrides.clear()
    
    def test_get_file_info_not_found(self, client, mock_user):
        """Test file info retrieval when design not found."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.get("/api/files/999")
            
            # Assertions
            assert response.status_code == 404
            assert "Design not found" in response.json()["detail"]
        finally:
            app.dependency_overrides.clear()
    
    def test_download_file_success(self, client, mock_user, mock_design):
        """Test successful file download."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        from services.file_service import file_storage_service
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_design
        
        # Mock storage service
        original_retrieve_file = file_storage_service.retrieve_file
        file_storage_service.retrieve_file = AsyncMock(return_value=b"test file content")
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.get("/api/files/1/download")
            
            # Assertions
            assert response.status_code == 200
            assert response.headers["content-type"] == "application/pdf"
            assert "attachment" in response.headers["content-disposition"]
            assert "test_drawing.pdf" in response.headers["content-disposition"]
            
            # Verify storage service was called
            file_storage_service.retrieve_file.assert_called_once_with("designs/1/test-file-id.pdf")
        finally:
            app.dependency_overrides.clear()
            file_storage_service.retrieve_file = original_retrieve_file
    
    def test_get_preview_url_success(self, client, mock_user, mock_design):
        """Test successful preview URL generation."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        from services.file_service import file_storage_service
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_design
        
        # Mock storage service
        original_get_file_url = file_storage_service.get_file_url
        file_storage_service.get_file_url = Mock(return_value="https://example.com/presigned-url")
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.get("/api/files/1/preview-url")
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["preview_url"] == "https://example.com/presigned-url"
            assert data["expires_in"] == "1 hour"
            assert data["design_id"] == 1
            assert data["filename"] == "test_drawing.pdf"
            
            # Verify storage service was called
            file_storage_service.get_file_url.assert_called_once_with("designs/1/test-file-id.pdf")
        finally:
            app.dependency_overrides.clear()
            file_storage_service.get_file_url = original_get_file_url
    
    def test_delete_file_success(self, client, mock_user, mock_design):
        """Test successful file deletion."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        from services.file_service import file_storage_service
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_design
        mock_db_session.delete = Mock()
        mock_db_session.commit = Mock()
        
        # Mock storage service
        original_delete_file = file_storage_service.delete_file
        file_storage_service.delete_file = AsyncMock(return_value=True)
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.delete("/api/files/1")
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "Design and file deleted successfully"
            assert data["design_id"] == 1
            
            # Verify storage service was called
            file_storage_service.delete_file.assert_called_once_with("designs/1/test-file-id.pdf")
            
            # Verify database operations
            mock_db_session.delete.assert_called_once_with(mock_design)
            mock_db_session.commit.assert_called_once()
        finally:
            app.dependency_overrides.clear()
            file_storage_service.delete_file = original_delete_file
    
    def test_update_file_status_success(self, client, mock_user, mock_design):
        """Test successful status update."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_design
        mock_db_session.commit = Mock()
        mock_db_session.refresh = Mock()
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.put("/api/files/1/status?status=processing")
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "Status updated successfully"
            assert data["design_id"] == 1
            assert data["old_status"] == "uploaded"
            assert data["new_status"] == "processing"
            
            # Verify database operations
            mock_db_session.commit.assert_called_once()
            mock_db_session.refresh.assert_called_once()
        finally:
            app.dependency_overrides.clear()
    
    def test_update_file_status_invalid(self, client, mock_user):
        """Test status update with invalid status."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.put("/api/files/1/status?status=invalid_status")
            
            # Assertions
            assert response.status_code == 400
            assert "Invalid status" in response.json()["detail"]
        finally:
            app.dependency_overrides.clear()
    
    def test_get_file_status_success(self, client, mock_user, mock_design):
        """Test successful status retrieval."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_design
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.get("/api/files/1/status")
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["design_id"] == 1
            assert data["status"] == "uploaded"
            assert data["name"] == "Test Design"
        finally:
            app.dependency_overrides.clear()
    
    def test_list_user_files_success(self, client, mock_user, mock_design):
        """Test successful file listing."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        
        # Mock database session
        mock_db_session = Mock(spec=Session)
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_design]
        mock_query.count.return_value = 1
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.get("/api/files/")
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert len(data["designs"]) == 1
            assert data["designs"][0]["design_id"] == 1
            assert data["designs"][0]["name"] == "Test Design"
            assert data["total_count"] == 1
            assert data["skip"] == 0
            assert data["limit"] == 100
        finally:
            app.dependency_overrides.clear()


class TestFileAPIErrorHandling:
    """Test error handling in file API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_upload_file_storage_error(self, client):
        """Test file upload with storage service error."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        from services.file_service import file_storage_service
        from fastapi import HTTPException
        
        # Setup mocks
        mock_user = Mock(spec=User)
        mock_user.id = 1
        
        mock_db_session = Mock(spec=Session)
        
        # Mock storage service to raise error
        original_store_file = file_storage_service.store_file
        file_storage_service.store_file = AsyncMock(side_effect=HTTPException(status_code=400, detail="Validation failed"))
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Create test file
            test_file_content = b"test content"
            files = {"file": ("test.txt", io.BytesIO(test_file_content), "text/plain")}
            
            # Make request
            response = client.post("/api/files/upload", files=files)
            
            # Assertions
            assert response.status_code == 400
            assert "Validation failed" in str(response.json()["detail"])
        finally:
            app.dependency_overrides.clear()
            file_storage_service.store_file = original_store_file
    
    def test_download_file_storage_error(self, client):
        """Test file download with storage service error."""
        from auth.dependencies import get_current_active_user
        from database.connection import get_database
        from services.file_service import file_storage_service
        from fastapi import HTTPException
        
        # Setup mocks
        mock_user = Mock(spec=User)
        mock_user.id = 1
        
        mock_db_session = Mock(spec=Session)
        
        mock_design = Mock(spec=Design)
        mock_design.id = 1
        mock_design.user_id = 1
        mock_design.file_path = "test/path"
        mock_design.original_filename = "test.pdf"
        
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_design
        
        # Mock storage service to raise error
        original_retrieve_file = file_storage_service.retrieve_file
        file_storage_service.retrieve_file = AsyncMock(side_effect=HTTPException(status_code=404, detail="File not found"))
        
        # Override dependencies
        app.dependency_overrides[get_current_active_user] = lambda: mock_user
        app.dependency_overrides[get_database] = lambda: mock_db_session
        
        try:
            # Make request
            response = client.get("/api/files/1/download")
            
            # Assertions
            assert response.status_code == 404
            assert "File not found" in str(response.json()["detail"])
        finally:
            app.dependency_overrides.clear()
            file_storage_service.retrieve_file = original_retrieve_file