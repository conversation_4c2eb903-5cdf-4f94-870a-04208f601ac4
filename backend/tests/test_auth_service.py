"""
Tests for authentication service.
"""
import pytest
from datetime import datetime, timedelta

from services.auth_service import AuthService
from auth.security import ValidationError, verify_password
from models.user import User, UserSession


class TestUserCreation:
    """Test user creation functionality."""
    
    def test_create_user_success(self, auth_service, test_user_data):
        """Test successful user creation."""
        user = auth_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        assert user.username == test_user_data["username"]
        assert user.email == test_user_data["email"]
        assert user.password_hash != test_user_data["password"]
        assert verify_password(test_user_data["password"], user.password_hash)
        assert not user.is_admin
        assert user.is_active
        assert user.created_at is not None
    
    def test_create_admin_user(self, auth_service, admin_user_data):
        """Test admin user creation."""
        user = auth_service.create_user(
            username=admin_user_data["username"],
            email=admin_user_data["email"],
            password=admin_user_data["password"],
            is_admin=True
        )
        
        assert user.is_admin
        assert user.is_active
    
    def test_create_user_duplicate_username(self, auth_service, test_user_data):
        """Test user creation with duplicate username."""
        # Create first user
        auth_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        # Try to create user with same username
        with pytest.raises(ValidationError, match="Username already registered"):
            auth_service.create_user(
                username=test_user_data["username"],
                email="<EMAIL>",
                password=test_user_data["password"]
            )
    
    def test_create_user_duplicate_email(self, auth_service, test_user_data):
        """Test user creation with duplicate email."""
        # Create first user
        auth_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        # Try to create user with same email
        with pytest.raises(ValidationError, match="Email already registered"):
            auth_service.create_user(
                username="differentuser",
                email=test_user_data["email"],
                password=test_user_data["password"]
            )
    
    def test_create_user_invalid_username(self, auth_service):
        """Test user creation with invalid username."""
        with pytest.raises(ValidationError, match="Username must be"):
            auth_service.create_user(
                username="ab",  # Too short
                email="<EMAIL>",
                password="TestPass123!"
            )
    
    def test_create_user_invalid_password(self, auth_service):
        """Test user creation with invalid password."""
        with pytest.raises(ValidationError, match="Password must be"):
            auth_service.create_user(
                username="testuser",
                email="<EMAIL>",
                password="weak"  # Too weak
            )


class TestUserAuthentication:
    """Test user authentication functionality."""
    
    def test_authenticate_user_success(self, auth_service, test_user, test_user_data):
        """Test successful user authentication."""
        user = auth_service.authenticate_user(
            test_user_data["username"],
            test_user_data["password"]
        )
        
        assert user is not None
        assert user.id == test_user.id
        assert user.username == test_user.username
        assert user.last_login is not None
    
    def test_authenticate_user_wrong_password(self, auth_service, test_user, test_user_data):
        """Test authentication with wrong password."""
        user = auth_service.authenticate_user(
            test_user_data["username"],
            "wrong_password"
        )
        
        assert user is None
    
    def test_authenticate_user_nonexistent(self, auth_service):
        """Test authentication with nonexistent user."""
        user = auth_service.authenticate_user("nonexistent", "password")
        assert user is None
    
    def test_authenticate_inactive_user(self, auth_service, test_user, test_user_data, db_session):
        """Test authentication with inactive user."""
        # Deactivate user
        test_user.is_active = False
        db_session.commit()
        
        user = auth_service.authenticate_user(
            test_user_data["username"],
            test_user_data["password"]
        )
        
        assert user is None


class TestUserSessions:
    """Test user session management."""
    
    def test_create_user_session(self, auth_service, test_user):
        """Test user session creation."""
        tokens = auth_service.create_user_session(test_user)
        
        assert "access_token" in tokens
        assert "refresh_token" in tokens
        assert "token_type" in tokens
        assert "session_token" in tokens
        assert tokens["token_type"] == "bearer"
        
        # Verify tokens are valid
        from auth.security import verify_token
        access_data = verify_token(tokens["access_token"], "access")
        refresh_data = verify_token(tokens["refresh_token"], "refresh")
        
        assert access_data.user_id == test_user.id
        assert refresh_data.user_id == test_user.id
    
    def test_refresh_access_token(self, auth_service, test_user):
        """Test access token refresh."""
        # Create initial session
        tokens = auth_service.create_user_session(test_user)
        refresh_token = tokens["refresh_token"]
        
        # Refresh access token
        new_tokens = auth_service.refresh_access_token(refresh_token)
        
        assert "access_token" in new_tokens
        assert "token_type" in new_tokens
        assert new_tokens["token_type"] == "bearer"
        
        # Verify new token is valid
        from auth.security import verify_token
        token_data = verify_token(new_tokens["access_token"], "access")
        assert token_data.user_id == test_user.id
    
    def test_refresh_token_invalid(self, auth_service):
        """Test refresh with invalid token."""
        with pytest.raises(Exception):  # Should raise AuthenticationError
            auth_service.refresh_access_token("invalid_token")
    
    def test_logout_user(self, auth_service, test_user, db_session):
        """Test user logout."""
        # Create session
        tokens = auth_service.create_user_session(test_user)
        session_token = tokens["session_token"]
        
        # Verify session exists
        session = db_session.query(UserSession).filter(
            UserSession.session_token == session_token
        ).first()
        assert session is not None
        
        # Logout
        result = auth_service.logout_user(session_token)
        assert result is True
        
        # Verify session is deleted
        session = db_session.query(UserSession).filter(
            UserSession.session_token == session_token
        ).first()
        assert session is None
    
    def test_get_user_by_token(self, auth_service, test_user):
        """Test getting user by access token."""
        tokens = auth_service.create_user_session(test_user)
        access_token = tokens["access_token"]
        
        user = auth_service.get_user_by_token(access_token)
        assert user is not None
        assert user.id == test_user.id
    
    def test_get_user_by_token_invalid(self, auth_service):
        """Test getting user with invalid token."""
        user = auth_service.get_user_by_token("invalid_token")
        assert user is None


class TestUserProfile:
    """Test user profile management."""
    
    def test_update_user_email(self, auth_service, test_user):
        """Test updating user email."""
        new_email = "<EMAIL>"
        updated_user = auth_service.update_user_profile(
            user_id=test_user.id,
            email=new_email
        )
        
        assert updated_user.email == new_email
    
    def test_update_user_password(self, auth_service, test_user, test_user_data):
        """Test updating user password."""
        new_password = "NewPassword123!"
        updated_user = auth_service.update_user_profile(
            user_id=test_user.id,
            current_password=test_user_data["password"],
            new_password=new_password
        )
        
        # Verify new password works
        assert verify_password(new_password, updated_user.password_hash)
        assert not verify_password(test_user_data["password"], updated_user.password_hash)
    
    def test_update_password_wrong_current(self, auth_service, test_user):
        """Test updating password with wrong current password."""
        with pytest.raises(ValidationError, match="Current password is incorrect"):
            auth_service.update_user_profile(
                user_id=test_user.id,
                current_password="wrong_password",
                new_password="NewPassword123!"
            )
    
    def test_update_password_weak_new(self, auth_service, test_user, test_user_data):
        """Test updating to weak password."""
        with pytest.raises(ValidationError, match="New password must be"):
            auth_service.update_user_profile(
                user_id=test_user.id,
                current_password=test_user_data["password"],
                new_password="weak"
            )


class TestPasswordReset:
    """Test password reset functionality."""
    
    def test_create_password_reset_token(self, auth_service, test_user):
        """Test password reset token creation."""
        token = auth_service.create_password_reset_token(test_user.email)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        from auth.security import verify_token
        token_data = verify_token(token, "reset")
        assert token_data.user_id == test_user.id
    
    def test_reset_password_success(self, auth_service, test_user, db_session):
        """Test successful password reset."""
        # Create reset token
        token = auth_service.create_password_reset_token(test_user.email)
        new_password = "NewResetPass123!"
        
        # Reset password
        result = auth_service.reset_password(token, new_password)
        assert result is True
        
        # Verify new password works
        db_session.refresh(test_user)
        assert verify_password(new_password, test_user.password_hash)
    
    def test_reset_password_invalid_token(self, auth_service):
        """Test password reset with invalid token."""
        with pytest.raises(ValidationError, match="Invalid or expired reset token"):
            auth_service.reset_password("invalid_token", "NewPassword123!")
    
    def test_reset_password_weak_password(self, auth_service, test_user):
        """Test password reset with weak password."""
        token = auth_service.create_password_reset_token(test_user.email)
        
        with pytest.raises(ValidationError, match="Password must be"):
            auth_service.reset_password(token, "weak")


class TestAdminFunctions:
    """Test admin-only functions."""
    
    def test_get_all_users(self, auth_service, test_user, admin_user):
        """Test getting all users."""
        users = auth_service.get_all_users()
        assert len(users) >= 2
        
        usernames = [user.username for user in users]
        assert test_user.username in usernames
        assert admin_user.username in usernames
    
    def test_update_user_status(self, auth_service, test_user):
        """Test updating user active status."""
        # Deactivate user
        updated_user = auth_service.update_user_status(test_user.id, False)
        assert not updated_user.is_active
        
        # Reactivate user
        updated_user = auth_service.update_user_status(test_user.id, True)
        assert updated_user.is_active
    
    def test_update_user_admin_status(self, auth_service, test_user):
        """Test updating user admin status."""
        # Make user admin
        updated_user = auth_service.update_user_admin_status(test_user.id, True)
        assert updated_user.is_admin
        
        # Remove admin status
        updated_user = auth_service.update_user_admin_status(test_user.id, False)
        assert not updated_user.is_admin
    
    def test_delete_user(self, auth_service, test_user, db_session):
        """Test user deletion."""
        user_id = test_user.id
        
        # Delete user
        result = auth_service.delete_user(user_id)
        assert result is True
        
        # Verify user is deleted
        deleted_user = db_session.query(User).filter(User.id == user_id).first()
        assert deleted_user is None