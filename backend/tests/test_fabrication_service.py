"""
Tests for fabrication progress tracking service
"""
import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from services.fabrication_service import FabricationService
from models.user import User
from models.design import Design, BOMItem
from models.fabrication import FabricationStage, FabricationTeam, TeamMember, PartFabricationProgress
from test_setup import get_test_db, create_test_user, create_test_design


@pytest.fixture
def test_db():
    """Get test database session"""
    return next(get_test_db())


@pytest.fixture
def test_user(test_db: Session):
    """Create a test user"""
    return create_test_user(test_db, "fab_service_user", "<EMAIL>")


@pytest.fixture
def test_design_with_bom(test_db: Session, test_user: User):
    """Create a test design with BOM items"""
    design = create_test_design(test_db, test_user.id, "Service Test Design")
    
    # Add BOM items
    bom_items = [
        BOMItem(
            design_id=design.id,
            part_number="SVC-001",
            description="Service Test Part 1",
            quantity=1,
            material="Steel A36",
            volume=100.0,
            weight=785.0
        ),
        BOMItem(
            design_id=design.id,
            part_number="SVC-002",
            description="Service Test Part 2",
            quantity=3,
            material="Aluminum 6061",
            volume=25.0,
            weight=67.5
        )
    ]
    
    for item in bom_items:
        test_db.add(item)
    
    test_db.commit()
    return design


@pytest.fixture
def fabrication_service(test_db: Session):
    """Create fabrication service instance"""
    return FabricationService(test_db)


class TestStageManagement:
    """Test fabrication stage management"""
    
    def test_get_fabrication_stages(self, fabrication_service: FabricationService):
        """Test getting fabrication stages"""
        stages = fabrication_service.get_fabrication_stages()
        
        assert isinstance(stages, list)
        assert len(stages) > 0
        
        # Check that default stages exist
        stage_names = [stage["name"] for stage in stages]
        expected_stages = ["Not Started", "Material Prep", "Machining", "Welding/Assembly", 
                          "Quality Check", "Finishing", "Completed"]
        
        for expected_stage in expected_stages:
            assert expected_stage in stage_names
    
    def test_get_default_stages_only(self, fabrication_service: FabricationService):
        """Test getting only default stages"""
        stages = fabrication_service.get_fabrication_stages(include_custom=False)
        
        for stage in stages:
            assert stage["is_default"] == True
    
    def test_create_fabrication_stage(self, fabrication_service: FabricationService, test_user: User):
        """Test creating a custom fabrication stage"""
        stage_data = fabrication_service.create_fabrication_stage(
            name="Custom Inspection",
            description="Custom inspection process",
            order_index=15,
            created_by=test_user.id
        )
        
        assert stage_data["name"] == "Custom Inspection"
        assert stage_data["description"] == "Custom inspection process"
        assert stage_data["order_index"] == 15
        assert stage_data["is_default"] == False
        assert stage_data["created_by"] == test_user.id
    
    def test_update_fabrication_stage(self, fabrication_service: FabricationService, 
                                    test_db: Session, test_user: User):
        """Test updating a fabrication stage"""
        # Create a stage first
        stage = FabricationStage(
            name="Update Test",
            description="Original description",
            order_index=20,
            is_default=False,
            created_by=test_user.id
        )
        test_db.add(stage)
        test_db.commit()
        test_db.refresh(stage)
        
        # Update the stage
        updated_stage = fabrication_service.update_fabrication_stage(
            stage_id=stage.id,
            name="Updated Name",
            description="Updated description",
            order_index=25
        )
        
        assert updated_stage["name"] == "Updated Name"
        assert updated_stage["description"] == "Updated description"
        assert updated_stage["order_index"] == 25
    
    def test_update_nonexistent_stage(self, fabrication_service: FabricationService):
        """Test updating a non-existent stage"""
        with pytest.raises(ValueError, match="Stage not found"):
            fabrication_service.update_fabrication_stage(
                stage_id=99999,
                name="Should Fail"
            )
    
    def test_delete_fabrication_stage(self, fabrication_service: FabricationService, 
                                    test_db: Session, test_user: User):
        """Test deleting a custom fabrication stage"""
        # Create a custom stage
        stage = FabricationStage(
            name="Delete Test",
            description="Stage to delete",
            order_index=30,
            is_default=False,
            created_by=test_user.id
        )
        test_db.add(stage)
        test_db.commit()
        test_db.refresh(stage)
        
        # Delete the stage
        result = fabrication_service.delete_fabrication_stage(stage.id)
        assert result == True
        
        # Verify it's deleted
        deleted_stage = test_db.query(FabricationStage).filter(FabricationStage.id == stage.id).first()
        assert deleted_stage is None
    
    def test_cannot_delete_default_stage(self, fabrication_service: FabricationService, test_db: Session):
        """Test that default stages cannot be deleted"""
        # Get a default stage
        default_stage = test_db.query(FabricationStage).filter(
            FabricationStage.is_default == True
        ).first()
        
        result = fabrication_service.delete_fabrication_stage(default_stage.id)
        assert result == False


class TestTeamManagement:
    """Test fabrication team management"""
    
    def test_get_fabrication_teams(self, fabrication_service: FabricationService):
        """Test getting fabrication teams"""
        teams = fabrication_service.get_fabrication_teams()
        assert isinstance(teams, list)
    
    def test_create_fabrication_team(self, fabrication_service: FabricationService, test_user: User):
        """Test creating a fabrication team"""
        team_data = fabrication_service.create_fabrication_team(
            name="Test Fabrication Team",
            description="Team for testing",
            lead_user_id=test_user.id
        )
        
        assert team_data["name"] == "Test Fabrication Team"
        assert team_data["description"] == "Team for testing"
        assert team_data["lead_user_id"] == test_user.id
        assert team_data["is_active"] == True
        assert team_data["member_count"] == 0
    
    def test_add_team_member(self, fabrication_service: FabricationService, 
                           test_db: Session, test_user: User):
        """Test adding a member to a team"""
        # Create a team
        team = FabricationTeam(
            name="Member Test Team",
            description="Team for member testing",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        # Create another user
        member_user = create_test_user(test_db, "team_member", "<EMAIL>")
        
        # Add member to team
        result = fabrication_service.add_team_member(
            team_id=team.id,
            user_id=member_user.id,
            role="Fabricator"
        )
        
        assert result == True
        
        # Verify member was added
        member = test_db.query(TeamMember).filter(
            TeamMember.team_id == team.id,
            TeamMember.user_id == member_user.id
        ).first()
        assert member is not None
        assert member.role == "Fabricator"
    
    def test_add_duplicate_team_member(self, fabrication_service: FabricationService, 
                                     test_db: Session, test_user: User):
        """Test adding a duplicate team member"""
        # Create team and add member
        team = FabricationTeam(
            name="Duplicate Test Team",
            description="Team for duplicate testing",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        member_user = create_test_user(test_db, "duplicate_member", "<EMAIL>")
        
        # Add member first time
        result1 = fabrication_service.add_team_member(team.id, member_user.id)
        assert result1 == True
        
        # Try to add same member again
        result2 = fabrication_service.add_team_member(team.id, member_user.id)
        assert result2 == False
    
    def test_remove_team_member(self, fabrication_service: FabricationService, 
                              test_db: Session, test_user: User):
        """Test removing a team member"""
        # Create team and add member
        team = FabricationTeam(
            name="Remove Test Team",
            description="Team for removal testing",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        member_user = create_test_user(test_db, "remove_member", "<EMAIL>")
        fabrication_service.add_team_member(team.id, member_user.id)
        
        # Remove member
        result = fabrication_service.remove_team_member(team.id, member_user.id)
        assert result == True
        
        # Verify member was removed
        member = test_db.query(TeamMember).filter(
            TeamMember.team_id == team.id,
            TeamMember.user_id == member_user.id
        ).first()
        assert member is None
    
    def test_remove_nonexistent_member(self, fabrication_service: FabricationService, 
                                     test_db: Session, test_user: User):
        """Test removing a non-existent team member"""
        # Create team
        team = FabricationTeam(
            name="Nonexistent Test Team",
            description="Team for nonexistent testing",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        # Try to remove non-existent member
        result = fabrication_service.remove_team_member(team.id, 99999)
        assert result == False


class TestProgressTracking:
    """Test fabrication progress tracking"""
    
    def test_get_design_progress(self, fabrication_service: FabricationService, 
                               test_design_with_bom: Design):
        """Test getting design progress"""
        progress = fabrication_service.get_design_progress(test_design_with_bom.id)
        
        assert progress["design_id"] == test_design_with_bom.id
        assert progress["design_name"] == test_design_with_bom.name
        assert progress["total_parts"] == 2
        assert progress["completed_parts"] == 0
        assert progress["completion_percentage"] == 0.0
        assert len(progress["parts_progress"]) == 2
        
        # Check that initial progress entries were created
        for part_progress in progress["parts_progress"]:
            assert part_progress["current_stage"]["name"] == "Not Started"
    
    def test_get_nonexistent_design_progress(self, fabrication_service: FabricationService):
        """Test getting progress for non-existent design"""
        with pytest.raises(ValueError, match="Design not found"):
            fabrication_service.get_design_progress(99999)
    
    def test_update_part_status(self, fabrication_service: FabricationService, 
                              test_design_with_bom: Design, test_db: Session, test_user: User):
        """Test updating part fabrication status"""
        # Get a BOM item
        bom_item = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).first()
        
        # Get machining stage
        machining_stage = test_db.query(FabricationStage).filter(
            FabricationStage.name == "Machining"
        ).first()
        
        # Update part status
        estimated_completion = datetime.utcnow() + timedelta(days=5)
        updated_progress = fabrication_service.update_part_status(
            design_id=test_design_with_bom.id,
            part_id=bom_item.id,
            stage_id=machining_stage.id,
            updated_by=test_user.id,
            notes="Started machining operations",
            estimated_completion=estimated_completion
        )
        
        assert updated_progress["current_stage"]["id"] == machining_stage.id
        assert updated_progress["current_stage"]["name"] == "Machining"
        assert updated_progress["notes"] == "Started machining operations"
        assert updated_progress["estimated_completion"] is not None
    
    def test_update_to_completed_status(self, fabrication_service: FabricationService, 
                                      test_design_with_bom: Design, test_db: Session, test_user: User):
        """Test updating part to completed status"""
        # Get a BOM item
        bom_item = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).first()
        
        # Get completed stage
        completed_stage = test_db.query(FabricationStage).filter(
            FabricationStage.name == "Completed"
        ).first()
        
        # Update to completed
        updated_progress = fabrication_service.update_part_status(
            design_id=test_design_with_bom.id,
            part_id=bom_item.id,
            stage_id=completed_stage.id,
            updated_by=test_user.id,
            notes="Part completed successfully"
        )
        
        assert updated_progress["current_stage"]["name"] == "Completed"
        assert updated_progress["actual_completion"] is not None
    
    def test_assign_part_to_user(self, fabrication_service: FabricationService, 
                               test_design_with_bom: Design, test_db: Session, test_user: User):
        """Test assigning a part to a user"""
        # Get a BOM item
        bom_item = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).first()
        
        # Assign part to user
        assigned_progress = fabrication_service.assign_part(
            design_id=test_design_with_bom.id,
            part_id=bom_item.id,
            assigned_to=test_user.id
        )
        
        assert assigned_progress["assigned_to"] == test_user.id
        assert assigned_progress["assigned_user_name"] == test_user.username
    
    def test_assign_part_to_team(self, fabrication_service: FabricationService, 
                               test_design_with_bom: Design, test_db: Session, test_user: User):
        """Test assigning a part to a team"""
        # Create a team
        team = FabricationTeam(
            name="Assignment Test Team",
            description="Team for assignment testing",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        # Get a BOM item
        bom_item = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).first()
        
        # Assign part to team
        assigned_progress = fabrication_service.assign_part(
            design_id=test_design_with_bom.id,
            part_id=bom_item.id,
            assigned_team_id=team.id
        )
        
        assert assigned_progress["assigned_team_id"] == team.id
        assert assigned_progress["assigned_team_name"] == team.name
    
    def test_get_timeline_data(self, fabrication_service: FabricationService, 
                             test_design_with_bom: Design, test_db: Session, test_user: User):
        """Test getting timeline data"""
        # Update some part statuses to create history
        bom_items = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).all()
        machining_stage = test_db.query(FabricationStage).filter(
            FabricationStage.name == "Machining"
        ).first()
        
        # Update first part status
        fabrication_service.update_part_status(
            design_id=test_design_with_bom.id,
            part_id=bom_items[0].id,
            stage_id=machining_stage.id,
            updated_by=test_user.id
        )
        
        # Get timeline data
        timeline = fabrication_service.get_timeline_data(test_design_with_bom.id)
        
        assert timeline["design_id"] == test_design_with_bom.id
        assert len(timeline["timeline_data"]) == 2
        
        # Check timeline structure
        timeline_item = timeline["timeline_data"][0]
        assert "part_id" in timeline_item
        assert "part_number" in timeline_item
        assert "description" in timeline_item
        assert "current_stage" in timeline_item
        assert "stage_history" in timeline_item
    
    def test_get_progress_reports(self, fabrication_service: FabricationService, 
                                test_design_with_bom: Design, test_db: Session, test_user: User):
        """Test getting progress reports"""
        # Create some progress data
        bom_items = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).all()
        completed_stage = test_db.query(FabricationStage).filter(
            FabricationStage.name == "Completed"
        ).first()
        
        # Complete one part
        fabrication_service.update_part_status(
            design_id=test_design_with_bom.id,
            part_id=bom_items[0].id,
            stage_id=completed_stage.id,
            updated_by=test_user.id
        )
        
        # Get reports
        reports = fabrication_service.get_progress_reports(test_design_with_bom.id)
        
        assert "stage_distribution" in reports
        assert "overdue_parts" in reports
        assert "completion_by_day" in reports
        assert "total_parts" in reports
        assert "completed_parts" in reports
        
        assert reports["total_parts"] == 2
        assert reports["completed_parts"] == 1
        assert "Completed" in reports["stage_distribution"]
        assert reports["stage_distribution"]["Completed"] == 1
    
    def test_get_dashboard_overview(self, fabrication_service: FabricationService, 
                                  test_design_with_bom: Design):
        """Test getting dashboard overview"""
        dashboard = fabrication_service.get_dashboard_overview()
        
        assert "projects" in dashboard
        assert "overall_stats" in dashboard
        
        overall_stats = dashboard["overall_stats"]
        assert "total_projects" in overall_stats
        assert "total_parts" in overall_stats
        assert "completed_parts" in overall_stats
        assert "overall_completion" in overall_stats
        
        # Should have at least our test design
        assert overall_stats["total_projects"] >= 1
        assert overall_stats["total_parts"] >= 2


class TestHelperMethods:
    """Test helper methods"""
    
    def test_create_initial_progress(self, fabrication_service: FabricationService, 
                                   test_design_with_bom: Design, test_db: Session):
        """Test creating initial progress entry"""
        # Get a BOM item
        bom_item = test_db.query(BOMItem).filter(BOMItem.design_id == test_design_with_bom.id).first()
        
        # Create initial progress
        progress = fabrication_service._create_initial_progress(test_design_with_bom.id, bom_item.id)
        
        assert progress.design_id == test_design_with_bom.id
        assert progress.part_id == bom_item.id
        assert progress.current_stage.name == "Not Started"
        assert progress.started_at is not None
    
    def test_stage_to_dict(self, fabrication_service: FabricationService, test_db: Session):
        """Test stage to dictionary conversion"""
        stage = test_db.query(FabricationStage).filter(
            FabricationStage.name == "Not Started"
        ).first()
        
        stage_dict = fabrication_service._stage_to_dict(stage)
        
        assert stage_dict["id"] == stage.id
        assert stage_dict["name"] == stage.name
        assert stage_dict["description"] == stage.description
        assert stage_dict["order_index"] == stage.order_index
        assert stage_dict["is_default"] == stage.is_default
    
    def test_team_to_dict(self, fabrication_service: FabricationService, 
                        test_db: Session, test_user: User):
        """Test team to dictionary conversion"""
        # Create a team with members
        team = FabricationTeam(
            name="Dict Test Team",
            description="Team for dict testing",
            lead_user_id=test_user.id
        )
        test_db.add(team)
        test_db.commit()
        test_db.refresh(team)
        
        team_dict = fabrication_service._team_to_dict(team)
        
        assert team_dict["id"] == team.id
        assert team_dict["name"] == team.name
        assert team_dict["description"] == team.description
        assert team_dict["lead_user_id"] == team.lead_user_id
        assert team_dict["lead_user_name"] == test_user.username
        assert team_dict["is_active"] == team.is_active
        assert "members" in team_dict
        assert "member_count" in team_dict