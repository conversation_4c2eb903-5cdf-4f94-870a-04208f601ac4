"""
Sharing and collaboration models.
"""
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.connection import Base


class DesignShare(Base):
    """Design sharing model for collaboration."""
    
    __tablename__ = "design_shares"
    
    id = Column(Integer, primary_key=True, index=True)
    design_id = Column(Integer, ForeignKey("designs.id"), nullable=False, index=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    shared_with_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    permission_level = Column(String(20), default="view")  # 'view' or 'edit'
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Unique constraint to prevent duplicate shares
    __table_args__ = (UniqueConstraint('design_id', 'shared_with_id', name='unique_design_share'),)
    
    # Relationships
    design = relationship("Design", back_populates="shares")
    owner = relationship("User", foreign_keys=[owner_id], back_populates="owned_shares")
    shared_with = relationship("User", foreign_keys=[shared_with_id], back_populates="received_shares")