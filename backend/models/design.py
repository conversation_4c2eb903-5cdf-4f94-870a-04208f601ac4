"""
Design and analysis models.
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.connection import Base


class Design(Base):
    """Design project model."""
    
    __tablename__ = "designs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(200), nullable=False)
    original_filename = Column(String(255))
    file_path = Column(String(500))
    status = Column(String(50), default="uploaded", index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="designs")
    analysis_results = relationship("AnalysisResult", back_populates="design", cascade="all, delete-orphan")
    bom_items = relationship("BOMItem", back_populates="design", cascade="all, delete-orphan")
    models_3d = relationship("Model3D", back_populates="design", cascade="all, delete-orphan")
    shares = relationship("DesignShare", back_populates="design", cascade="all, delete-orphan")
    fabrication_progress = relationship("PartFabricationProgress", back_populates="design", cascade="all, delete-orphan")


class AnalysisResult(Base):
    """Analysis results model for storing computer vision and OCR output."""
    
    __tablename__ = "analysis_results"
    
    id = Column(Integer, primary_key=True, index=True)
    design_id = Column(Integer, ForeignKey("designs.id"), nullable=False, index=True)
    analysis_data = Column(JSON)  # JSONB in PostgreSQL
    confidence_score = Column(DECIMAL(3, 2))
    processing_time = Column(Integer)  # Processing time in seconds
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    design = relationship("Design", back_populates="analysis_results")


class BOMItem(Base):
    """Bill of Materials item model."""
    
    __tablename__ = "bom_items"
    
    id = Column(Integer, primary_key=True, index=True)
    design_id = Column(Integer, ForeignKey("designs.id"), nullable=False, index=True)
    part_number = Column(String(100))
    description = Column(Text)
    quantity = Column(Integer)
    material = Column(String(100))
    volume = Column(DECIMAL(10, 4))  # Volume in cubic units
    weight = Column(DECIMAL(10, 4))  # Total weight
    unit_weight = Column(DECIMAL(10, 4))  # Weight per unit
    
    # Relationships
    design = relationship("Design", back_populates="bom_items")
    fabrication_progress = relationship("PartFabricationProgress", back_populates="part", cascade="all, delete-orphan")


class Model3D(Base):
    """3D model storage model."""
    
    __tablename__ = "models_3d"
    
    id = Column(Integer, primary_key=True, index=True)
    design_id = Column(Integer, ForeignKey("designs.id"), nullable=False, index=True)
    model_file_path = Column(String(500))
    openscad_script = Column(Text)
    generation_time = Column(Integer)  # Generation time in seconds
    file_size = Column(Integer)  # File size in bytes
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    design = relationship("Design", back_populates="models_3d")