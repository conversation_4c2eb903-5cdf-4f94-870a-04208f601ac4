"""
Fabrication progress tracking models
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, TIMESTAMP, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.connection import Base


class FabricationStage(Base):
    """Fabrication stages for tracking part progress"""
    __tablename__ = "fabrication_stages"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    order_index = Column(Integer, nullable=False)
    is_default = Column(Boolean, default=False)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    part_progress = relationship("PartFabricationProgress", back_populates="current_stage")
    stage_history = relationship("FabricationStageHistory", back_populates="stage")


class FabricationTeam(Base):
    """Teams for fabrication work assignments"""
    __tablename__ = "fabrication_teams"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    lead_user_id = Column(Integer, ForeignKey("users.id"))
    is_active = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    # Relationships
    lead_user = relationship("User", foreign_keys=[lead_user_id])
    members = relationship("TeamMember", back_populates="team")
    assigned_parts = relationship("PartFabricationProgress", back_populates="assigned_team")


class TeamMember(Base):
    """Team member assignments"""
    __tablename__ = "team_members"
    
    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("fabrication_teams.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    role = Column(String(50))
    joined_at = Column(TIMESTAMP, server_default=func.now())
    
    __table_args__ = (UniqueConstraint('team_id', 'user_id'),)
    
    # Relationships
    team = relationship("FabricationTeam", back_populates="members")
    user = relationship("User")


class PartFabricationProgress(Base):
    """Progress tracking for individual parts"""
    __tablename__ = "part_fabrication_progress"
    
    id = Column(Integer, primary_key=True, index=True)
    design_id = Column(Integer, ForeignKey("designs.id"))
    part_id = Column(Integer, ForeignKey("bom_items.id"))
    current_stage_id = Column(Integer, ForeignKey("fabrication_stages.id"))
    assigned_to = Column(Integer, ForeignKey("users.id"))
    assigned_team_id = Column(Integer, ForeignKey("fabrication_teams.id"))
    started_at = Column(TIMESTAMP)
    estimated_completion = Column(TIMESTAMP)
    actual_completion = Column(TIMESTAMP)
    notes = Column(Text)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    design = relationship("Design")
    part = relationship("BOMItem")
    current_stage = relationship("FabricationStage", back_populates="part_progress")
    assigned_user = relationship("User", foreign_keys=[assigned_to])
    assigned_team = relationship("FabricationTeam", back_populates="assigned_parts")
    stage_history = relationship("FabricationStageHistory", back_populates="part_progress")


class FabricationStageHistory(Base):
    """History of stage transitions for parts"""
    __tablename__ = "fabrication_stage_history"
    
    id = Column(Integer, primary_key=True, index=True)
    part_progress_id = Column(Integer, ForeignKey("part_fabrication_progress.id"))
    stage_id = Column(Integer, ForeignKey("fabrication_stages.id"))
    started_at = Column(TIMESTAMP, nullable=False)
    completed_at = Column(TIMESTAMP)
    duration_minutes = Column(Integer)
    notes = Column(Text)
    updated_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    # Relationships
    part_progress = relationship("PartFabricationProgress", back_populates="stage_history")
    stage = relationship("FabricationStage", back_populates="stage_history")
    updated_by_user = relationship("User", foreign_keys=[updated_by])