"""
User management models.
"""
from sqlalchemy import <PERSON><PERSON>n, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.connection import Base


class User(Base):
    """User model for authentication and user management."""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    designs = relationship("Design", back_populates="user", cascade="all, delete-orphan")
    owned_shares = relationship("DesignShare", foreign_keys="DesignShare.owner_id", back_populates="owner")
    received_shares = relationship("DesignShare", foreign_keys="DesignShare.shared_with_id", back_populates="shared_with")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    team_memberships = relationship("TeamMember", back_populates="user")
    led_teams = relationship("FabricationTeam", back_populates="lead_user")
    assigned_parts = relationship("PartFabricationProgress", back_populates="assigned_user")
    stage_updates = relationship("FabricationStageHistory", back_populates="updated_by_user")
    created_stages = relationship("FabricationStage", back_populates="creator")


class UserSession(Base):
    """User session model for JWT token management."""
    
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="sessions")