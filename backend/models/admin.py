"""
Admin-related models for system management.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.connection import Base


class UserActivityLog(Base):
    """User activity log model for audit tracking."""
    
    __tablename__ = "user_activity_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    action = Column(String(100), nullable=False, index=True)
    resource_type = Column(String(50), index=True)
    resource_id = Column(Integer)
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    request_data = Column(Text)  # JSON string of request data
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # Relationships
    user = relationship("User")


class ErrorLog(Base):
    """Error log model for system error tracking."""
    
    __tablename__ = "error_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    error_type = Column(String(100), nullable=False, index=True)
    error_message = Column(Text, nullable=False)
    stack_trace = Column(Text)
    user_id = Column(Integer, ForeignKey("users.id"))
    endpoint = Column(String(200))
    request_data = Column(Text)  # JSON string of request data
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    resolved = Column(Boolean, default=False, index=True)
    resolved_at = Column(DateTime(timezone=True))
    resolved_by = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    resolver = relationship("User", foreign_keys=[resolved_by])


class SystemHealthMetric(Base):
    """System health metrics model."""
    
    __tablename__ = "system_health_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String(100), nullable=False, index=True)
    value = Column(String(50), nullable=False)  # Store as string to handle different data types
    unit = Column(String(20))
    status = Column(String(20), nullable=False, index=True)  # 'healthy', 'warning', 'critical'
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)