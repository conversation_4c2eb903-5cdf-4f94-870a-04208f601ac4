"""
Material properties models.
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, DECIMAL
from sqlalchemy.sql import func
from database.connection import Base


class Material(Base):
    """Material properties model for weight calculations."""
    
    __tablename__ = "materials"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    density = Column(DECIMAL(8, 4), nullable=False)  # Density in kg/m³
    category = Column(String(50), index=True)
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())