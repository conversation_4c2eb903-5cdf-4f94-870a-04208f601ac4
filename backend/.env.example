# Database Configuration
DATABASE_URL=postgresql://dev_user:dev_password@localhost:5432/bom_generator_dev

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage Configuration
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=bom-generator-files

# OpenSCAD Configuration
OPENSCAD_PATH=/usr/bin/openscad

# Tesseract Configuration
TESSERACT_CMD=tesseract

# Development Settings
DEBUG=True
CORS_ORIGINS=["http://localhost:3000"]