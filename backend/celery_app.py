"""
Celery application configuration for asynchronous task processing
"""

import os
from celery import Celery
import redis
from services.drawing_analysis_service import DrawingAnalysisService, AnalysisRequest

# Create Celery app
celery_app = Celery(
    'drawing_analysis',
    broker=os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
    backend=os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
    include=['celery_tasks']
)

# Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Task routing
celery_app.conf.task_routes = {
    'drawing_analysis.analyze_drawing': {'queue': 'analysis'},
}

if __name__ == '__main__':
    celery_app.start()