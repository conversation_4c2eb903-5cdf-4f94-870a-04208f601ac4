"""
Celery tasks for drawing analysis processing
"""

import asyncio
import logging
import redis
from celery import Celery
from celery_app import celery_app
from services.drawing_analysis_service import DrawingAnalysisService, AnalysisRequest
from services.model_3d_service import model_3d_service
from database.connection import get_database

logger = logging.getLogger(__name__)

# Initialize Redis client
redis_client = redis.Redis.from_url(celery_app.conf.broker_url)

# Initialize analysis service
analysis_service = DrawingAnalysisService(redis_client, celery_app)


@celery_app.task(bind=True, name='drawing_analysis.analyze_drawing')
def analyze_drawing_task(self, analysis_request_dict):
    """
    Celery task for analyzing engineering drawings
    
    Args:
        analysis_request_dict: Dictionary containing analysis request data
        
    Returns:
        Analysis results dictionary
    """
    try:
        # Convert dict back to AnalysisRequest
        analysis_request = AnalysisRequest(**analysis_request_dict)
        
        logger.info(f"Starting analysis for design {analysis_request.design_id}")
        
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={
                'design_id': analysis_request.design_id,
                'status': 'starting',
                'progress': 0
            }
        )
        
        # Run the analysis (need to handle async in sync context)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                analysis_service.perform_analysis(analysis_request)
            )
            
            logger.info(f"Analysis completed for design {analysis_request.design_id}")
            
            # Convert result to dict for serialization
            result_dict = {
                'design_id': result.design_id,
                'cv_results': result.cv_results,
                'ocr_results': result.ocr_results,
                'confidence_assessment': result.confidence_assessment,
                'quality_report': result.quality_report,
                'processing_time': result.processing_time,
                'errors': result.errors,
                'warnings': result.warnings,
                'created_at': result.created_at
            }
            
            return result_dict
            
        finally:
            loop.close()
            
    except Exception as e:
        error_msg = f"Analysis task failed: {str(e)}"
        logger.error(error_msg)
        
        # Update task state with error
        self.update_state(
            state='FAILURE',
            meta={
                'design_id': analysis_request_dict.get('design_id'),
                'error': error_msg
            }
        )
        
        raise Exception(error_msg)


@celery_app.task(name='drawing_analysis.health_check')
def health_check_task():
    """
    Health check task for the analysis system
    
    Returns:
        Health status
    """
    try:
        # Test Redis connection
        redis_client.ping()
        
        # Test basic CV and OCR functionality
        import cv2
        import numpy as np
        import pytesseract
        
        # Create test image
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "TEST", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Test OCR
        text = pytesseract.image_to_string(test_image)
        
        # Test CV
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        return {
            'status': 'healthy',
            'redis_connected': True,
            'ocr_working': 'TEST' in text.upper(),
            'cv_working': edges is not None,
            'timestamp': str(asyncio.get_event_loop().time())
        }
        
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': str(asyncio.get_event_loop().time())
        }


@celery_app.task(name='drawing_analysis.cleanup_cache')
def cleanup_cache_task():
    """
    Cleanup old cached results and progress data
    
    Returns:
        Cleanup statistics
    """
    try:
        # Get all analysis result keys
        result_keys = redis_client.keys('analysis_result:*')
        progress_keys = redis_client.keys('analysis_progress:*')
        
        cleaned_results = 0
        cleaned_progress = 0
        
        # Clean up old results (older than 7 days)
        import time
        week_ago = time.time() - (7 * 24 * 60 * 60)
        
        for key in result_keys:
            try:
                # Check if key is old (this is a simplified check)
                ttl = redis_client.ttl(key)
                if ttl < 0:  # Key has no expiration
                    redis_client.delete(key)
                    cleaned_results += 1
            except Exception:
                continue
        
        # Clean up completed progress entries older than 1 day
        day_ago = time.time() - (24 * 60 * 60)
        
        for key in progress_keys:
            try:
                progress_data = redis_client.get(key)
                if progress_data:
                    import json
                    progress = json.loads(progress_data)
                    if progress.get('status') in ['completed', 'failed']:
                        redis_client.delete(key)
                        cleaned_progress += 1
            except Exception:
                continue
        
        return {
            'cleaned_results': cleaned_results,
            'cleaned_progress': cleaned_progress,
            'total_result_keys': len(result_keys),
            'total_progress_keys': len(progress_keys)
        }
        
    except Exception as e:
        logger.error(f"Cache cleanup failed: {str(e)}")
        return {
            'error': str(e),
            'cleaned_results': 0,
            'cleaned_progress': 0
        }


@celery_app.task(bind=True, name='model_3d.generate_model')
def generate_3d_model_task(self, design_id):
    """
    Celery task for generating 3D models from design analysis data
    
    Args:
        design_id: ID of the design to generate model for
        
    Returns:
        Model generation results dictionary
    """
    try:
        logger.info(f"Starting 3D model generation for design {design_id}")
        
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={
                'design_id': design_id,
                'status': 'starting',
                'progress': 0
            }
        )
        
        # Get database session
        db = next(get_database())
        
        try:
            # Update progress
            self.update_state(
                state='PROGRESS',
                meta={
                    'design_id': design_id,
                    'status': 'generating_script',
                    'progress': 25
                }
            )
            
            # Generate 3D model
            model_3d = model_3d_service.generate_3d_model(design_id, db)
            
            if not model_3d:
                raise Exception("Failed to generate 3D model")
            
            # Update progress
            self.update_state(
                state='PROGRESS',
                meta={
                    'design_id': design_id,
                    'status': 'executing_openscad',
                    'progress': 75
                }
            )
            
            logger.info(f"3D model generation completed for design {design_id}")
            
            # Return success result
            result_dict = {
                'design_id': design_id,
                'model_id': model_3d.id,
                'model_file_path': model_3d.model_file_path,
                'generation_time': model_3d.generation_time,
                'file_size': model_3d.file_size,
                'status': 'completed'
            }
            
            return result_dict
            
        finally:
            db.close()
            
    except Exception as e:
        error_msg = f"3D model generation task failed: {str(e)}"
        logger.error(error_msg)
        
        # Update task state with error
        self.update_state(
            state='FAILURE',
            meta={
                'design_id': design_id,
                'error': error_msg
            }
        )
        
        raise Exception(error_msg)