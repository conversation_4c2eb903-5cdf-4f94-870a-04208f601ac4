"""
Material Database Seeding Script

Seeds the materials table with common engineering materials
and their density properties for weight calculations.
"""

import sys
import os
from sqlalchemy.orm import Session

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connection import get_database, engine
from models.material import Material


def seed_materials():
    """Seed the materials table with common engineering materials"""
    
    # Common engineering materials with densities in kg/m³
    materials_data = [
        # Steel alloys
        {
            'name': 'Carbon Steel A36',
            'density': 7850.0,
            'category': 'Steel',
            'description': 'Common structural steel, good weldability and machinability'
        },
        {
            'name': 'Carbon Steel 1018',
            'density': 7870.0,
            'category': 'Steel',
            'description': 'Low carbon steel, excellent machinability'
        },
        {
            'name': 'Carbon Steel 1045',
            'density': 7870.0,
            'category': 'Steel',
            'description': 'Medium carbon steel, good strength and hardness'
        },
        {
            'name': 'Stainless Steel 304',
            'density': 8000.0,
            'category': 'Steel',
            'description': 'Austenitic stainless steel, corrosion resistant'
        },
        {
            'name': 'Stainless Steel 316',
            'density': 8000.0,
            'category': 'Steel',
            'description': 'Marine grade stainless steel, superior corrosion resistance'
        },
        {
            'name': 'Stainless Steel 410',
            'density': 7750.0,
            'category': 'Steel',
            'description': 'Martensitic stainless steel, magnetic and hardenable'
        },
        {
            'name': 'Tool Steel O1',
            'density': 7850.0,
            'category': 'Steel',
            'description': 'Oil hardening tool steel, good wear resistance'
        },
        {
            'name': 'Tool Steel A2',
            'density': 7870.0,
            'category': 'Steel',
            'description': 'Air hardening tool steel, excellent toughness'
        },
        
        # Aluminum alloys
        {
            'name': 'Aluminum 6061',
            'density': 2700.0,
            'category': 'Aluminum',
            'description': 'Versatile aluminum alloy, good strength and corrosion resistance'
        },
        {
            'name': 'Aluminum 6063',
            'density': 2700.0,
            'category': 'Aluminum',
            'description': 'Architectural aluminum, excellent extrudability'
        },
        {
            'name': 'Aluminum 7075',
            'density': 2810.0,
            'category': 'Aluminum',
            'description': 'High strength aluminum alloy, aerospace grade'
        },
        {
            'name': 'Aluminum 2024',
            'density': 2780.0,
            'category': 'Aluminum',
            'description': 'High strength aluminum, aircraft structural applications'
        },
        {
            'name': 'Aluminum 5052',
            'density': 2680.0,
            'category': 'Aluminum',
            'description': 'Marine grade aluminum, excellent corrosion resistance'
        },
        {
            'name': 'Aluminum 1100',
            'density': 2710.0,
            'category': 'Aluminum',
            'description': 'Pure aluminum, excellent corrosion resistance and formability'
        },
        
        # Copper alloys
        {
            'name': 'Copper 110',
            'density': 8960.0,
            'category': 'Copper',
            'description': 'Pure copper, excellent electrical and thermal conductivity'
        },
        {
            'name': 'Brass 360',
            'density': 8500.0,
            'category': 'Brass',
            'description': 'Free machining brass, excellent machinability'
        },
        {
            'name': 'Brass 260',
            'density': 8530.0,
            'category': 'Brass',
            'description': 'Cartridge brass, good formability and corrosion resistance'
        },
        {
            'name': 'Bronze 932',
            'density': 8800.0,
            'category': 'Bronze',
            'description': 'Bearing bronze, excellent wear resistance'
        },
        {
            'name': 'Phosphor Bronze',
            'density': 8800.0,
            'category': 'Bronze',
            'description': 'Spring bronze, good fatigue resistance'
        },
        
        # Titanium alloys
        {
            'name': 'Titanium Grade 2',
            'density': 4510.0,
            'category': 'Titanium',
            'description': 'Commercially pure titanium, excellent corrosion resistance'
        },
        {
            'name': 'Titanium Ti-6Al-4V',
            'density': 4430.0,
            'category': 'Titanium',
            'description': 'Aerospace grade titanium alloy, high strength-to-weight ratio'
        },
        
        # Plastics
        {
            'name': 'ABS Plastic',
            'density': 1050.0,
            'category': 'Plastic',
            'description': 'Acrylonitrile Butadiene Styrene, good impact resistance'
        },
        {
            'name': 'PLA Plastic',
            'density': 1240.0,
            'category': 'Plastic',
            'description': 'Polylactic Acid, biodegradable 3D printing material'
        },
        {
            'name': 'PETG Plastic',
            'density': 1270.0,
            'category': 'Plastic',
            'description': 'Polyethylene Terephthalate Glycol, chemical resistant'
        },
        {
            'name': 'Nylon 6/6',
            'density': 1140.0,
            'category': 'Plastic',
            'description': 'Polyamide, excellent mechanical properties'
        },
        {
            'name': 'Polycarbonate',
            'density': 1200.0,
            'category': 'Plastic',
            'description': 'High impact strength, optical clarity'
        },
        {
            'name': 'HDPE',
            'density': 950.0,
            'category': 'Plastic',
            'description': 'High Density Polyethylene, chemical resistant'
        },
        {
            'name': 'PVC',
            'density': 1400.0,
            'category': 'Plastic',
            'description': 'Polyvinyl Chloride, versatile thermoplastic'
        },
        {
            'name': 'Acetal (Delrin)',
            'density': 1410.0,
            'category': 'Plastic',
            'description': 'Polyoxymethylene, excellent dimensional stability'
        },
        
        # Cast Iron
        {
            'name': 'Gray Cast Iron',
            'density': 7200.0,
            'category': 'Cast Iron',
            'description': 'Good machinability and vibration damping'
        },
        {
            'name': 'Ductile Iron',
            'density': 7100.0,
            'category': 'Cast Iron',
            'description': 'Nodular iron, good ductility and strength'
        },
        
        # Other metals
        {
            'name': 'Magnesium AZ31',
            'density': 1770.0,
            'category': 'Magnesium',
            'description': 'Lightweight structural alloy'
        },
        {
            'name': 'Zinc Alloy',
            'density': 6600.0,
            'category': 'Zinc',
            'description': 'Die casting alloy, good surface finish'
        },
        {
            'name': 'Lead',
            'density': 11340.0,
            'category': 'Lead',
            'description': 'Dense metal, radiation shielding applications'
        },
        
        # Composites
        {
            'name': 'Carbon Fiber Composite',
            'density': 1600.0,
            'category': 'Composite',
            'description': 'High strength-to-weight ratio, aerospace applications'
        },
        {
            'name': 'Fiberglass Composite',
            'density': 1800.0,
            'category': 'Composite',
            'description': 'Glass fiber reinforced plastic, good electrical insulation'
        },
        
        # Wood (for reference)
        {
            'name': 'Oak Wood',
            'density': 750.0,
            'category': 'Wood',
            'description': 'Hardwood, furniture and construction applications'
        },
        {
            'name': 'Pine Wood',
            'density': 520.0,
            'category': 'Wood',
            'description': 'Softwood, construction and general purpose'
        },
        {
            'name': 'Plywood',
            'density': 600.0,
            'category': 'Wood',
            'description': 'Engineered wood product, structural applications'
        }
    ]
    
    db = next(get_database())
    
    try:
        print("Seeding materials database...")
        
        # Check if materials already exist
        existing_count = db.query(Material).count()
        if existing_count > 0:
            print(f"Found {existing_count} existing materials.")
            response = input("Do you want to clear existing materials and reseed? (y/N): ")
            if response.lower() == 'y':
                db.query(Material).delete()
                db.commit()
                print("Cleared existing materials.")
            else:
                print("Skipping seeding to preserve existing data.")
                return
        
        # Add materials
        added_count = 0
        for material_data in materials_data:
            # Check if material already exists
            existing = db.query(Material).filter(
                Material.name == material_data['name']
            ).first()
            
            if not existing:
                material = Material(**material_data)
                db.add(material)
                added_count += 1
            else:
                print(f"Material '{material_data['name']}' already exists, skipping.")
        
        db.commit()
        
        print(f"Successfully added {added_count} materials to the database.")
        
        # Display summary by category
        print("\nMaterials by category:")
        categories = db.query(Material.category).distinct().all()
        for (category,) in categories:
            count = db.query(Material).filter(Material.category == category).count()
            print(f"  {category}: {count} materials")
        
        print(f"\nTotal materials in database: {db.query(Material).count()}")
        
    except Exception as e:
        db.rollback()
        print(f"Error seeding materials: {str(e)}")
        raise
    finally:
        db.close()


def list_materials():
    """List all materials in the database"""
    db = next(get_database())
    
    try:
        materials = db.query(Material).order_by(Material.category, Material.name).all()
        
        if not materials:
            print("No materials found in database.")
            return
        
        print(f"\nMaterials Database ({len(materials)} total):")
        print("=" * 80)
        
        current_category = None
        for material in materials:
            if material.category != current_category:
                current_category = material.category
                print(f"\n{current_category}:")
                print("-" * 40)
            
            print(f"  {material.name:<30} {material.density:>8.1f} kg/m³")
            if material.description:
                print(f"    {material.description}")
    
    finally:
        db.close()


def search_materials(search_term):
    """Search for materials by name or category"""
    db = next(get_database())
    
    try:
        materials = db.query(Material).filter(
            Material.name.ilike(f"%{search_term}%") |
            Material.category.ilike(f"%{search_term}%") |
            Material.description.ilike(f"%{search_term}%")
        ).order_by(Material.category, Material.name).all()
        
        if not materials:
            print(f"No materials found matching '{search_term}'.")
            return
        
        print(f"\nSearch results for '{search_term}' ({len(materials)} found):")
        print("=" * 60)
        
        for material in materials:
            print(f"{material.name} ({material.category})")
            print(f"  Density: {material.density} kg/m³")
            if material.description:
                print(f"  Description: {material.description}")
            print()
    
    finally:
        db.close()


def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'seed':
            seed_materials()
        elif command == 'list':
            list_materials()
        elif command == 'search':
            if len(sys.argv) > 2:
                search_term = ' '.join(sys.argv[2:])
                search_materials(search_term)
            else:
                print("Please provide a search term.")
        else:
            print("Unknown command. Available commands: seed, list, search")
    else:
        print("Material Database Management")
        print("=" * 30)
        print("Available commands:")
        print("  python seed_materials.py seed    - Seed the database with materials")
        print("  python seed_materials.py list    - List all materials")
        print("  python seed_materials.py search <term> - Search for materials")


if __name__ == "__main__":
    main()