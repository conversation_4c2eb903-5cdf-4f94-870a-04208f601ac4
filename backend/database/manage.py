"""
Database management utilities.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connection import create_tables, drop_tables, SessionLocal
from database.seed_data import seed_database


def create_all_tables():
    """Create all database tables."""
    print("Creating all database tables...")
    create_tables()
    print("Tables created successfully.")


def drop_all_tables():
    """Drop all database tables."""
    print("Dropping all database tables...")
    drop_tables()
    print("Tables dropped successfully.")


def seed_all_data():
    """Seed database with default data."""
    db = SessionLocal()
    try:
        seed_database(db)
    finally:
        db.close()


def reset_database():
    """Reset database by dropping, creating, and seeding."""
    print("Resetting database...")
    drop_all_tables()
    create_all_tables()
    seed_all_data()
    print("Database reset completed.")


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python manage.py [create|drop|seed|reset]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "create":
        create_all_tables()
    elif command == "drop":
        drop_all_tables()
    elif command == "seed":
        seed_all_data()
    elif command == "reset":
        reset_database()
    else:
        print("Invalid command. Use: create, drop, seed, or reset")
        sys.exit(1)