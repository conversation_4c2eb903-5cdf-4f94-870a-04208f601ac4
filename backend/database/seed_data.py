"""
Database seeding scripts with default materials and fabrication stages.
"""
from sqlalchemy.orm import Session
from models.material import Material
from models.fabrication import FabricationStage


def seed_materials(db: Session) -> None:
    """Seed the database with comprehensive metal fabrication materials."""
    
    # Check if materials already exist
    if db.query(Material).count() > 0:
        print("Materials already exist, skipping seeding.")
        return
    
    materials_data = [
        # Steel grades
        ("Carbon Steel A36", 7850.0, "Steel", "Structural steel for construction and fabrication"),
        ("Carbon Steel 1018", 7870.0, "Steel", "Low carbon steel for machining and welding"),
        ("Carbon Steel 1045", 7870.0, "Steel", "Medium carbon steel for shafts and gears"),
        ("Stainless Steel 304", 8000.0, "Steel", "Austenitic stainless steel, corrosion resistant"),
        ("Stainless Steel 316", 8000.0, "Steel", "Marine grade stainless steel"),
        ("Stainless Steel 410", 7750.0, "Steel", "Martensitic stainless steel, hardenable"),
        ("Tool Steel O1", 7850.0, "Steel", "Oil hardening tool steel"),
        ("Tool Steel A2", 7860.0, "Steel", "Air hardening tool steel"),
        ("Alloy Steel 4140", 7850.0, "Steel", "Chromium-molybdenum alloy steel"),
        ("Alloy Steel 4340", 7850.0, "Steel", "Nickel-chromium-molybdenum alloy steel"),
        
        # Iron grades
        ("Cast Iron Gray", 7200.0, "Iron", "Gray cast iron for engine blocks and housings"),
        ("Cast Iron Ductile", 7100.0, "Iron", "Ductile iron with improved toughness"),
        ("Wrought Iron", 7700.0, "Iron", "Traditional wrought iron for decorative work"),
        
        # Aluminum grades
        ("Aluminum 1100", 2710.0, "Aluminum", "Pure aluminum, excellent corrosion resistance"),
        ("Aluminum 2024", 2780.0, "Aluminum", "High strength aluminum for aerospace"),
        ("Aluminum 3003", 2730.0, "Aluminum", "General purpose aluminum with manganese"),
        ("Aluminum 5052", 2680.0, "Aluminum", "Marine grade aluminum with magnesium"),
        ("Aluminum 6061", 2700.0, "Aluminum", "Structural aluminum, heat treatable"),
        ("Aluminum 6063", 2700.0, "Aluminum", "Architectural aluminum for extrusions"),
        ("Aluminum 7075", 2810.0, "Aluminum", "High strength aluminum for aircraft"),
        
        # Other common fabrication metals
        ("Brass 360", 8500.0, "Brass", "Free machining brass"),
        ("Brass 260", 8530.0, "Brass", "Cartridge brass, 70% copper"),
        ("Bronze 932", 8800.0, "Bronze", "Bearing bronze with tin and lead"),
        ("Copper 110", 8960.0, "Copper", "Electrolytic tough pitch copper"),
        ("Titanium Grade 2", 4510.0, "Titanium", "Commercially pure titanium"),
        ("Inconel 625", 8440.0, "Superalloy", "Nickel-chromium superalloy"),
        
        # Plastics for comparison
        ("ABS Plastic", 1050.0, "Plastic", "General purpose thermoplastic"),
        ("PLA Plastic", 1240.0, "Plastic", "3D printing filament"),
        ("HDPE", 950.0, "Plastic", "High density polyethylene"),
        ("Nylon 6", 1140.0, "Plastic", "Engineering thermoplastic"),
    ]
    
    materials = []
    for name, density, category, description in materials_data:
        material = Material(
            name=name,
            density=density,
            category=category,
            description=description
        )
        materials.append(material)
    
    db.add_all(materials)
    db.commit()
    print(f"Seeded {len(materials)} materials into the database.")


def seed_fabrication_stages(db: Session) -> None:
    """Seed the database with default fabrication stages."""
    
    # Check if fabrication stages already exist
    if db.query(FabricationStage).count() > 0:
        print("Fabrication stages already exist, skipping seeding.")
        return
    
    stages_data = [
        ("Not Started", "Part has not begun fabrication", 1, True),
        ("Material Prep", "Material cutting and preparation", 2, True),
        ("Machining", "CNC machining or manual machining operations", 3, True),
        ("Welding/Assembly", "Welding and assembly operations", 4, True),
        ("Quality Check", "Quality inspection and testing", 5, True),
        ("Finishing", "Surface treatment, painting, or coating", 6, True),
        ("Completed", "Part fabrication completed", 7, True),
    ]
    
    stages = []
    for name, description, order_index, is_default in stages_data:
        stage = FabricationStage(
            name=name,
            description=description,
            order_index=order_index,
            is_default=is_default
        )
        stages.append(stage)
    
    db.add_all(stages)
    db.commit()
    print(f"Seeded {len(stages)} fabrication stages into the database.")


def seed_database(db: Session) -> None:
    """Seed the database with all default data."""
    print("Starting database seeding...")
    seed_materials(db)
    seed_fabrication_stages(db)
    print("Database seeding completed.")


if __name__ == "__main__":
    from database.connection import SessionLocal
    
    db = SessionLocal()
    try:
        seed_database(db)
    finally:
        db.close()