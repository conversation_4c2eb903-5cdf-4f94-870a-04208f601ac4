# Database Setup

This directory contains the database models, migrations, and utilities for the Automated 3D BOM Generator.

## Structure

- `connection.py` - Database connection utilities and configuration
- `seed_data.py` - Database seeding scripts with default materials and fabrication stages
- `manage.py` - Database management utilities
- `README.md` - This file

## Models

The application uses the following SQLAlchemy models:

### User Management
- `User` - User accounts and authentication
- `UserSession` - JWT session management

### Design and Analysis
- `Design` - Design projects and file metadata
- `AnalysisResult` - Computer vision and OCR analysis results
- `BOMItem` - Bill of Materials items
- `Model3D` - 3D model files and OpenSCAD scripts

### Sharing and Collaboration
- `DesignShare` - Design sharing permissions

### Materials and Fabrication
- `Material` - Material properties database with comprehensive metal grades
- `FabricationStage` - Customizable fabrication workflow stages
- `FabricationTeam` - Team management for fabrication assignments
- `TeamMember` - Team member assignments
- `PartFabricationProgress` - Individual part fabrication tracking
- `FabricationStageHistory` - Historical progress tracking

## Database Management

### Using manage.py

```bash
# Create all tables
python database/manage.py create

# Drop all tables
python database/manage.py drop

# Seed database with default data
python database/manage.py seed

# Reset database (drop, create, seed)
python database/manage.py reset
```

### Using Alembic Migrations

```bash
# Create a new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Downgrade migrations
alembic downgrade -1
```

## Default Data

The seeding script includes:

### Materials (30 types)
- **Steel grades**: A36, 1018, 1045, 304 SS, 316 SS, 410 SS, O1, A2, 4140, 4340
- **Iron grades**: Gray cast iron, ductile iron, wrought iron
- **Aluminum grades**: 1100, 2024, 3003, 5052, 6061, 6063, 7075
- **Other metals**: Brass 360/260, Bronze 932, Copper 110, Titanium Grade 2, Inconel 625
- **Plastics**: ABS, PLA, HDPE, Nylon 6

### Fabrication Stages (7 default stages)
1. Not Started
2. Material Prep
3. Machining
4. Welding/Assembly
5. Quality Check
6. Finishing
7. Completed

## Environment Variables

Required environment variables (see `.env.example`):

```
DATABASE_URL=postgresql://user:password@localhost:5432/database_name
```

## Testing

Run the test script to verify setup:

```bash
python test_setup.py
```

This will test:
- Model imports and definitions
- Table creation (if database is available)
- Data seeding
- Model relationships