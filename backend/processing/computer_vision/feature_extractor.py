"""
Computer Vision Feature Extraction Module

This module handles geometric feature extraction from engineering drawings
using OpenCV for contour detection, line detection, and shape analysis.
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import time
import logging

logger = logging.getLogger(__name__)


class FeatureType(Enum):
    LINE = "line"
    CIRCLE = "circle"
    ARC = "arc"
    RECTANGLE = "rectangle"
    POLYGON = "polygon"
    DIMENSION_LINE = "dimension_line"
    CENTER_LINE = "center_line"


@dataclass
class GeometricFeature:
    feature_type: FeatureType
    coordinates: List[Tuple[float, float]]
    confidence: float
    properties: Dict[str, Any]
    bbox: Tuple[int, int, int, int]


@dataclass
class DrawingSection:
    section_type: str  # 'main_view', 'title_block', 'parts_list', 'notes'
    bbox: Tuple[int, int, int, int]
    confidence: float
    features: List[GeometricFeature]


@dataclass
class CVResult:
    sections: List[DrawingSection]
    features: List[GeometricFeature]
    overall_confidence: float
    processing_time: float
    errors: List[str]
    warnings: List[str]
    image_quality_score: float


class ImagePreprocessor:
    """Handles image preprocessing for better feature extraction"""
    
    def __init__(self):
        pass
    
    def enhance_image(self, image: np.ndarray) -> np.ndarray:
        """
        Enhance image quality for better feature extraction
        
        Args:
            image: Input image
            
        Returns:
            Enhanced image
        """
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # Denoise
            denoised = cv2.fastNlMeansDenoising(enhanced)
            
            # Sharpen
            kernel = np.array([[-1, -1, -1],
                             [-1,  9, -1],
                             [-1, -1, -1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)
            
            return sharpened
            
        except Exception as e:
            logger.warning(f"Image enhancement failed: {str(e)}")
            return image if len(image.shape) == 2 else cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    def assess_image_quality(self, image: np.ndarray) -> float:
        """
        Assess image quality for processing
        
        Args:
            image: Input image
            
        Returns:
            Quality score (0-100)
        """
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Calculate Laplacian variance (sharpness)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # Calculate contrast
            contrast = gray.std()
            
            # Calculate brightness distribution
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            brightness_score = 100 - abs(128 - np.argmax(hist)) / 128 * 100
            
            # Combine metrics
            sharpness_score = min(laplacian_var / 100, 100)
            contrast_score = min(contrast / 50 * 100, 100)
            
            overall_score = (sharpness_score * 0.4 + contrast_score * 0.4 + brightness_score * 0.2)
            
            return max(0, min(100, overall_score))
            
        except Exception as e:
            logger.warning(f"Quality assessment failed: {str(e)}")
            return 50.0  # Default medium quality


class LayoutAnalyzer:
    """Analyzes drawing layout to identify different sections"""
    
    def __init__(self):
        pass
    
    def identify_sections(self, image: np.ndarray) -> List[DrawingSection]:
        """
        Identify different sections of the engineering drawing
        
        Args:
            image: Preprocessed image
            
        Returns:
            List of identified sections
        """
        sections = []
        height, width = image.shape[:2]
        
        try:
            # Find contours for section identification
            edges = cv2.Canny(image, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Sort contours by area
            contours = sorted(contours, key=cv2.contourArea, reverse=True)
            
            # Identify title block (usually bottom right)
            title_block_bbox = self._identify_title_block(image, contours)
            if title_block_bbox:
                sections.append(DrawingSection(
                    section_type="title_block",
                    bbox=title_block_bbox,
                    confidence=0.8,
                    features=[]
                ))
            
            # Identify parts list (usually top right or bottom left)
            parts_list_bbox = self._identify_parts_list(image, contours, title_block_bbox)
            if parts_list_bbox:
                sections.append(DrawingSection(
                    section_type="parts_list",
                    bbox=parts_list_bbox,
                    confidence=0.7,
                    features=[]
                ))
            
            # Main drawing area (remaining space)
            main_view_bbox = self._identify_main_view(image, title_block_bbox, parts_list_bbox)
            sections.append(DrawingSection(
                section_type="main_view",
                bbox=main_view_bbox,
                confidence=0.9,
                features=[]
            ))
            
        except Exception as e:
            logger.warning(f"Section identification failed: {str(e)}")
            # Fallback: treat entire image as main view
            sections.append(DrawingSection(
                section_type="main_view",
                bbox=(0, 0, width, height),
                confidence=0.5,
                features=[]
            ))
        
        return sections
    
    def _identify_title_block(self, image: np.ndarray, contours: List) -> Optional[Tuple[int, int, int, int]]:
        """Identify title block location (usually bottom right)"""
        height, width = image.shape[:2]
        
        # Look for rectangular contours in bottom right quadrant
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check if it's in bottom right and has reasonable size
            if (x > width * 0.6 and y > height * 0.7 and 
                w > width * 0.15 and h > height * 0.1 and
                w < width * 0.4 and h < height * 0.3):
                return (x, y, w, h)
        
        return None
    
    def _identify_parts_list(self, image: np.ndarray, contours: List, 
                           title_block_bbox: Optional[Tuple[int, int, int, int]]) -> Optional[Tuple[int, int, int, int]]:
        """Identify parts list location"""
        height, width = image.shape[:2]
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # Avoid title block area
            if title_block_bbox:
                tx, ty, tw, th = title_block_bbox
                if (x < tx + tw and x + w > tx and y < ty + th and y + h > ty):
                    continue
            
            # Look for table-like structures
            if (w > width * 0.2 and h > height * 0.15 and
                w < width * 0.5 and h < height * 0.4):
                return (x, y, w, h)
        
        return None
    
    def _identify_main_view(self, image: np.ndarray, 
                          title_block_bbox: Optional[Tuple[int, int, int, int]],
                          parts_list_bbox: Optional[Tuple[int, int, int, int]]) -> Tuple[int, int, int, int]:
        """Identify main drawing view area"""
        height, width = image.shape[:2]
        
        # Start with full image
        x, y, w, h = 0, 0, width, height
        
        # Exclude title block area
        if title_block_bbox:
            tx, ty, tw, th = title_block_bbox
            if ty > height * 0.7:  # Title block at bottom
                h = ty - 10  # Leave some margin
        
        # Exclude parts list area
        if parts_list_bbox:
            px, py, pw, ph = parts_list_bbox
            if px > width * 0.6:  # Parts list on right
                w = px - 10  # Leave some margin
        
        return (x, y, w, h)


class FeatureExtractor:
    """Extracts geometric features from drawing sections"""
    
    def __init__(self):
        pass
    
    def extract_features(self, image: np.ndarray, section: DrawingSection) -> List[GeometricFeature]:
        """
        Extract geometric features from a drawing section
        
        Args:
            image: Preprocessed image
            section: Drawing section to analyze
            
        Returns:
            List of extracted features
        """
        features = []
        
        try:
            # Extract region of interest
            x, y, w, h = section.bbox
            roi = image[y:y+h, x:x+w]
            
            # Extract different types of features
            lines = self._extract_lines(roi, (x, y))
            circles = self._extract_circles(roi, (x, y))
            rectangles = self._extract_rectangles(roi, (x, y))
            
            features.extend(lines)
            features.extend(circles)
            features.extend(rectangles)
            
        except Exception as e:
            logger.warning(f"Feature extraction failed for section {section.section_type}: {str(e)}")
        
        return features
    
    def _extract_lines(self, roi: np.ndarray, offset: Tuple[int, int]) -> List[GeometricFeature]:
        """Extract line features using Hough transform"""
        features = []
        
        try:
            edges = cv2.Canny(roi, 50, 150)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
                                  minLineLength=30, maxLineGap=10)
            
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    
                    # Adjust coordinates with offset
                    x1 += offset[0]
                    y1 += offset[1]
                    x2 += offset[0]
                    y2 += offset[1]
                    
                    # Calculate properties
                    length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                    angle = np.arctan2(y2-y1, x2-x1) * 180 / np.pi
                    
                    # Determine line type
                    feature_type = FeatureType.LINE
                    if abs(angle) < 5 or abs(angle - 180) < 5:  # Horizontal
                        if self._is_dimension_line(roi, (x1-offset[0], y1-offset[1], x2-offset[0], y2-offset[1])):
                            feature_type = FeatureType.DIMENSION_LINE
                    elif abs(angle - 90) < 5 or abs(angle + 90) < 5:  # Vertical
                        if self._is_dimension_line(roi, (x1-offset[0], y1-offset[1], x2-offset[0], y2-offset[1])):
                            feature_type = FeatureType.DIMENSION_LINE
                    
                    features.append(GeometricFeature(
                        feature_type=feature_type,
                        coordinates=[(x1, y1), (x2, y2)],
                        confidence=0.8,
                        properties={
                            'length': length,
                            'angle': angle,
                            'is_horizontal': abs(angle) < 5 or abs(angle - 180) < 5,
                            'is_vertical': abs(angle - 90) < 5 or abs(angle + 90) < 5
                        },
                        bbox=(min(x1, x2), min(y1, y2), abs(x2-x1), abs(y2-y1))
                    ))
                    
        except Exception as e:
            logger.warning(f"Line extraction failed: {str(e)}")
        
        return features
    
    def _extract_circles(self, roi: np.ndarray, offset: Tuple[int, int]) -> List[GeometricFeature]:
        """Extract circular features using Hough circle transform"""
        features = []
        
        try:
            circles = cv2.HoughCircles(roi, cv2.HOUGH_GRADIENT, dp=1, minDist=30,
                                     param1=50, param2=30, minRadius=5, maxRadius=100)
            
            if circles is not None:
                circles = np.round(circles[0, :]).astype("int")
                
                for (x, y, r) in circles:
                    # Adjust coordinates with offset
                    x += offset[0]
                    y += offset[1]
                    
                    features.append(GeometricFeature(
                        feature_type=FeatureType.CIRCLE,
                        coordinates=[(x, y)],
                        confidence=0.7,
                        properties={
                            'radius': r,
                            'diameter': 2 * r,
                            'area': np.pi * r * r
                        },
                        bbox=(x-r, y-r, 2*r, 2*r)
                    ))
                    
        except Exception as e:
            logger.warning(f"Circle extraction failed: {str(e)}")
        
        return features
    
    def _extract_rectangles(self, roi: np.ndarray, offset: Tuple[int, int]) -> List[GeometricFeature]:
        """Extract rectangular features"""
        features = []
        
        try:
            edges = cv2.Canny(roi, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # Approximate contour to polygon
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # Check if it's a rectangle (4 vertices)
                if len(approx) == 4:
                    x, y, w, h = cv2.boundingRect(approx)
                    
                    # Adjust coordinates with offset
                    x += offset[0]
                    y += offset[1]
                    
                    # Calculate corner points
                    corners = [(x, y), (x+w, y), (x+w, y+h), (x, y+h)]
                    
                    features.append(GeometricFeature(
                        feature_type=FeatureType.RECTANGLE,
                        coordinates=corners,
                        confidence=0.6,
                        properties={
                            'width': w,
                            'height': h,
                            'area': w * h,
                            'aspect_ratio': w / h if h > 0 else 0
                        },
                        bbox=(x, y, w, h)
                    ))
                    
        except Exception as e:
            logger.warning(f"Rectangle extraction failed: {str(e)}")
        
        return features
    
    def _is_dimension_line(self, roi: np.ndarray, line_coords: Tuple[int, int, int, int]) -> bool:
        """Check if a line is likely a dimension line"""
        # Simple heuristic: look for nearby text or arrows
        # This is a simplified implementation
        x1, y1, x2, y2 = line_coords
        
        # Check for nearby small features that could be arrows or text
        # In a real implementation, this would be more sophisticated
        return False


class ComputerVisionEngine:
    """Main computer vision engine that coordinates all CV operations"""
    
    def __init__(self):
        self.preprocessor = ImagePreprocessor()
        self.layout_analyzer = LayoutAnalyzer()
        self.feature_extractor = FeatureExtractor()
    
    def analyze_drawing(self, image: np.ndarray) -> CVResult:
        """
        Perform complete computer vision analysis of an engineering drawing
        
        Args:
            image: Input image
            
        Returns:
            Complete CV analysis results
        """
        start_time = time.time()
        errors = []
        warnings = []
        
        try:
            # Assess image quality
            quality_score = self.preprocessor.assess_image_quality(image)
            if quality_score < 30:
                warnings.append("Low image quality detected - results may be unreliable")
            
            # Preprocess image
            enhanced_image = self.preprocessor.enhance_image(image)
            
            # Identify drawing sections
            sections = self.layout_analyzer.identify_sections(enhanced_image)
            
            # Extract features from each section
            all_features = []
            for section in sections:
                features = self.feature_extractor.extract_features(enhanced_image, section)
                section.features = features
                all_features.extend(features)
            
            # Calculate overall confidence
            if all_features:
                overall_confidence = np.mean([f.confidence for f in all_features])
            else:
                overall_confidence = 0.0
                warnings.append("No geometric features detected")
            
            return CVResult(
                sections=sections,
                features=all_features,
                overall_confidence=overall_confidence,
                processing_time=time.time() - start_time,
                errors=errors,
                warnings=warnings,
                image_quality_score=quality_score
            )
            
        except Exception as e:
            error_msg = f"Computer vision analysis failed: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            
            return CVResult(
                sections=[],
                features=[],
                overall_confidence=0.0,
                processing_time=time.time() - start_time,
                errors=errors,
                warnings=warnings,
                image_quality_score=0.0
            )