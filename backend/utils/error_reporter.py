"""
Error reporting and logging system for comprehensive error tracking.
Provides structured logging, error aggregation, and reporting capabilities.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import uuid
from pathlib import Path

from .error_handler import <PERSON>rrorCode, ErrorCategory, ErrorSeverity, ErrorDetails


class ReportType(Enum):
    """Types of error reports"""
    USER_ERROR = "user_error"
    SYSTEM_ERROR = "system_error"
    PERFORMANCE_ISSUE = "performance_issue"
    SECURITY_INCIDENT = "security_incident"


@dataclass
class ErrorReport:
    """Structured error report"""
    id: str
    timestamp: datetime
    report_type: ReportType
    error_code: ErrorCode
    category: ErrorCategory
    severity: ErrorSeverity
    user_id: Optional[str]
    session_id: Optional[str]
    request_id: Optional[str]
    file_id: Optional[str]
    error_message: str
    user_message: str
    stack_trace: Optional[str]
    context: Dict[str, Any]
    user_agent: Optional[str]
    ip_address: Optional[str]
    resolved: bool = False
    resolution_notes: Optional[str] = None


class ErrorReporter:
    """Handles error reporting and logging"""
    
    def __init__(self, log_file_path: str = "logs/error_reports.log"):
        self.log_file_path = Path(log_file_path)
        self.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Set up structured logging
        self.logger = logging.getLogger("error_reporter")
        self.logger.setLevel(logging.INFO)
        
        # File handler for error reports
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setLevel(logging.INFO)
        
        # Simple formatter for structured logging
        formatter = logging.Formatter('%(message)s')
        file_handler.setFormatter(formatter)
        
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
    
    def report_error(
        self,
        error_details: ErrorDetails,
        context: Dict[str, Any] = None,
        user_id: str = None,
        session_id: str = None,
        request_id: str = None,
        file_id: str = None,
        stack_trace: str = None,
        user_agent: str = None,
        ip_address: str = None
    ) -> str:
        """Report an error and return report ID"""
        
        report_id = str(uuid.uuid4())
        
        # Determine report type based on error category
        if error_details.category == ErrorCategory.AUTHENTICATION:
            report_type = ReportType.SECURITY_INCIDENT
        elif error_details.category == ErrorCategory.SYSTEM:
            report_type = ReportType.SYSTEM_ERROR
        else:
            report_type = ReportType.USER_ERROR
        
        error_report = ErrorReport(
            id=report_id,
            timestamp=datetime.utcnow(),
            report_type=report_type,
            error_code=error_details.code,
            category=error_details.category,
            severity=error_details.severity,
            user_id=user_id,
            session_id=session_id,
            request_id=request_id,
            file_id=file_id,
            error_message=error_details.message,
            user_message=error_details.user_message,
            stack_trace=stack_trace,
            context=context or {},
            user_agent=user_agent,
            ip_address=ip_address
        )
        
        # Log the error report
        self._log_error_report(error_report)
        
        # Ensure log is flushed to file
        for handler in self.logger.handlers:
            handler.flush()
        
        return report_id
    
    def _log_error_report(self, error_report: ErrorReport):
        """Log error report in structured format"""
        
        # Convert to dictionary for JSON logging
        report_dict = asdict(error_report)
        
        # Convert datetime to ISO format
        report_dict['timestamp'] = error_report.timestamp.isoformat()
        
        # Convert enums to values
        report_dict['report_type'] = error_report.report_type.value
        report_dict['error_code'] = error_report.error_code.value
        report_dict['category'] = error_report.category.value
        report_dict['severity'] = error_report.severity.value
        
        # Log as JSON
        self.logger.info(json.dumps(report_dict))
    
    def get_error_statistics(self, days: int = 7) -> Dict[str, Any]:
        """Get error statistics for the specified number of days"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        stats = {
            'total_errors': 0,
            'by_category': {},
            'by_severity': {},
            'by_error_code': {},
            'by_day': {},
            'top_errors': [],
            'resolution_rate': 0.0
        }
        
        try:
            with open(self.log_file_path, 'r') as f:
                for line in f:
                    try:
                        # The log line is directly a JSON object (report_data)
                        report_data = json.loads(line.strip())
                        
                        # Parse timestamp
                        timestamp = datetime.fromisoformat(report_data['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp < cutoff_date:
                            continue
                        
                        stats['total_errors'] += 1
                        
                        # Count by category
                        category = report_data.get('category', 'unknown')
                        stats['by_category'][category] = stats['by_category'].get(category, 0) + 1
                        
                        # Count by severity
                        severity = report_data.get('severity', 'unknown')
                        stats['by_severity'][severity] = stats['by_severity'].get(severity, 0) + 1
                        
                        # Count by error code
                        error_code = report_data.get('error_code', 'unknown')
                        stats['by_error_code'][error_code] = stats['by_error_code'].get(error_code, 0) + 1
                        
                        # Count by day
                        day_key = timestamp.strftime('%Y-%m-%d')
                        stats['by_day'][day_key] = stats['by_day'].get(day_key, 0) + 1
                        
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
        
        except FileNotFoundError:
            pass
        
        # Calculate top errors
        stats['top_errors'] = sorted(
            stats['by_error_code'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        return stats
    
    def get_user_error_history(self, user_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get error history for a specific user"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        user_errors = []
        
        try:
            with open(self.log_file_path, 'r') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line)
                        
                        # Parse timestamp
                        timestamp = datetime.fromisoformat(log_entry['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp < cutoff_date:
                            continue
                        
                        # Extract error report from log message
                        report_data = json.loads(log_entry['message'])
                        
                        if report_data.get('user_id') == user_id:
                            user_errors.append({
                                'timestamp': timestamp.isoformat(),
                                'error_code': report_data.get('error_code'),
                                'category': report_data.get('category'),
                                'severity': report_data.get('severity'),
                                'user_message': report_data.get('user_message'),
                                'file_id': report_data.get('file_id'),
                                'resolved': report_data.get('resolved', False)
                            })
                    
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
        
        except FileNotFoundError:
            pass
        
        return sorted(user_errors, key=lambda x: x['timestamp'], reverse=True)
    
    def mark_error_resolved(self, report_id: str, resolution_notes: str = None):
        """Mark an error as resolved (this would typically update a database)"""
        # In a real implementation, this would update the error report in a database
        # For now, we'll log the resolution
        resolution_log = {
            'action': 'error_resolved',
            'report_id': report_id,
            'resolution_notes': resolution_notes,
            'resolved_at': datetime.utcnow().isoformat()
        }
        
        self.logger.info(json.dumps(resolution_log))


class ErrorAggregator:
    """Aggregates and analyzes error patterns"""
    
    def __init__(self, error_reporter: ErrorReporter):
        self.error_reporter = error_reporter
    
    def detect_error_patterns(self, days: int = 7) -> Dict[str, Any]:
        """Detect patterns in error occurrences"""
        
        stats = self.error_reporter.get_error_statistics(days)
        patterns = {
            'trending_errors': [],
            'critical_issues': [],
            'user_impact': {},
            'system_health': 'good'
        }
        
        # Identify trending errors (errors that are increasing)
        for error_code, count in stats['top_errors']:
            if count > 10:  # Threshold for trending
                patterns['trending_errors'].append({
                    'error_code': error_code,
                    'count': count,
                    'trend': 'increasing'  # Would calculate actual trend with historical data
                })
        
        # Identify critical issues
        critical_count = stats['by_severity'].get('critical', 0)
        high_count = stats['by_severity'].get('high', 0)
        
        if critical_count > 0:
            patterns['critical_issues'].append(f"{critical_count} critical errors in last {days} days")
            patterns['system_health'] = 'critical'
        elif high_count > 5:
            patterns['critical_issues'].append(f"{high_count} high severity errors in last {days} days")
            patterns['system_health'] = 'warning'
        
        # Calculate user impact
        total_errors = stats['total_errors']
        if total_errors > 100:
            patterns['user_impact']['level'] = 'high'
        elif total_errors > 50:
            patterns['user_impact']['level'] = 'medium'
        else:
            patterns['user_impact']['level'] = 'low'
        
        patterns['user_impact']['total_errors'] = total_errors
        
        return patterns
    
    def generate_error_report(self, days: int = 7) -> Dict[str, Any]:
        """Generate comprehensive error report"""
        
        stats = self.error_reporter.get_error_statistics(days)
        patterns = self.detect_error_patterns(days)
        
        report = {
            'period': f"Last {days} days",
            'generated_at': datetime.utcnow().isoformat(),
            'summary': {
                'total_errors': stats['total_errors'],
                'system_health': patterns['system_health'],
                'user_impact_level': patterns['user_impact']['level']
            },
            'statistics': stats,
            'patterns': patterns,
            'recommendations': self._generate_recommendations(stats, patterns)
        }
        
        return report
    
    def _generate_recommendations(self, stats: Dict[str, Any], patterns: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on error analysis"""
        
        recommendations = []
        
        # Check for high error rates
        if stats['total_errors'] > 100:
            recommendations.append("High error rate detected - investigate system stability")
        
        # Check for specific error patterns
        top_error = stats['top_errors'][0] if stats['top_errors'] else None
        if top_error and top_error[1] > 20:
            recommendations.append(f"Address recurring {top_error[0]} errors - {top_error[1]} occurrences")
        
        # Check for critical issues
        if patterns['system_health'] == 'critical':
            recommendations.append("Immediate attention required for critical system errors")
        
        # Check for user experience issues
        processing_errors = stats['by_category'].get('processing', 0)
        if processing_errors > stats['total_errors'] * 0.5:
            recommendations.append("High processing error rate - review analysis pipeline")
        
        return recommendations


# Global error reporter instance
error_reporter = ErrorReporter()