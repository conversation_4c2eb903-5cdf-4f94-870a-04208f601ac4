"""
Confidence assessment system for analysis results.
Provides confidence scoring and quality metrics for uncertain results.
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np


class ConfidenceLevel(Enum):
    """Confidence levels for analysis results"""
    VERY_HIGH = "very_high"  # 90-100%
    HIGH = "high"           # 75-89%
    MEDIUM = "medium"       # 50-74%
    LOW = "low"            # 25-49%
    VERY_LOW = "very_low"   # 0-24%


class AnalysisComponent(Enum):
    """Different components of the analysis pipeline"""
    IMAGE_QUALITY = "image_quality"
    FEATURE_DETECTION = "feature_detection"
    OCR_TEXT = "ocr_text"
    DIMENSION_EXTRACTION = "dimension_extraction"
    BOM_EXTRACTION = "bom_extraction"
    MATERIAL_IDENTIFICATION = "material_identification"
    WEIGHT_CALCULATION = "weight_calculation"
    MODEL_3D_GENERATION = "model_3d_generation"


@dataclass
class ConfidenceMetric:
    """Individual confidence metric"""
    component: AnalysisComponent
    score: float  # 0.0 to 1.0
    level: ConfidenceLevel
    details: Dict[str, Any]
    issues: List[str]
    suggestions: List[str]


@dataclass
class OverallConfidence:
    """Overall confidence assessment"""
    overall_score: float
    overall_level: ConfidenceLevel
    component_scores: Dict[AnalysisComponent, ConfidenceMetric]
    critical_issues: List[str]
    warnings: List[str]
    recommendations: List[str]


class ConfidenceAssessor:
    """Assesses confidence levels for analysis results"""
    
    # Confidence thresholds
    CONFIDENCE_THRESHOLDS = {
        ConfidenceLevel.VERY_HIGH: 0.90,
        ConfidenceLevel.HIGH: 0.75,
        ConfidenceLevel.MEDIUM: 0.50,
        ConfidenceLevel.LOW: 0.25,
        ConfidenceLevel.VERY_LOW: 0.0
    }
    
    # Component weights for overall score calculation
    COMPONENT_WEIGHTS = {
        AnalysisComponent.IMAGE_QUALITY: 0.15,
        AnalysisComponent.FEATURE_DETECTION: 0.20,
        AnalysisComponent.OCR_TEXT: 0.15,
        AnalysisComponent.DIMENSION_EXTRACTION: 0.15,
        AnalysisComponent.BOM_EXTRACTION: 0.15,
        AnalysisComponent.MATERIAL_IDENTIFICATION: 0.10,
        AnalysisComponent.WEIGHT_CALCULATION: 0.05,
        AnalysisComponent.MODEL_3D_GENERATION: 0.05
    }
    
    @classmethod
    def get_confidence_level(cls, score: float) -> ConfidenceLevel:
        """Convert numeric score to confidence level"""
        for level, threshold in cls.CONFIDENCE_THRESHOLDS.items():
            if score >= threshold:
                return level
        return ConfidenceLevel.VERY_LOW
    
    @classmethod
    def assess_image_quality(cls, image_metrics: Dict[str, Any]) -> ConfidenceMetric:
        """Assess image quality confidence"""
        score = 1.0
        issues = []
        suggestions = []
        details = {}
        
        # Check resolution
        if 'resolution' in image_metrics:
            resolution = image_metrics['resolution']
            details['resolution'] = resolution
            if resolution < 150:
                score *= 0.3
                issues.append("Very low resolution (< 150 DPI)")
                suggestions.append("Upload higher resolution image (minimum 300 DPI)")
            elif resolution < 300:
                score *= 0.7
                issues.append("Low resolution (< 300 DPI)")
                suggestions.append("Higher resolution would improve accuracy")
        
        # Check contrast
        if 'contrast' in image_metrics:
            contrast = image_metrics['contrast']
            details['contrast'] = contrast
            if contrast < 0.3:
                score *= 0.5
                issues.append("Poor contrast between lines and background")
                suggestions.append("Improve image contrast or scan quality")
        
        # Check noise level
        if 'noise_level' in image_metrics:
            noise = image_metrics['noise_level']
            details['noise_level'] = noise
            if noise > 0.2:
                score *= 0.6
                issues.append("High noise level in image")
                suggestions.append("Clean up image or rescan without artifacts")
        
        # Check blur
        if 'blur_metric' in image_metrics:
            blur = image_metrics['blur_metric']
            details['blur_metric'] = blur
            if blur > 0.3:
                score *= 0.4
                issues.append("Image appears blurred or out of focus")
                suggestions.append("Ensure drawing is in focus when scanning")
        
        return ConfidenceMetric(
            component=AnalysisComponent.IMAGE_QUALITY,
            score=max(0.0, min(1.0, score)),
            level=cls.get_confidence_level(score),
            details=details,
            issues=issues,
            suggestions=suggestions
        )
    
    @classmethod
    def assess_feature_detection(cls, detection_results: Dict[str, Any]) -> ConfidenceMetric:
        """Assess feature detection confidence"""
        score = 1.0
        issues = []
        suggestions = []
        details = {}
        
        # Check number of detected features
        if 'detected_lines' in detection_results:
            line_count = detection_results['detected_lines']
            details['detected_lines'] = line_count
            if line_count < 10:
                score *= 0.4
                issues.append("Very few lines detected")
                suggestions.append("Ensure drawing has clear, continuous lines")
            elif line_count < 50:
                score *= 0.7
                issues.append("Limited line detection")
        
        # Check circle detection
        if 'detected_circles' in detection_results:
            circle_count = detection_results['detected_circles']
            details['detected_circles'] = circle_count
        
        # Check detection confidence scores
        if 'average_confidence' in detection_results:
            avg_conf = detection_results['average_confidence']
            details['average_confidence'] = avg_conf
            score *= avg_conf
        
        # Check for geometric consistency
        if 'geometric_consistency' in detection_results:
            consistency = detection_results['geometric_consistency']
            details['geometric_consistency'] = consistency
            if consistency < 0.5:
                score *= 0.6
                issues.append("Inconsistent geometric features detected")
                suggestions.append("Check drawing for proper alignment and scaling")
        
        return ConfidenceMetric(
            component=AnalysisComponent.FEATURE_DETECTION,
            score=max(0.0, min(1.0, score)),
            level=cls.get_confidence_level(score),
            details=details,
            issues=issues,
            suggestions=suggestions
        )
    
    @classmethod
    def assess_ocr_results(cls, ocr_results: Dict[str, Any]) -> ConfidenceMetric:
        """Assess OCR confidence"""
        score = 1.0
        issues = []
        suggestions = []
        details = {}
        
        # Check OCR confidence scores
        if 'text_confidence' in ocr_results:
            text_conf = ocr_results['text_confidence']
            details['text_confidence'] = text_conf
            score *= text_conf
            
            if text_conf < 0.5:
                issues.append("Low OCR confidence for text recognition")
                suggestions.append("Improve text clarity or use manual input")
        
        # Check dimension recognition
        if 'dimensions_found' in ocr_results:
            dim_count = ocr_results['dimensions_found']
            details['dimensions_found'] = dim_count
            if dim_count == 0:
                score *= 0.3
                issues.append("No dimensions detected")
                suggestions.append("Ensure dimensions are clearly visible")
        
        # Check part number recognition
        if 'part_numbers_found' in ocr_results:
            part_count = ocr_results['part_numbers_found']
            details['part_numbers_found'] = part_count
            if part_count == 0:
                score *= 0.5
                issues.append("No part numbers detected")
                suggestions.append("Verify part numbers are readable")
        
        return ConfidenceMetric(
            component=AnalysisComponent.OCR_TEXT,
            score=max(0.0, min(1.0, score)),
            level=cls.get_confidence_level(score),
            details=details,
            issues=issues,
            suggestions=suggestions
        )
    
    @classmethod
    def assess_bom_extraction(cls, bom_results: Dict[str, Any]) -> ConfidenceMetric:
        """Assess BOM extraction confidence"""
        score = 1.0
        issues = []
        suggestions = []
        details = {}
        
        # Check completeness of BOM data
        if 'parts_extracted' in bom_results:
            parts_count = bom_results['parts_extracted']
            details['parts_extracted'] = parts_count
            if parts_count == 0:
                score = 0.0
                issues.append("No parts extracted from drawing")
                suggestions.append("Verify drawing contains a parts list or BOM")
        
        # Check for missing information
        if 'missing_materials' in bom_results:
            missing_materials = bom_results['missing_materials']
            details['missing_materials'] = missing_materials
            if missing_materials > 0:
                score *= (1.0 - missing_materials / max(1, bom_results.get('parts_extracted', 1)))
                issues.append(f"{missing_materials} parts missing material information")
                suggestions.append("Add material specifications manually")
        
        # Check for missing quantities
        if 'missing_quantities' in bom_results:
            missing_qty = bom_results['missing_quantities']
            details['missing_quantities'] = missing_qty
            if missing_qty > 0:
                score *= 0.8
                issues.append(f"{missing_qty} parts missing quantity information")
                suggestions.append("Verify quantities in parts list")
        
        return ConfidenceMetric(
            component=AnalysisComponent.BOM_EXTRACTION,
            score=max(0.0, min(1.0, score)),
            level=cls.get_confidence_level(score),
            details=details,
            issues=issues,
            suggestions=suggestions
        )
    
    @classmethod
    def assess_weight_calculation(cls, weight_results: Dict[str, Any]) -> ConfidenceMetric:
        """Assess weight calculation confidence"""
        score = 1.0
        issues = []
        suggestions = []
        details = {}
        
        # Check for volume calculation accuracy
        if 'volume_confidence' in weight_results:
            vol_conf = weight_results['volume_confidence']
            details['volume_confidence'] = vol_conf
            score *= vol_conf
            
            if vol_conf < 0.6:
                issues.append("Low confidence in volume calculations")
                suggestions.append("Verify dimensional accuracy in drawing")
        
        # Check for material density availability
        if 'unknown_materials' in weight_results:
            unknown_count = weight_results['unknown_materials']
            details['unknown_materials'] = unknown_count
            if unknown_count > 0:
                score *= 0.7
                issues.append(f"{unknown_count} parts using default material density")
                suggestions.append("Specify exact materials for accurate weights")
        
        return ConfidenceMetric(
            component=AnalysisComponent.WEIGHT_CALCULATION,
            score=max(0.0, min(1.0, score)),
            level=cls.get_confidence_level(score),
            details=details,
            issues=issues,
            suggestions=suggestions
        )
    
    @classmethod
    def assess_3d_generation(cls, model_results: Dict[str, Any]) -> ConfidenceMetric:
        """Assess 3D model generation confidence"""
        score = 1.0
        issues = []
        suggestions = []
        details = {}
        
        # Check if 3D model was generated
        if 'model_generated' in model_results:
            if not model_results['model_generated']:
                score = 0.0
                issues.append("3D model generation failed")
                suggestions.append("Ensure drawing has sufficient dimensional information")
                return ConfidenceMetric(
                    component=AnalysisComponent.MODEL_3D_GENERATION,
                    score=score,
                    level=cls.get_confidence_level(score),
                    details=details,
                    issues=issues,
                    suggestions=suggestions
                )
        
        # Check geometric complexity handling
        if 'complex_features_simplified' in model_results:
            simplified = model_results['complex_features_simplified']
            details['complex_features_simplified'] = simplified
            if simplified > 0:
                score *= 0.8
                issues.append(f"{simplified} complex features were simplified")
                suggestions.append("Review 3D model for accuracy of complex features")
        
        # Check dimensional accuracy
        if 'dimensional_accuracy' in model_results:
            accuracy = model_results['dimensional_accuracy']
            details['dimensional_accuracy'] = accuracy
            score *= accuracy
            
            if accuracy < 0.8:
                issues.append("Low dimensional accuracy in 3D model")
                suggestions.append("Verify dimensions in original drawing")
        
        return ConfidenceMetric(
            component=AnalysisComponent.MODEL_3D_GENERATION,
            score=max(0.0, min(1.0, score)),
            level=cls.get_confidence_level(score),
            details=details,
            issues=issues,
            suggestions=suggestions
        )
    
    @classmethod
    def calculate_overall_confidence(cls, component_metrics: Dict[AnalysisComponent, ConfidenceMetric]) -> OverallConfidence:
        """Calculate overall confidence from component metrics"""
        
        # Calculate weighted average score
        total_weight = 0.0
        weighted_score = 0.0
        
        for component, metric in component_metrics.items():
            weight = cls.COMPONENT_WEIGHTS.get(component, 0.0)
            weighted_score += metric.score * weight
            total_weight += weight
        
        overall_score = weighted_score / max(total_weight, 1.0)
        overall_level = cls.get_confidence_level(overall_score)
        
        # Collect critical issues and warnings
        critical_issues = []
        warnings = []
        recommendations = []
        
        for metric in component_metrics.values():
            if metric.level in [ConfidenceLevel.VERY_LOW, ConfidenceLevel.LOW]:
                critical_issues.extend(metric.issues)
            elif metric.level == ConfidenceLevel.MEDIUM:
                warnings.extend(metric.issues)
            
            recommendations.extend(metric.suggestions)
        
        # Remove duplicates while preserving order
        critical_issues = list(dict.fromkeys(critical_issues))
        warnings = list(dict.fromkeys(warnings))
        recommendations = list(dict.fromkeys(recommendations))
        
        return OverallConfidence(
            overall_score=overall_score,
            overall_level=overall_level,
            component_scores=component_metrics,
            critical_issues=critical_issues,
            warnings=warnings,
            recommendations=recommendations
        )
    
    @classmethod
    def assess_analysis_results(cls, analysis_data: Dict[str, Any]) -> OverallConfidence:
        """Perform complete confidence assessment of analysis results"""
        
        component_metrics = {}
        
        # Assess each component if data is available
        if 'image_metrics' in analysis_data:
            component_metrics[AnalysisComponent.IMAGE_QUALITY] = cls.assess_image_quality(
                analysis_data['image_metrics']
            )
        
        if 'feature_detection' in analysis_data:
            component_metrics[AnalysisComponent.FEATURE_DETECTION] = cls.assess_feature_detection(
                analysis_data['feature_detection']
            )
        
        if 'ocr_results' in analysis_data:
            component_metrics[AnalysisComponent.OCR_TEXT] = cls.assess_ocr_results(
                analysis_data['ocr_results']
            )
        
        if 'bom_results' in analysis_data:
            component_metrics[AnalysisComponent.BOM_EXTRACTION] = cls.assess_bom_extraction(
                analysis_data['bom_results']
            )
        
        if 'weight_results' in analysis_data:
            component_metrics[AnalysisComponent.WEIGHT_CALCULATION] = cls.assess_weight_calculation(
                analysis_data['weight_results']
            )
        
        if 'model_3d_results' in analysis_data:
            component_metrics[AnalysisComponent.MODEL_3D_GENERATION] = cls.assess_3d_generation(
                analysis_data['model_3d_results']
            )
        
        return cls.calculate_overall_confidence(component_metrics)