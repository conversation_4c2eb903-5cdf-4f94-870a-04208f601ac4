"""
Comprehensive error handling and classification system for the 3D BOM Generator.
Provides structured error responses with actionable recovery suggestions.
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
import traceback
from dataclasses import dataclass


class ErrorCategory(Enum):
    """Error categories for classification"""
    INPUT_VALIDATION = "input_validation"
    PROCESSING = "processing"
    SYSTEM = "system"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    RESOURCE = "resource"
    EXTERNAL_SERVICE = "external_service"


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCode(Enum):
    """Specific error codes for different failure types"""
    # Input validation errors
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    FILE_CORRUPTED = "FILE_CORRUPTED"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    
    # Processing errors
    IMAGE_QUALITY_POOR = "IMAGE_QUALITY_POOR"
    OCR_FAILED = "OCR_FAILED"
    FEATURE_DETECTION_FAILED = "FEATURE_DETECTION_FAILED"
    BOM_EXTRACTION_FAILED = "BOM_EXTRACTION_FAILED"
    WEIGHT_CALCULATION_FAILED = "WEIGHT_CALCULATION_FAILED"
    MODEL_3D_GENERATION_FAILED = "MODEL_3D_GENERATION_FAILED"
    ANALYSIS_TIMEOUT = "ANALYSIS_TIMEOUT"
    PARTIAL_ANALYSIS_FAILURE = "PARTIAL_ANALYSIS_FAILURE"
    
    # System errors
    DATABASE_ERROR = "DATABASE_ERROR"
    FILE_STORAGE_ERROR = "FILE_STORAGE_ERROR"
    MEMORY_EXCEEDED = "MEMORY_EXCEEDED"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    NETWORK_ERROR = "NETWORK_ERROR"
    
    # Authentication/Authorization errors
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    SESSION_EXPIRED = "SESSION_EXPIRED"
    ACCESS_DENIED = "ACCESS_DENIED"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    
    # Resource errors
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    STORAGE_FULL = "STORAGE_FULL"


@dataclass
class ErrorDetails:
    """Detailed error information"""
    code: ErrorCode
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    user_message: str
    suggestions: List[str]
    technical_details: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None
    failed_sections: Optional[List[str]] = None
    retry_possible: bool = True
    timestamp: Optional[datetime] = None


class ErrorClassifier:
    """Classifies and structures errors with recovery suggestions"""
    
    ERROR_DEFINITIONS = {
        ErrorCode.INVALID_FILE_FORMAT: ErrorDetails(
            code=ErrorCode.INVALID_FILE_FORMAT,
            category=ErrorCategory.INPUT_VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            message="Unsupported file format uploaded",
            user_message="The file format you uploaded is not supported.",
            suggestions=[
                "Please upload a PDF, DXF, PNG, JPG, or TIFF file",
                "Convert your file to a supported format",
                "Ensure the file extension matches the actual file type"
            ]
        ),
        
        ErrorCode.FILE_TOO_LARGE: ErrorDetails(
            code=ErrorCode.FILE_TOO_LARGE,
            category=ErrorCategory.INPUT_VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            message="File size exceeds maximum allowed limit",
            user_message="Your file is too large to process.",
            suggestions=[
                "Reduce the file size to under 50MB",
                "Compress the image or reduce resolution",
                "Split large drawings into smaller sections"
            ]
        ),
        
        ErrorCode.IMAGE_QUALITY_POOR: ErrorDetails(
            code=ErrorCode.IMAGE_QUALITY_POOR,
            category=ErrorCategory.PROCESSING,
            severity=ErrorSeverity.MEDIUM,
            message="Image quality insufficient for reliable analysis",
            user_message="The drawing quality is too poor for accurate analysis.",
            suggestions=[
                "Upload a higher resolution image (minimum 300 DPI recommended)",
                "Ensure the drawing has clear, dark lines on light background",
                "Remove any shadows or distortions from the scan",
                "Try scanning in grayscale mode for better contrast"
            ]
        ),
        
        ErrorCode.OCR_FAILED: ErrorDetails(
            code=ErrorCode.OCR_FAILED,
            category=ErrorCategory.PROCESSING,
            severity=ErrorSeverity.MEDIUM,
            message="Text recognition failed on drawing",
            user_message="Unable to read text and dimensions from the drawing.",
            suggestions=[
                "Ensure text is clearly visible and not blurred",
                "Check that text size is readable (minimum 8pt font)",
                "Verify drawing orientation is correct",
                "Use manual input to add missing text information"
            ]
        ),
        
        ErrorCode.FEATURE_DETECTION_FAILED: ErrorDetails(
            code=ErrorCode.FEATURE_DETECTION_FAILED,
            category=ErrorCategory.PROCESSING,
            severity=ErrorSeverity.HIGH,
            message="Unable to detect geometric features in drawing",
            user_message="Could not identify shapes and features in your drawing.",
            suggestions=[
                "Ensure drawing lines are continuous and well-defined",
                "Check that the drawing follows standard engineering conventions",
                "Remove any background patterns or watermarks",
                "Try uploading a cleaner version of the drawing"
            ]
        ),
        
        ErrorCode.BOM_EXTRACTION_FAILED: ErrorDetails(
            code=ErrorCode.BOM_EXTRACTION_FAILED,
            category=ErrorCategory.PROCESSING,
            severity=ErrorSeverity.MEDIUM,
            message="Failed to extract Bill of Materials from drawing",
            user_message="Unable to generate a complete Bill of Materials.",
            suggestions=[
                "Verify the drawing contains a parts list or BOM table",
                "Ensure part numbers are clearly visible and readable",
                "Check that material specifications are included",
                "Use manual input to complete missing BOM information"
            ]
        ),
        
        ErrorCode.MODEL_3D_GENERATION_FAILED: ErrorDetails(
            code=ErrorCode.MODEL_3D_GENERATION_FAILED,
            category=ErrorCategory.PROCESSING,
            severity=ErrorSeverity.MEDIUM,
            message="3D model generation failed",
            user_message="Could not create a 3D model from your drawing.",
            suggestions=[
                "Ensure the drawing contains sufficient dimensional information",
                "Check that all views are properly aligned and scaled",
                "Verify that geometric features are clearly defined",
                "Try simplifying complex features for better 3D inference"
            ]
        ),
        
        ErrorCode.ANALYSIS_TIMEOUT: ErrorDetails(
            code=ErrorCode.ANALYSIS_TIMEOUT,
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.HIGH,
            message="Analysis processing exceeded time limit",
            user_message="Processing took too long and was stopped.",
            suggestions=[
                "Try uploading a smaller or simpler drawing",
                "Reduce image resolution if very high",
                "Split complex drawings into separate files",
                "Contact support if the issue persists"
            ]
        ),
        
        ErrorCode.PARTIAL_ANALYSIS_FAILURE: ErrorDetails(
            code=ErrorCode.PARTIAL_ANALYSIS_FAILURE,
            category=ErrorCategory.PROCESSING,
            severity=ErrorSeverity.LOW,
            message="Some analysis components failed but partial results available",
            user_message="Analysis completed with some limitations.",
            suggestions=[
                "Review the results and manually add missing information",
                "Check confidence scores for reliability",
                "Try reprocessing with a higher quality image",
                "Use the manual correction tools to complete the analysis"
            ]
        ),
        
        ErrorCode.DATABASE_ERROR: ErrorDetails(
            code=ErrorCode.DATABASE_ERROR,
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.CRITICAL,
            message="Database operation failed",
            user_message="A system error occurred while saving your data.",
            suggestions=[
                "Please try again in a few moments",
                "Contact support if the problem continues",
                "Your work may not have been saved"
            ],
            retry_possible=True
        ),
        
        ErrorCode.SESSION_EXPIRED: ErrorDetails(
            code=ErrorCode.SESSION_EXPIRED,
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.MEDIUM,
            message="User session has expired",
            user_message="Your session has expired. Please log in again.",
            suggestions=[
                "Click here to log in again",
                "Your work has been saved automatically",
                "Enable 'Remember Me' to stay logged in longer"
            ]
        ),
        
        ErrorCode.ACCESS_DENIED: ErrorDetails(
            code=ErrorCode.ACCESS_DENIED,
            category=ErrorCategory.AUTHORIZATION,
            severity=ErrorSeverity.MEDIUM,
            message="User does not have permission to access resource",
            user_message="You don't have permission to access this resource.",
            suggestions=[
                "Contact the owner to request access",
                "Verify you're logged into the correct account",
                "Check if the resource has been shared with you"
            ]
        ),
        
        ErrorCode.QUOTA_EXCEEDED: ErrorDetails(
            code=ErrorCode.QUOTA_EXCEEDED,
            category=ErrorCategory.RESOURCE,
            severity=ErrorSeverity.MEDIUM,
            message="User has exceeded their usage quota",
            user_message="You've reached your usage limit for this period.",
            suggestions=[
                "Wait until your quota resets",
                "Delete old designs to free up space",
                "Contact support about upgrading your account"
            ]
        ),
        
        ErrorCode.SERVICE_UNAVAILABLE: ErrorDetails(
            code=ErrorCode.SERVICE_UNAVAILABLE,
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.HIGH,
            message="Service is temporarily unavailable",
            user_message="The service is currently unavailable. Please try again later.",
            suggestions=[
                "Please try again in a few moments",
                "Check your internet connection",
                "Contact support if the problem persists"
            ]
        ),
        
        ErrorCode.FILE_STORAGE_ERROR: ErrorDetails(
            code=ErrorCode.FILE_STORAGE_ERROR,
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.HIGH,
            message="File storage operation failed",
            user_message="Unable to save or retrieve your file.",
            suggestions=[
                "Please try uploading again",
                "Check that you have sufficient storage space",
                "Contact support if the problem continues"
            ]
        )
    }
    
    @classmethod
    def classify_error(cls, error_code: ErrorCode, **kwargs) -> ErrorDetails:
        """Classify an error and return structured error details"""
        if error_code not in cls.ERROR_DEFINITIONS:
            # Default error for unknown codes
            return ErrorDetails(
                code=error_code,
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.HIGH,
                message="An unexpected error occurred",
                user_message="Something went wrong. Please try again.",
                suggestions=["Please try again", "Contact support if the problem persists"]
            )
        
        error_details = cls.ERROR_DEFINITIONS[error_code]
        
        # Update with any provided kwargs
        if 'confidence_score' in kwargs:
            error_details.confidence_score = kwargs['confidence_score']
        if 'failed_sections' in kwargs:
            error_details.failed_sections = kwargs['failed_sections']
        if 'technical_details' in kwargs:
            error_details.technical_details = kwargs['technical_details']
        
        error_details.timestamp = datetime.utcnow()
        
        return error_details


class ErrorHandler:
    """Main error handler for the application"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def handle_error(self, error: Exception, error_code: ErrorCode = None, **kwargs) -> Dict[str, Any]:
        """Handle an error and return structured response"""
        
        # Determine error code if not provided
        if error_code is None:
            error_code = self._infer_error_code(error)
        
        # Classify the error
        error_details = ErrorClassifier.classify_error(error_code, **kwargs)
        
        # Log the error
        self._log_error(error, error_details)
        
        # Return structured error response
        return self._format_error_response(error_details)
    
    def _infer_error_code(self, error: Exception) -> ErrorCode:
        """Infer error code from exception type"""
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        if "filenotfound" in error_type.lower() or "ioerror" in error_type.lower():
            return ErrorCode.FILE_STORAGE_ERROR
        elif "database" in error_message or "sql" in error_message:
            return ErrorCode.DATABASE_ERROR
        elif "timeout" in error_message:
            return ErrorCode.ANALYSIS_TIMEOUT
        elif "memory" in error_message:
            return ErrorCode.MEMORY_EXCEEDED
        elif "permission" in error_message or "access" in error_message:
            return ErrorCode.ACCESS_DENIED
        else:
            return ErrorCode.SERVICE_UNAVAILABLE
    
    def _log_error(self, error: Exception, error_details: ErrorDetails):
        """Log error with appropriate level"""
        log_data = {
            'error_code': error_details.code.value,
            'category': error_details.category.value,
            'severity': error_details.severity.value,
            'error_message': error_details.message,
            'timestamp': error_details.timestamp.isoformat() if error_details.timestamp else None,
            'traceback': traceback.format_exc() if error else None
        }
        
        if error_details.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"Critical error: {error_details.message}")
        elif error_details.severity == ErrorSeverity.HIGH:
            self.logger.error(f"High severity error: {error_details.message}")
        elif error_details.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"Medium severity error: {error_details.message}")
        else:
            self.logger.info(f"Low severity error: {error_details.message}")
    
    def _format_error_response(self, error_details: ErrorDetails) -> Dict[str, Any]:
        """Format error response for API"""
        response = {
            "error": {
                "code": error_details.code.value,
                "category": error_details.category.value,
                "severity": error_details.severity.value,
                "message": error_details.user_message,
                "suggestions": error_details.suggestions,
                "retry_possible": error_details.retry_possible,
                "timestamp": error_details.timestamp.isoformat() if error_details.timestamp else None
            }
        }
        
        # Add optional fields if present
        if error_details.confidence_score is not None:
            response["error"]["confidence_score"] = error_details.confidence_score
        
        if error_details.failed_sections:
            response["error"]["failed_sections"] = error_details.failed_sections
        
        if error_details.technical_details:
            response["error"]["technical_details"] = error_details.technical_details
        
        return response


# Global error handler instance
error_handler = ErrorHandler()