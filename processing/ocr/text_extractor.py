"""OCR Text Extraction Module"""

import cv2
import numpy as np
import pytesseract
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import time

class TextType(Enum):
    DIMENSION = "dimension"
    PART_NUMBER = "part_number"
    MATERIAL = "material"
    ANNOTATION = "annotation"
    GENERAL = "general"

@dataclass
class ExtractedText:
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]
    text_type: TextType

@dataclass
class OCRResult:
    extracted_texts: List[ExtractedText]
    overall_confidence: float
    processing_time: float
    errors: List[str]
    warnings: List[str]

class PatternMatcher:
    def __init__(self):
        self.part_number_patterns = [r'[A-Z]{2,4}-\d{3,6}']
        self.dimension_patterns = [r'\d+\.?\d*\s*[±]\s*\d+\.?\d*']
        self.material_patterns = [r'STEEL\s+A\d{2,3}']
    
    def classify_text(self, text: str) -> TextType:
        text_upper = text.upper().strip()
        for pattern in self.part_number_patterns:
            if re.search(pattern, text_upper):
                return TextType.PART_NUMBER
        for pattern in self.dimension_patterns:
            if re.search(pattern, text_upper):
                return TextType.DIMENSION
        for pattern in self.material_patterns:
            if re.search(pattern, text_upper):
                return TextType.MATERIAL
        if any(kw in text_upper for kw in ['NOTE', 'SCALE']):
            return TextType.ANNOTATION
        return TextType.GENERAL

class OCRTextExtractor:
    def __init__(self):
        self.pattern_matcher = PatternMatcher()
    
    def extract_text_from_image(self, image: np.ndarray) -> OCRResult:
        start_time = time.time()
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            ocr_data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
            extracted_texts = []
            
            for i in range(len(ocr_data['text'])):
                text = ocr_data['text'][i].strip()
                confidence = float(ocr_data['conf'][i])
                
                if text and confidence > 30:
                    bbox = (ocr_data['left'][i], ocr_data['top'][i], 
                           ocr_data['width'][i], ocr_data['height'][i])
                    text_type = self.pattern_matcher.classify_text(text)
                    
                    extracted_texts.append(ExtractedText(
                        text=text, confidence=confidence, bbox=bbox, text_type=text_type
                    ))
            
            overall_confidence = np.mean([et.confidence for et in extracted_texts]) if extracted_texts else 0.0
            
            return OCRResult(
                extracted_texts=extracted_texts,
                overall_confidence=overall_confidence,
                processing_time=time.time() - start_time,
                errors=[],
                warnings=[]
            )
        except Exception as e:
            return OCRResult(
                extracted_texts=[],
                overall_confidence=0.0,
                processing_time=time.time() - start_time,
                errors=[str(e)],
                warnings=[]
            )
    
    def extract_specific_patterns(self, image: np.ndarray) -> Dict[str, List[str]]:
        result = self.extract_text_from_image(image)
        patterns = {'dimensions': [], 'part_numbers': [], 'materials': [], 'annotations': []}
        
        for et in result.extracted_texts:
            if et.text_type == TextType.DIMENSION:
                patterns['dimensions'].append(et.text)
            elif et.text_type == TextType.PART_NUMBER:
                patterns['part_numbers'].append(et.text)
            elif et.text_type == TextType.MATERIAL:
                patterns['materials'].append(et.text)
            elif et.text_type == TextType.ANNOTATION:
                patterns['annotations'].append(et.text)
        
        return patterns
