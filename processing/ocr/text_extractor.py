"""OCR Text Extraction Module"""

import cv2
import numpy as np
import pytesseract
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import time

class TextType(Enum):
    DIMENSION = "dimension"
    PART_NUMBER = "part_number"
    MATERIAL = "material"
    ANNOTATION = "annotation"
    GENERAL = "general"

@dataclass
class ExtractedText:
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]
    text_type: TextType

@dataclass
class OCRResult:
    extracted_texts: List[ExtractedText]
    overall_confidence: float
    processing_time: float
    errors: List[str]
    warnings: List[str]

class TextPreprocessor:
    """Text preprocessing utilities for OCR results"""

    def __init__(self):
        pass

    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())

        # Fix common OCR errors
        text = text.replace('|', 'I')  # Common OCR mistake
        text = text.replace('0', 'O')  # In some contexts

        return text

    def normalize_dimensions(self, text: str) -> str:
        """Normalize dimension text"""
        # Standardize dimension formats
        text = re.sub(r'(\d+)\.(\d+)\s*[±]\s*(\d+)\.(\d+)', r'\1.\2±\3.\4', text)
        return text

    def extract_numbers(self, text: str) -> List[float]:
        """Extract numeric values from text"""
        numbers = re.findall(r'\d+\.?\d*', text)
        return [float(n) for n in numbers if n]

class TextValidator:
    """Validation utilities for extracted text"""

    def __init__(self):
        pass

    def validate_part_number(self, text: str) -> bool:
        """Validate if text looks like a valid part number"""
        if not text:
            return False

        # Basic part number patterns
        patterns = [
            r'^[A-Z]{2,4}-\d{3,6}$',  # ABC-123456
            r'^[A-Z]+\d+$',           # ABC123
            r'^\d{4,8}$'              # 12345678
        ]

        return any(re.match(pattern, text.upper()) for pattern in patterns)

    def validate_dimension(self, text: str) -> bool:
        """Validate if text looks like a valid dimension"""
        if not text:
            return False

        # Dimension patterns
        patterns = [
            r'\d+\.?\d*\s*[±]\s*\d+\.?\d*',  # 10.5±0.1
            r'\d+\.?\d*\s*mm',               # 10.5mm
            r'\d+\.?\d*\s*in',               # 10.5in
            r'\d+\.?\d*"',                   # 10.5"
        ]

        return any(re.search(pattern, text) for pattern in patterns)

    def validate_confidence(self, confidence: float) -> bool:
        """Validate confidence score"""
        return 0.0 <= confidence <= 100.0

class PatternMatcher:
    def __init__(self):
        self.part_number_patterns = [r'[A-Z]{2,4}-\d{3,6}']
        self.dimension_patterns = [r'\d+\.?\d*\s*[±]\s*\d+\.?\d*']
        self.material_patterns = [r'STEEL\s+A\d{2,3}']

    def classify_text(self, text: str) -> TextType:
        text_upper = text.upper().strip()
        for pattern in self.part_number_patterns:
            if re.search(pattern, text_upper):
                return TextType.PART_NUMBER
        for pattern in self.dimension_patterns:
            if re.search(pattern, text_upper):
                return TextType.DIMENSION
        for pattern in self.material_patterns:
            if re.search(pattern, text_upper):
                return TextType.MATERIAL
        if any(kw in text_upper for kw in ['NOTE', 'SCALE']):
            return TextType.ANNOTATION
        return TextType.GENERAL

class OCRTextExtractor:
    def __init__(self):
        self.pattern_matcher = PatternMatcher()
        self.text_preprocessor = TextPreprocessor()
        self.text_validator = TextValidator()
    
    def extract_text_from_image(self, image: np.ndarray) -> OCRResult:
        start_time = time.time()
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            ocr_data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
            extracted_texts = []
            
            for i in range(len(ocr_data['text'])):
                text = ocr_data['text'][i].strip()
                confidence = float(ocr_data['conf'][i])
                
                if text and confidence > 30:
                    bbox = (ocr_data['left'][i], ocr_data['top'][i], 
                           ocr_data['width'][i], ocr_data['height'][i])
                    text_type = self.pattern_matcher.classify_text(text)
                    
                    extracted_texts.append(ExtractedText(
                        text=text, confidence=confidence, bbox=bbox, text_type=text_type
                    ))
            
            overall_confidence = np.mean([et.confidence for et in extracted_texts]) if extracted_texts else 0.0
            
            return OCRResult(
                extracted_texts=extracted_texts,
                overall_confidence=overall_confidence,
                processing_time=time.time() - start_time,
                errors=[],
                warnings=[]
            )
        except Exception as e:
            return OCRResult(
                extracted_texts=[],
                overall_confidence=0.0,
                processing_time=time.time() - start_time,
                errors=[str(e)],
                warnings=[]
            )
    
    def extract_specific_patterns(self, image: np.ndarray) -> Dict[str, List[str]]:
        result = self.extract_text_from_image(image)
        patterns = {'dimensions': [], 'part_numbers': [], 'materials': [], 'annotations': []}
        
        for et in result.extracted_texts:
            if et.text_type == TextType.DIMENSION:
                patterns['dimensions'].append(et.text)
            elif et.text_type == TextType.PART_NUMBER:
                patterns['part_numbers'].append(et.text)
            elif et.text_type == TextType.MATERIAL:
                patterns['materials'].append(et.text)
            elif et.text_type == TextType.ANNOTATION:
                patterns['annotations'].append(et.text)
        
        return patterns
