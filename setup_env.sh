#!/bin/bash

# Environment Setup Script for BOM Generator Production
set -e

echo "🚀 Setting up production environment for BOM Generator..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to generate secure random password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to generate JWT secret
generate_jwt_secret() {
    openssl rand -base64 64 | tr -d "=+/" | cut -c1-64
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}❌ Please do not run this script as root${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Checking system requirements...${NC}"

# Check OS
if [ -f /etc/os-release ]; then
    . /etc/os-release
    echo "✅ OS: $NAME $VERSION"
else
    echo -e "${RED}❌ Cannot determine OS version${NC}"
    exit 1
fi

# Check available memory
mem_gb=$(free -g | awk 'NR==2{print $2}')
if [ "$mem_gb" -lt 8 ]; then
    echo -e "${YELLOW}⚠️  Warning: Only ${mem_gb}GB RAM available. 8GB+ recommended${NC}"
else
    echo "✅ Memory: ${mem_gb}GB"
fi

# Check available disk space
disk_gb=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
if [ "$disk_gb" -lt 100 ]; then
    echo -e "${YELLOW}⚠️  Warning: Only ${disk_gb}GB disk space available. 100GB+ recommended${NC}"
else
    echo "✅ Disk space: ${disk_gb}GB available"
fi

echo -e "\n${BLUE}🔧 Installing system dependencies...${NC}"

# Update package list
sudo apt update

# Install required packages
packages=(
    "docker.io"
    "docker-compose"
    "git"
    "curl"
    "openssl"
    "ufw"
    "htop"
    "postgresql-client"
)

for package in "${packages[@]}"; do
    if ! dpkg -l | grep -q "^ii  $package "; then
        echo "Installing $package..."
        sudo apt install -y "$package"
    else
        echo "✅ $package already installed"
    fi
done

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Add current user to docker group
sudo usermod -aG docker "$USER"

echo -e "\n${BLUE}🔒 Configuring firewall...${NC}"

# Configure UFW firewall
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

echo "✅ Firewall configured"

echo -e "\n${BLUE}📁 Creating directory structure...${NC}"

# Create necessary directories
directories=(
    "logs"
    "backups"
    "nginx/ssl"
    "monitoring/grafana/dashboards"
    "monitoring/grafana/datasources"
)

for dir in "${directories[@]}"; do
    mkdir -p "$dir"
    echo "✅ Created $dir"
done

echo -e "\n${BLUE}🔐 Generating environment configuration...${NC}"

# Generate .env.prod if it doesn't exist
if [ ! -f .env.prod ]; then
    echo "Generating .env.prod file..."
    
    # Generate secure passwords
    DB_PASSWORD=$(generate_password)
    JWT_SECRET_KEY=$(generate_jwt_secret)
    MINIO_ACCESS_KEY=$(generate_password | cut -c1-20)
    MINIO_SECRET_KEY=$(generate_password)
    GRAFANA_PASSWORD=$(generate_password | cut -c1-16)
    
    cat > .env.prod << EOF
# Production Environment Variables
# Generated on $(date)

# Database Configuration
DB_PASSWORD=$DB_PASSWORD

# JWT Configuration
JWT_SECRET_KEY=$JWT_SECRET_KEY

# MinIO/S3 Configuration
MINIO_ACCESS_KEY=$MINIO_ACCESS_KEY
MINIO_SECRET_KEY=$MINIO_SECRET_KEY

# Monitoring Configuration
GRAFANA_PASSWORD=$GRAFANA_PASSWORD

# Backup Configuration (Optional - configure for remote backups)
# BACKUP_S3_BUCKET=your-backup-bucket-name
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
BACKUP_RETENTION_DAYS=30

# Admin User (Optional - created during deployment)
# ADMIN_EMAIL=<EMAIL>
# ADMIN_PASSWORD=your_admin_password

# SSL Configuration (Optional)
# Place SSL certificates in nginx/ssl/ directory
# SSL_CERT_PATH=nginx/ssl/cert.pem
# SSL_KEY_PATH=nginx/ssl/key.pem

# Domain Configuration (Optional)
# DOMAIN_NAME=yourdomain.com
EOF

    chmod 600 .env.prod
    echo "✅ Generated .env.prod with secure passwords"
    
    echo -e "\n${YELLOW}📝 Important: Save these credentials securely:${NC}"
    echo "Database Password: $DB_PASSWORD"
    echo "Grafana Password: $GRAFANA_PASSWORD"
    echo "MinIO Access Key: $MINIO_ACCESS_KEY"
    echo "MinIO Secret Key: $MINIO_SECRET_KEY"
    
else
    echo "✅ .env.prod already exists"
fi

echo -e "\n${BLUE}🐳 Preparing Docker environment...${NC}"

# Pull base images
echo "Pulling Docker images..."
docker pull postgres:15-alpine
docker pull redis:7-alpine
docker pull minio/minio:latest
docker pull prom/prometheus:latest
docker pull grafana/grafana:latest
docker pull prom/node-exporter:latest

echo "✅ Docker images pulled"

echo -e "\n${BLUE}🔧 Setting up log rotation...${NC}"

# Create logrotate configuration
sudo tee /etc/logrotate.d/bom-generator > /dev/null << EOF
$(pwd)/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        docker-compose -f $(pwd)/docker-compose.prod.yml restart backend celery-worker 2>/dev/null || true
    endscript
}
EOF

echo "✅ Log rotation configured"

echo -e "\n${BLUE}⏰ Setting up system monitoring...${NC}"

# Create system monitoring script
cat > monitor_system.sh << 'EOF'
#!/bin/bash
# System monitoring script

LOG_FILE="logs/system_monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check disk usage
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "$DATE - WARNING: Disk usage is ${DISK_USAGE}%" >> "$LOG_FILE"
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEM_USAGE" -gt 80 ]; then
    echo "$DATE - WARNING: Memory usage is ${MEM_USAGE}%" >> "$LOG_FILE"
fi

# Check Docker containers
UNHEALTHY=$(docker-compose -f docker-compose.prod.yml ps | grep -c "unhealthy" || true)
if [ "$UNHEALTHY" -gt 0 ]; then
    echo "$DATE - WARNING: $UNHEALTHY unhealthy containers" >> "$LOG_FILE"
fi
EOF

chmod +x monitor_system.sh

# Add to crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * $(pwd)/monitor_system.sh") | crontab -

echo "✅ System monitoring configured"

echo -e "\n${GREEN}🎉 Environment setup completed!${NC}"

echo -e "\n${BLUE}📋 Next Steps:${NC}"
echo "1. Review and customize .env.prod file"
echo "2. Configure SSL certificates (optional but recommended)"
echo "3. Set up domain name and DNS (if applicable)"
echo "4. Run deployment: ./deploy.sh"
echo "5. Run health checks: ./health-check.sh"
echo "6. Run security tests: ./security/security-test.sh"

echo -e "\n${BLUE}📚 Documentation:${NC}"
echo "- Production Guide: PRODUCTION.md"
echo "- Security Guide: security/security-hardening.md"

echo -e "\n${YELLOW}⚠️  Important Notes:${NC}"
echo "- You may need to log out and back in for Docker group membership to take effect"
echo "- Configure backup storage (S3) in .env.prod for production use"
echo "- Set up SSL certificates for HTTPS in production"
echo "- Review firewall rules for your specific environment"

echo -e "\n${GREEN}✅ Setup complete! You can now deploy the application.${NC}"