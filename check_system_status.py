#!/usr/bin/env python3
"""
Quick system status checker
"""
import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking System Dependencies")
    print("-" * 40)
    
    dependencies = [
        ("python3", "Python 3"),
        ("pip", "Python Package Manager"),
        ("node", "Node.js"),
        ("npm", "Node Package Manager"),
        ("docker", "Docker"),
        ("docker-compose", "Docker Compose"),
        ("tesseract", "Tesseract OCR"),
        ("openscad", "OpenSCAD"),
    ]
    
    available = 0
    for cmd, name in dependencies:
        try:
            result = subprocess.run([cmd, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {name}")
                available += 1
            else:
                print(f"❌ {name} - Not found")
        except FileNotFoundError:
            print(f"❌ {name} - Not installed")
    
    print(f"\nDependencies available: {available}/{len(dependencies)}")
    return available == len(dependencies)

def check_project_structure():
    """Check if project structure is intact"""
    print("\n🏗️  Checking Project Structure")
    print("-" * 40)
    
    required_paths = [
        "backend/",
        "frontend/",
        "backend/main.py",
        "backend/requirements.txt",
        "frontend/package.json",
        "docker-compose.dev.yml",
        ".env.example",
    ]
    
    missing = []
    for path in required_paths:
        if Path(path).exists():
            print(f"✅ {path}")
        else:
            print(f"❌ {path} - Missing")
            missing.append(path)
    
    return len(missing) == 0

def check_environment():
    """Check environment configuration"""
    print("\n🌍 Checking Environment")
    print("-" * 40)
    
    env_files = [".env", ".env.dev", ".env.prod"]
    env_found = False
    
    for env_file in env_files:
        if Path(env_file).exists():
            print(f"✅ {env_file} found")
            env_found = True
        else:
            print(f"⚠️  {env_file} not found")
    
    if not env_found:
        print("💡 Consider copying .env.example to .env")
    
    return True

def main():
    """Run system status check"""
    print("🚀 SYSTEM STATUS CHECK")
    print("=" * 50)
    
    deps_ok = check_dependencies()
    structure_ok = check_project_structure()
    env_ok = check_environment()
    
    print("\n" + "=" * 50)
    print("📊 SYSTEM STATUS SUMMARY")
    print("=" * 50)
    
    if deps_ok and structure_ok:
        print("✅ System is ready for testing!")
        print("\nNext steps:")
        print("1. Run: python run_comprehensive_tests.py")
        print("2. Or run individual test scripts")
        return 0
    else:
        print("⚠️  System needs attention before testing")
        if not deps_ok:
            print("- Install missing dependencies")
        if not structure_ok:
            print("- Fix project structure issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())