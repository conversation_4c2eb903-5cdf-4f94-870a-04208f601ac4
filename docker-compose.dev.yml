version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15
    container_name: bom_generator_postgres
    environment:
      POSTGRES_DB: bomgen_dev
      POSTGRES_USER: bomgen
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - bom_generator_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U bomgen -d bomgen_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    container_name: bom_generator_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bom_generator_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO for file storage
  minio:
    image: minio/minio:latest
    container_name: bom_generator_minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - bom_generator_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: .
      dockerfile: ./backend/Dockerfile.dev
    container_name: bom_generator_backend
    environment:
      - DATABASE_URL=**********************************************/bomgen_dev
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - JWT_SECRET_KEY=dev_secret_key_for_development_only
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - bom_generator_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery worker for background tasks
  celery-worker:
    build:
      context: .
      dockerfile: ./backend/Dockerfile.dev
    container_name: bom_generator_celery
    command: celery -A celery_app worker --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=**********************************************/bomgen_dev
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    depends_on:
      - postgres
      - redis
      - minio
      - backend
    networks:
      - bom_generator_network

  # Frontend React app
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: bom_generator_frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - backend
    networks:
      - bom_generator_network

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  bom_generator_network:
    driver: bridge