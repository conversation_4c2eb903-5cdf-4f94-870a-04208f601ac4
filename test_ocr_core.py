#!/usr/bin/env python3
"""
Core OCR functionality tests for Task 8 (without database dependencies)
"""

import sys
import cv2
import numpy as np
from processing.ocr.text_extractor import OCRTextExtractor, PatternMatcher, TextType

def create_test_image():
    """Create a test engineering drawing image"""
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # Add typical engineering drawing text
    cv2.putText(image, "2.50±0.01", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "ABC-123", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "STEEL A36", (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "NOTE: ALL DIMS", (50, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    return image

def test_pattern_classification():
    """Test pattern classification functionality"""
    print("🧪 Testing Pattern Classification...")
    
    matcher = PatternMatcher()
    
    test_cases = [
        ("2.50±0.01", TextType.DIMENSION, "Toleranced dimension"),
        ("ABC-123", TextType.PART_NUMBER, "Part number"),
        ("STEEL A36", TextType.MATERIAL, "Material specification"),
        ("NOTE: TEST", TextType.ANNOTATION, "Annotation text"),
        ("Random text", TextType.GENERAL, "General text")
    ]
    
    passed = 0
    for text, expected_type, description in test_cases:
        result = matcher.classify_text(text)
        if result == expected_type:
            print(f"  ✅ {description}: '{text}' -> {result.value}")
            passed += 1
        else:
            print(f"  ❌ {description}: '{text}' -> {result.value} (expected: {expected_type.value})")
    
    print(f"  📊 Pattern Classification: {passed}/{len(test_cases)} passed")
    return passed == len(test_cases)

def test_ocr_extractor():
    """Test OCR text extraction"""
    print("\n🧪 Testing OCR Text Extraction...")
    
    try:
        extractor = OCRTextExtractor()
        test_image = create_test_image()
        
        # Test OCR extraction
        result = extractor.extract_text_from_image(test_image)
        
        print(f"  ✅ OCR processing completed in {result.processing_time:.2f}s")
        print(f"  📊 Extracted {len(result.extracted_texts)} text elements")
        print(f"  📈 Overall confidence: {result.overall_confidence:.1f}%")
        
        if result.errors:
            print(f"  ⚠️  Errors: {result.errors}")
        if result.warnings:
            print(f"  ⚠️  Warnings: {result.warnings}")
        
        # Test pattern extraction
        patterns = extractor.extract_specific_patterns(test_image)
        print(f"  📋 Pattern extraction results:")
        for pattern_type, items in patterns.items():
            print(f"    - {pattern_type}: {items}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ OCR extraction failed: {e}")
        return False

def test_ocr_imports():
    """Test OCR module imports"""
    print("\n🧪 Testing OCR Module Imports...")
    
    try:
        from processing.ocr.text_extractor import (
            OCRTextExtractor, PatternMatcher, TextType, 
            ExtractedText, OCRResult
        )
        print("  ✅ All OCR classes imported successfully")
        
        # Test instantiation
        extractor = OCRTextExtractor()
        matcher = PatternMatcher()
        print("  ✅ OCR objects created successfully")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Import failed: {e}")
        return False

def test_error_handling():
    """Test error handling"""
    print("\n🧪 Testing Error Handling...")
    
    try:
        extractor = OCRTextExtractor()
        
        # Test with invalid image
        invalid_image = np.array([])
        result = extractor.extract_text_from_image(invalid_image)
        
        if result.errors:
            print("  ✅ Error handling works - invalid image caught")
            return True
        else:
            print("  ❌ Error handling failed - no errors reported")
            return False
            
    except Exception as e:
        print(f"  ✅ Error handling works - exception caught: {e}")
        return True

def main():
    """Run all OCR core tests"""
    print("🚀 Running Core OCR Tests for Task 8")
    print("=" * 60)
    
    tests = [
        ("OCR Module Imports", test_ocr_imports),
        ("Pattern Classification", test_pattern_classification),
        ("OCR Text Extraction", test_ocr_extractor),
        ("Error Handling", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"📊 CORE OCR TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All core OCR tests passed!")
        print("✅ Task 8 OCR implementation is working correctly!")
        return 0
    else:
        print("⚠️  Some core tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())