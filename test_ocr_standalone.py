#!/usr/bin/env python3
"""
Standalone OCR functionality test
"""

import cv2
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append('.')

from processing.ocr.text_extractor import (
    OCRTextExtractor, TextPreprocessor, PatternMatcher, TextValidator,
    ExtractedText, OCRResult, TextType
)


def create_test_image():
    """Create a test engineering drawing image"""
    image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # Add typical engineering drawing text
    cv2.putText(image, "2.50±0.01", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "PART NO: ABC-123", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "MATERIAL: STEEL A36", (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "R0.125", (300, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "NOTE: ALL DIMS IN INCHES", (50, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    return image


def test_text_preprocessor():
    """Test TextPreprocessor functionality"""
    print("Testing TextPreprocessor...")
    
    preprocessor = TextPreprocessor()
    test_image = create_test_image()
    
    # Test preprocessing
    processed = preprocessor.preprocess_for_ocr(test_image)
    assert len(processed.shape) == 2, "Should be grayscale"
    print("✓ Preprocessing works")
    
    # Test text region enhancement
    bbox = (50, 80, 100, 40)
    enhanced = preprocessor.enhance_text_regions(processed, bbox)
    assert enhanced.shape[0] > 0 and enhanced.shape[1] > 0, "Should have valid dimensions"
    print("✓ Text region enhancement works")


def test_pattern_matcher():
    """Test PatternMatcher functionality"""
    print("Testing PatternMatcher...")
    
    matcher = PatternMatcher()
    
    # Test text classification
    test_cases = [
        ("2.50±0.01", TextType.DIMENSION),
        ("ABC-123", TextType.PART_NUMBER),
        ("STEEL A36", TextType.MATERIAL),
        ("NOTE: TEST", TextType.ANNOTATION)
    ]
    
    for text, expected_type in test_cases:
        result = matcher.classify_text(text)
        assert result == expected_type, f"Failed to classify '{text}' as {expected_type}"
        print(f"✓ Classified '{text}' as {result.value}")
    
    # Test pattern extraction
    dimensions = matcher.extract_dimensions("Dimensions: 2.50±0.01 and R0.125")
    assert len(dimensions) > 0, "Should extract dimensions"
    print(f"✓ Extracted dimensions: {dimensions}")
    
    part_numbers = matcher.extract_part_numbers("Part: ABC-123 and P/N: XYZ789")
    assert len(part_numbers) > 0, "Should extract part numbers"
    print(f"✓ Extracted part numbers: {part_numbers}")


def test_text_validator():
    """Test TextValidator functionality"""
    print("Testing TextValidator...")
    
    validator = TextValidator()
    
    # Test valid text
    is_valid, confidence, warnings = validator.validate_text("ABC123", 85.0, TextType.PART_NUMBER)
    assert is_valid, "Should validate good text"
    print(f"✓ Validated good text: confidence={confidence}")
    
    # Test invalid text
    is_valid, confidence, warnings = validator.validate_text("", 50.0, TextType.GENERAL)
    assert not is_valid, "Should reject empty text"
    print(f"✓ Rejected empty text: {warnings}")
    
    # Test text with artifacts
    is_valid, confidence, warnings = validator.validate_text("ABC|123", 70.0, TextType.GENERAL)
    assert confidence < 70.0, "Should reduce confidence for artifacts"
    print(f"✓ Reduced confidence for artifacts: {confidence}")


def test_ocr_extractor():
    """Test OCRTextExtractor functionality"""
    print("Testing OCRTextExtractor...")
    
    try:
        extractor = OCRTextExtractor()
        test_image = create_test_image()
        
        # Test OCR extraction (this requires Tesseract to be installed)
        result = extractor.extract_text_from_image(test_image)
        
        assert isinstance(result, OCRResult), "Should return OCRResult"
        assert result.processing_time > 0, "Should have processing time"
        print(f"✓ OCR extraction completed in {result.processing_time:.2f}s")
        print(f"  - Extracted {len(result.extracted_texts)} text elements")
        print(f"  - Overall confidence: {result.overall_confidence:.1f}%")
        
        if result.errors:
            print(f"  - Errors: {result.errors}")
        if result.warnings:
            print(f"  - Warnings: {result.warnings}")
        
        # Test pattern extraction
        patterns = extractor.extract_specific_patterns(test_image)
        print(f"✓ Pattern extraction completed:")
        for pattern_type, items in patterns.items():
            print(f"  - {pattern_type}: {items}")
            
    except Exception as e:
        print(f"⚠ OCR extraction test skipped (Tesseract may not be installed): {e}")


def main():
    """Run all OCR tests"""
    print("Running OCR functionality tests...\n")
    
    try:
        test_text_preprocessor()
        print()
        
        test_pattern_matcher()
        print()
        
        test_text_validator()
        print()
        
        test_ocr_extractor()
        print()
        
        print("✅ All OCR tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())