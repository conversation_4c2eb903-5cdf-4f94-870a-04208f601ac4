#!/usr/bin/env python3
"""
Simple OCR test to verify basic functionality
"""

import sys
import os
sys.path.append('.')

# Test basic imports
try:
    import cv2
    import numpy as np
    import pytesseract
    print("✓ Basic dependencies imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Test OCR module import
try:
    exec(open('processing/ocr/text_extractor.py').read())
    print("✓ OCR module executed successfully")
    
    # Test pattern matching
    matcher = PatternMatcher()
    
    # Test cases
    test_cases = [
        ("ABC-123", "part_number"),
        ("2.50±0.01", "dimension"),
        ("STEEL A36", "material"),
        ("NOTE: TEST", "annotation")
    ]
    
    print("\nTesting pattern classification:")
    for text, expected in test_cases:
        result = matcher.classify_text(text)
        print(f"  '{text}' -> {result.value} (expected: {expected})")
        if result.value == expected:
            print("    ✓ PASS")
        else:
            print("    ❌ FAIL")
    
    print("\n✅ OCR pattern matching tests completed!")
    
except Exception as e:
    print(f"❌ OCR module error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)