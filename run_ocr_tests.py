#!/usr/bin/env python3
"""
Test runner for OCR functionality (Task 8)
"""

import subprocess
import sys
import os

def run_test_command(cmd, description):
    """Run a test command and report results"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            print("✅ PASSED")
            if result.stdout:
                print("Output:")
                print(result.stdout)
        else:
            print("❌ FAILED")
            if result.stderr:
                print("Error:")
                print(result.stderr)
            if result.stdout:
                print("Output:")
                print(result.stdout)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all OCR tests for task 8"""
    print("🧪 Running OCR Test Suite for Task 8")
    print("=" * 60)
    
    tests = [
        # Test 1: OCR Text Extractor Unit Tests
        ("python -m pytest backend/tests/test_ocr_text_extractor.py::TestPatternMatcher::test_classify_dimension_text -v", 
         "Pattern Matcher - Dimension Classification"),
        
        # Test 2: OCR Service Tests
        ("python -m pytest backend/tests/test_ocr_service.py::TestOCRService::test_validate_ocr_results_high_quality -v", 
         "OCR Service - High Quality Validation"),
        
        # Test 3: OCR API Health Check
        ("python -m pytest backend/tests/test_ocr_api.py::TestOCRAPIEndpoints::test_ocr_health_check_success -v", 
         "OCR API - Health Check"),
        
        # Test 4: Simple OCR Import Test
        ("python -c \"from processing.ocr.text_extractor import OCRTextExtractor, PatternMatcher; print('✅ OCR modules imported successfully')\"", 
         "OCR Module Import Test"),
        
        # Test 5: Pattern Matching Test
        ("python -c \"from processing.ocr.text_extractor import PatternMatcher, TextType; m=PatternMatcher(); r=m.classify_text('ABC-123'); print(f'ABC-123 -> {r.value}')\"", 
         "Pattern Classification Test"),
    ]
    
    passed = 0
    total = len(tests)
    
    for cmd, description in tests:
        if run_test_command(cmd, description):
            passed += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"📊 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All OCR tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())