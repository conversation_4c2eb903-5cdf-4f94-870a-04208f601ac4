import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { AuthProvider, useAuth } from '../AuthContext';
import { AuthService } from '../../services/authService';

// Mock the AuthService
jest.mock('../../services/authService');
const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;

// Test component that uses the auth context
const TestComponent: React.FC = () => {
  const { user, isAuthenticated, isLoading, login, register, logout, updateProfile } = useAuth();

  return (
    <div>
      <div data-testid="loading">{isLoading.toString()}</div>
      <div data-testid="authenticated">{isAuthenticated.toString()}</div>
      <div data-testid="user">{user ? user.username : 'null'}</div>
      <button onClick={() => login({ username: 'test', password: 'test' })}>
        Login
      </button>
      <button onClick={() => register({ username: 'test', email: '<EMAIL>', password: 'test', confirmPassword: 'test' })}>
        Register
      </button>
      <button onClick={logout}>Logout</button>
      <button onClick={() => updateProfile({ username: 'updated' })}>
        Update Profile
      </button>
    </div>
  );
};

const renderWithAuthProvider = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('initializes with loading state', () => {
    mockAuthService.isAuthenticated.mockReturnValue(false);
    
    renderWithAuthProvider();
    
    expect(screen.getByTestId('loading')).toHaveTextContent('true');
    expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
    expect(screen.getByTestId('user')).toHaveTextContent('null');
  });

  it('loads user data if authenticated on initialization', async () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      isAdmin: false,
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z'
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getCurrentUser.mockResolvedValue(mockUser);
    
    renderWithAuthProvider();
    
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
      expect(screen.getByTestId('user')).toHaveTextContent('testuser');
    });
  });

  it('handles initialization error by clearing tokens', async () => {
    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getCurrentUser.mockRejectedValue(new Error('Token expired'));
    
    // Set up localStorage with tokens
    localStorage.setItem('accessToken', 'invalid-token');
    localStorage.setItem('refreshToken', 'invalid-refresh');
    
    renderWithAuthProvider();
    
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
      expect(screen.getByTestId('user')).toHaveTextContent('null');
      expect(localStorage.getItem('accessToken')).toBeNull();
      expect(localStorage.getItem('refreshToken')).toBeNull();
    });
  });

  it('handles login successfully', async () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      isAdmin: false,
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z'
    };

    const mockAuthResponse = {
      user: mockUser,
      accessToken: 'access-token',
      refreshToken: 'refresh-token'
    };

    mockAuthService.isAuthenticated.mockReturnValue(false);
    mockAuthService.login.mockResolvedValue(mockAuthResponse);
    
    renderWithAuthProvider();
    
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
    });
    
    const loginButton = screen.getByText('Login');
    
    await act(async () => {
      loginButton.click();
    });
    
    await waitFor(() => {
      expect(mockAuthService.login).toHaveBeenCalledWith({
        username: 'test',
        password: 'test'
      });
      expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
      expect(screen.getByTestId('user')).toHaveTextContent('testuser');
    });
  });

  it('handles register successfully', async () => {
    const mockUser = {
      id: 1,
      username: 'newuser',
      email: '<EMAIL>',
      isAdmin: false,
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z'
    };

    const mockAuthResponse = {
      user: mockUser,
      accessToken: 'access-token',
      refreshToken: 'refresh-token'
    };

    mockAuthService.isAuthenticated.mockReturnValue(false);
    mockAuthService.register.mockResolvedValue(mockAuthResponse);
    
    renderWithAuthProvider();
    
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
    });
    
    const registerButton = screen.getByText('Register');
    
    await act(async () => {
      registerButton.click();
    });
    
    await waitFor(() => {
      expect(mockAuthService.register).toHaveBeenCalledWith({
        username: 'test',
        email: '<EMAIL>',
        password: 'test',
        confirmPassword: 'test'
      });
      expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
      expect(screen.getByTestId('user')).toHaveTextContent('newuser');
    });
  });

  it('handles logout successfully', async () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      isAdmin: false,
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z'
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getCurrentUser.mockResolvedValue(mockUser);
    mockAuthService.logout.mockResolvedValue();
    
    renderWithAuthProvider();
    
    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
    });
    
    const logoutButton = screen.getByText('Logout');
    
    await act(async () => {
      logoutButton.click();
    });
    
    await waitFor(() => {
      expect(mockAuthService.logout).toHaveBeenCalled();
      expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
      expect(screen.getByTestId('user')).toHaveTextContent('null');
    });
  });

  it('handles logout even when API call fails', async () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      isAdmin: false,
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z'
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getCurrentUser.mockResolvedValue(mockUser);
    mockAuthService.logout.mockRejectedValue(new Error('Network error'));
    
    renderWithAuthProvider();
    
    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
    });
    
    const logoutButton = screen.getByText('Logout');
    
    await act(async () => {
      logoutButton.click();
    });
    
    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
      expect(screen.getByTestId('user')).toHaveTextContent('null');
    });
  });

  it('handles profile update successfully', async () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      isAdmin: false,
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z'
    };

    const updatedUser = {
      ...mockUser,
      username: 'updated'
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getCurrentUser.mockResolvedValue(mockUser);
    mockAuthService.updateProfile.mockResolvedValue(updatedUser);
    
    renderWithAuthProvider();
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('testuser');
    });
    
    const updateButton = screen.getByText('Update Profile');
    
    await act(async () => {
      updateButton.click();
    });
    
    await waitFor(() => {
      expect(mockAuthService.updateProfile).toHaveBeenCalledWith({
        username: 'updated'
      });
      expect(screen.getByTestId('user')).toHaveTextContent('updated');
    });
  });

  it('throws error when useAuth is used outside AuthProvider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');
    
    consoleSpy.mockRestore();
  });
});