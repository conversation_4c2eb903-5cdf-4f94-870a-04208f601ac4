export interface Model3D {
  id: string;
  designId: string;
  modelFilePath: string;
  openscadScript?: string;
  generationTime?: number;
  fileSize?: number;
  createdAt: string;
}

export interface ViewerSettings {
  wireframe: boolean;
  transparency: number;
  showGrid: boolean;
  backgroundColor: string;
}

export interface CameraPosition {
  x: number;
  y: number;
  z: number;
}

export interface ViewerControls {
  enableRotate: boolean;
  enableZoom: boolean;
  enablePan: boolean;
  autoRotate: boolean;
  autoRotateSpeed: number;
}

export interface ExportOptions {
  format: 'stl' | 'step' | 'obj';
  quality: 'low' | 'medium' | 'high';
}

export interface ViewerError {
  type: 'loading' | 'rendering' | 'export';
  message: string;
  details?: string;
}