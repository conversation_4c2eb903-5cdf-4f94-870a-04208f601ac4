export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
  file: File;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  previewUrl?: string;
  analysisId?: string;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface ProcessingStatus {
  stage: 'upload' | 'analysis' | 'bom' | 'model' | 'complete';
  message: string;
  progress: number;
}

export const SUPPORTED_FILE_TYPES = {
  'application/pdf': '.pdf',
  'image/png': '.png',
  'image/jpeg': '.jpg,.jpeg',
  'image/tiff': '.tiff,.tif',
  'application/dxf': '.dxf',
  'image/vnd.dxf': '.dxf'
};

export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB