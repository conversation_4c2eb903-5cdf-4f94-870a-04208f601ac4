export interface Design {
  id: string;
  userId: number;
  name: string;
  originalFilename: string;
  filePath: string;
  status: 'uploaded' | 'processing' | 'completed' | 'error';
  createdAt: string;
  updatedAt: string;
  thumbnailUrl?: string;
  fileSize: number;
  fileType: string;
  
  // Analysis results
  analysisResults?: AnalysisResult;
  bomItems?: BOMItem[];
  model3D?: Model3D;
  
  // Progress tracking
  processingProgress: ProcessingProgress;
  
  // Sharing
  isShared: boolean;
  sharedWith?: SharedUser[];
  
  // Statistics
  viewCount: number;
  lastViewed?: string;
}

export interface AnalysisResult {
  id: string;
  designId: string;
  analysisData: any;
  confidenceScore: number;
  processingTime: number;
  createdAt: string;
}

export interface BOMItem {
  id: string;
  designId: string;
  partNumber: string;
  description: string;
  quantity: number;
  material: string;
  volume: number;
  weight: number;
  unitWeight: number;
}

export interface Model3D {
  id: string;
  designId: string;
  modelFilePath: string;
  openscadScript: string;
  generationTime: number;
  fileSize: number;
  createdAt: string;
}

export interface ProcessingProgress {
  stage: 'upload' | 'analysis' | 'bom' | 'model' | 'complete' | 'error';
  progress: number;
  message: string;
  startedAt?: string;
  completedAt?: string;
  error?: string;
}

export interface SharedUser {
  id: number;
  username: string;
  email: string;
  permissionLevel: 'view' | 'edit';
  sharedAt: string;
}

export interface DesignFilters {
  search: string;
  status: string[];
  material: string[];
  dateRange: {
    start?: string;
    end?: string;
  };
  sortBy: 'name' | 'createdAt' | 'updatedAt' | 'status';
  sortOrder: 'asc' | 'desc';
}

export interface DesignStats {
  totalDesigns: number;
  completedDesigns: number;
  processingDesigns: number;
  errorDesigns: number;
  totalFileSize: number;
  averageProcessingTime: number;
  recentActivity: RecentActivity[];
}

export interface RecentActivity {
  id: string;
  type: 'upload' | 'complete' | 'share' | 'view';
  designId: string;
  designName: string;
  timestamp: string;
  details?: string;
}

export interface DesignAction {
  type: 'rename' | 'delete' | 'duplicate' | 'share' | 'download';
  designId: string;
  payload?: any;
}

export interface DuplicateDesignRequest {
  originalId: string;
  newName: string;
}

export interface RenameDesignRequest {
  id: string;
  newName: string;
}

export interface ShareDesignRequest {
  designId: string;
  userEmail: string;
  permissionLevel: 'view' | 'edit';
}