/**
 * Cross-browser compatibility tests
 * Tests core functionality across different browser environments
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Mock browser APIs that might not be available in test environment
const mockBrowserAPIs = () => {
  // Mock File API
  global.File = class MockFile {
    constructor(bits: any[], filename: string, options: any = {}) {
      this.name = filename;
      this.size = bits.reduce((acc, bit) => acc + (bit.length || 0), 0);
      this.type = options.type || '';
      this.lastModified = Date.now();
    }
    name: string;
    size: number;
    type: string;
    lastModified: number;
  } as any;

  // Mock FileReader
  global.FileReader = class MockFileReader {
    result: string | ArrayBuffer | null = null;
    error: any = null;
    readyState: number = 0;
    onload: ((event: any) => void) | null = null;
    onerror: ((event: any) => void) | null = null;
    onloadend: ((event: any) => void) | null = null;

    readAsDataURL(file: File) {
      setTimeout(() => {
        this.result = `data:${file.type};base64,mock-base64-data`;
        this.readyState = 2;
        if (this.onload) this.onload({ target: this });
        if (this.onloadend) this.onloadend({ target: this });
      }, 100);
    }

    readAsText(file: File) {
      setTimeout(() => {
        this.result = 'mock file content';
        this.readyState = 2;
        if (this.onload) this.onload({ target: this });
        if (this.onloadend) this.onloadend({ target: this });
      }, 100);
    }
  } as any;

  // Mock URL.createObjectURL
  global.URL.createObjectURL = jest.fn(() => 'mock-object-url');
  global.URL.revokeObjectURL = jest.fn();

  // Mock Canvas API
  HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
    fillRect: jest.fn(),
    clearRect: jest.fn(),
    getImageData: jest.fn(() => ({ data: new Array(4) })),
    putImageData: jest.fn(),
    createImageData: jest.fn(() => ({ data: new Array(4) })),
    setTransform: jest.fn(),
    drawImage: jest.fn(),
    save: jest.fn(),
    fillText: jest.fn(),
    restore: jest.fn(),
    beginPath: jest.fn(),
    moveTo: jest.fn(),
    lineTo: jest.fn(),
    closePath: jest.fn(),
    stroke: jest.fn(),
    translate: jest.fn(),
    scale: jest.fn(),
    rotate: jest.fn(),
    arc: jest.fn(),
    fill: jest.fn(),
    measureText: jest.fn(() => ({ width: 0 })),
    transform: jest.fn(),
    rect: jest.fn(),
    clip: jest.fn(),
  }));

  // Mock WebGL context
  HTMLCanvasElement.prototype.getContext = jest.fn((contextType) => {
    if (contextType === 'webgl' || contextType === 'webgl2') {
      return {
        createShader: jest.fn(),
        shaderSource: jest.fn(),
        compileShader: jest.fn(),
        createProgram: jest.fn(),
        attachShader: jest.fn(),
        linkProgram: jest.fn(),
        useProgram: jest.fn(),
        createBuffer: jest.fn(),
        bindBuffer: jest.fn(),
        bufferData: jest.fn(),
        getAttribLocation: jest.fn(),
        enableVertexAttribArray: jest.fn(),
        vertexAttribPointer: jest.fn(),
        drawArrays: jest.fn(),
        viewport: jest.fn(),
        clearColor: jest.fn(),
        clear: jest.fn(),
        enable: jest.fn(),
        disable: jest.fn(),
        getParameter: jest.fn(),
        getExtension: jest.fn(),
      };
    }
    return null;
  });

  // Mock ResizeObserver
  global.ResizeObserver = class MockResizeObserver {
    observe = jest.fn();
    unobserve = jest.fn();
    disconnect = jest.fn();
  };

  // Mock IntersectionObserver
  global.IntersectionObserver = class MockIntersectionObserver {
    observe = jest.fn();
    unobserve = jest.fn();
    disconnect = jest.fn();
  };

  // Mock localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  });

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock
  });
};

// Browser feature detection utilities
const detectBrowserFeatures = () => {
  return {
    supportsES6: typeof Symbol !== 'undefined',
    supportsES2017: typeof Object.values !== 'undefined',
    supportsES2018: typeof Object.fromEntries !== 'undefined',
    supportsWebGL: !!document.createElement('canvas').getContext('webgl'),
    supportsWebGL2: !!document.createElement('canvas').getContext('webgl2'),
    supportsFileAPI: typeof File !== 'undefined',
    supportsFormData: typeof FormData !== 'undefined',
    supportsFetch: typeof fetch !== 'undefined',
    supportsPromises: typeof Promise !== 'undefined',
    supportsAsyncAwait: (async () => {})().constructor === Promise,
    supportsLocalStorage: typeof Storage !== 'undefined',
    supportsSessionStorage: typeof Storage !== 'undefined',
    supportsWebWorkers: typeof Worker !== 'undefined',
    supportsServiceWorkers: 'serviceWorker' in navigator,
    supportsGeolocation: 'geolocation' in navigator,
    supportsNotifications: 'Notification' in window,
    supportsTouchEvents: 'ontouchstart' in window,
    supportsPointerEvents: 'onpointerdown' in window,
    supportsResizeObserver: typeof ResizeObserver !== 'undefined',
    supportsIntersectionObserver: typeof IntersectionObserver !== 'undefined',
  };
};

describe('Cross-Browser Compatibility Tests', () => {
  beforeAll(() => {
    mockBrowserAPIs();
  });

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('Browser Feature Detection', () => {
    test('should detect required browser features', () => {
      const features = detectBrowserFeatures();
      
      // Core JavaScript features
      expect(features.supportsES6).toBe(true);
      expect(features.supportsPromises).toBe(true);
      
      // Web APIs
      expect(features.supportsFetch).toBe(true);
      expect(features.supportsFormData).toBe(true);
      
      // Storage APIs
      expect(features.supportsLocalStorage).toBe(true);
      expect(features.supportsSessionStorage).toBe(true);
      
      console.log('Browser Features:', features);
    });

    test('should handle missing browser features gracefully', () => {
      // Temporarily remove a feature
      const originalFetch = global.fetch;
      delete (global as any).fetch;
      
      const features = detectBrowserFeatures();
      expect(features.supportsFetch).toBe(false);
      
      // Restore feature
      global.fetch = originalFetch;
    });
  });

  describe('File Upload Compatibility', () => {
    test('should handle file upload across browsers', async () => {
      const { FileUpload } = await import('../components/FileUpload');
      
      const mockOnUpload = jest.fn();
      render(<FileUpload onUpload={mockOnUpload} />);
      
      const fileInput = screen.getByLabelText(/upload/i);
      expect(fileInput).toBeInTheDocument();
      
      // Test file selection
      const file = new File(['test content'], 'test.png', { type: 'image/png' });
      
      await userEvent.upload(fileInput, file);
      
      // Verify file was processed
      await waitFor(() => {
        expect(mockOnUpload).toHaveBeenCalled();
      });
    });

    test('should handle drag and drop across browsers', async () => {
      const { FileUpload } = await import('../components/FileUpload');
      
      const mockOnUpload = jest.fn();
      render(<FileUpload onUpload={mockOnUpload} />);
      
      const dropZone = screen.getByText(/drag.*drop/i).closest('div');
      expect(dropZone).toBeInTheDocument();
      
      // Simulate drag and drop
      const file = new File(['test content'], 'test.png', { type: 'image/png' });
      
      fireEvent.dragEnter(dropZone!);
      fireEvent.dragOver(dropZone!);
      fireEvent.drop(dropZone!, {
        dataTransfer: {
          files: [file],
        },
      });
      
      await waitFor(() => {
        expect(mockOnUpload).toHaveBeenCalled();
      });
    });
  });

  describe('3D Viewer Compatibility', () => {
    test('should handle WebGL availability', async () => {
      const { Model3DViewer } = await import('../components/Model3DViewer');
      
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      // Should render without crashing even if WebGL is not available
      expect(screen.getByTestId('3d-viewer-container')).toBeInTheDocument();
    });

    test('should provide fallback for unsupported browsers', async () => {
      // Mock WebGL as unavailable
      HTMLCanvasElement.prototype.getContext = jest.fn(() => null);
      
      const { Model3DViewer } = await import('../components/Model3DViewer');
      
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      // Should show fallback message
      expect(screen.getByText(/webgl.*not.*supported/i)).toBeInTheDocument();
    });
  });

  describe('Authentication Compatibility', () => {
    test('should handle localStorage across browsers', async () => {
      const { AuthContext } = await import('../contexts/AuthContext');
      
      // Test localStorage functionality
      const mockToken = 'test-jwt-token';
      
      // Simulate storing token
      localStorage.setItem('auth_token', mockToken);
      expect(localStorage.setItem).toHaveBeenCalledWith('auth_token', mockToken);
      
      // Simulate retrieving token
      (localStorage.getItem as jest.Mock).mockReturnValue(mockToken);
      const retrievedToken = localStorage.getItem('auth_token');
      expect(retrievedToken).toBe(mockToken);
    });

    test('should handle sessionStorage fallback', async () => {
      // Mock localStorage as unavailable
      const originalLocalStorage = window.localStorage;
      delete (window as any).localStorage;
      
      const { AuthContext } = await import('../contexts/AuthContext');
      
      // Should fall back to sessionStorage
      const mockToken = 'test-jwt-token';
      sessionStorage.setItem('auth_token', mockToken);
      expect(sessionStorage.setItem).toHaveBeenCalledWith('auth_token', mockToken);
      
      // Restore localStorage
      Object.defineProperty(window, 'localStorage', {
        value: originalLocalStorage
      });
    });
  });

  describe('Form Validation Compatibility', () => {
    test('should handle form validation across browsers', async () => {
      const { LoginForm } = await import('../components/auth/LoginForm');
      
      const mockOnLogin = jest.fn();
      render(<LoginForm onLogin={mockOnLogin} />);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      // Test HTML5 validation
      expect(usernameInput).toHaveAttribute('required');
      expect(passwordInput).toHaveAttribute('required');
      
      // Test form submission with empty fields
      fireEvent.click(submitButton);
      
      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/username.*required/i)).toBeInTheDocument();
      });
    });

    test('should handle custom validation for older browsers', async () => {
      // Mock older browser without HTML5 validation
      const originalCheckValidity = HTMLInputElement.prototype.checkValidity;
      HTMLInputElement.prototype.checkValidity = undefined as any;
      
      const { LoginForm } = await import('../components/auth/LoginForm');
      
      const mockOnLogin = jest.fn();
      render(<LoginForm onLogin={mockOnLogin} />);
      
      const submitButton = screen.getByRole('button', { name: /login/i });
      fireEvent.click(submitButton);
      
      // Should still show validation errors using custom validation
      await waitFor(() => {
        expect(screen.getByText(/username.*required/i)).toBeInTheDocument();
      });
      
      // Restore original method
      HTMLInputElement.prototype.checkValidity = originalCheckValidity;
    });
  });

  describe('CSS and Layout Compatibility', () => {
    test('should handle flexbox layout', async () => {
      const { Dashboard } = await import('../components/Dashboard');
      
      render(<Dashboard />);
      
      const dashboardContainer = screen.getByTestId('dashboard-container');
      expect(dashboardContainer).toBeInTheDocument();
      
      // Check if flexbox styles are applied
      const computedStyle = window.getComputedStyle(dashboardContainer);
      expect(computedStyle.display).toBe('flex');
    });

    test('should handle grid layout fallback', async () => {
      // Mock CSS Grid as unsupported
      const originalSupports = CSS.supports;
      CSS.supports = jest.fn((property, value) => {
        if (property === 'display' && value === 'grid') {
          return false;
        }
        return originalSupports.call(CSS, property, value);
      });
      
      const { DesignGallery } = await import('../components/DesignGallery');
      
      const mockDesigns = [
        { id: '1', name: 'Design 1', thumbnail: 'thumb1.jpg' },
        { id: '2', name: 'Design 2', thumbnail: 'thumb2.jpg' },
      ];
      
      render(<DesignGallery designs={mockDesigns} />);
      
      // Should render with flexbox fallback
      const galleryContainer = screen.getByTestId('design-gallery');
      expect(galleryContainer).toBeInTheDocument();
      
      // Restore original CSS.supports
      CSS.supports = originalSupports;
    });
  });

  describe('Event Handling Compatibility', () => {
    test('should handle touch events on mobile browsers', async () => {
      const { Model3DViewer } = await import('../components/Model3DViewer');
      
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      
      // Simulate touch events
      fireEvent.touchStart(viewerContainer, {
        touches: [{ clientX: 100, clientY: 100 }]
      });
      
      fireEvent.touchMove(viewerContainer, {
        touches: [{ clientX: 150, clientY: 150 }]
      });
      
      fireEvent.touchEnd(viewerContainer);
      
      // Should handle touch events without errors
      expect(viewerContainer).toBeInTheDocument();
    });

    test('should handle pointer events', async () => {
      const { Model3DViewer } = await import('../components/Model3DViewer');
      
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      
      // Simulate pointer events
      fireEvent.pointerDown(viewerContainer, { pointerId: 1 });
      fireEvent.pointerMove(viewerContainer, { pointerId: 1 });
      fireEvent.pointerUp(viewerContainer, { pointerId: 1 });
      
      // Should handle pointer events without errors
      expect(viewerContainer).toBeInTheDocument();
    });
  });

  describe('Network Request Compatibility', () => {
    test('should handle fetch API', async () => {
      const { authService } = await import('../services/authService');
      
      // Mock successful fetch response
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ token: 'test-token' }),
      });
      
      const result = await authService.login('testuser', 'password');
      
      expect(fetch).toHaveBeenCalled();
      expect(result.token).toBe('test-token');
    });

    test('should handle XMLHttpRequest fallback', async () => {
      // Mock fetch as unavailable
      const originalFetch = global.fetch;
      delete (global as any).fetch;
      
      // Mock XMLHttpRequest
      const mockXHR = {
        open: jest.fn(),
        send: jest.fn(),
        setRequestHeader: jest.fn(),
        readyState: 4,
        status: 200,
        responseText: JSON.stringify({ token: 'test-token' }),
        onreadystatechange: null as any,
      };
      
      global.XMLHttpRequest = jest.fn(() => mockXHR) as any;
      
      const { authService } = await import('../services/authService');
      
      // Trigger the request
      const loginPromise = authService.login('testuser', 'password');
      
      // Simulate XHR completion
      if (mockXHR.onreadystatechange) {
        mockXHR.onreadystatechange();
      }
      
      const result = await loginPromise;
      expect(result.token).toBe('test-token');
      
      // Restore fetch
      global.fetch = originalFetch;
    });
  });

  describe('Error Handling Compatibility', () => {
    test('should handle errors gracefully across browsers', async () => {
      const { ErrorDisplay } = await import('../components/ErrorDisplay');
      
      const mockError = {
        message: 'Test error message',
        code: 'TEST_ERROR',
        details: 'Error details'
      };
      
      render(<ErrorDisplay error={mockError} />);
      
      expect(screen.getByText('Test error message')).toBeInTheDocument();
      expect(screen.getByText(/TEST_ERROR/)).toBeInTheDocument();
    });

    test('should handle console errors in older browsers', () => {
      // Mock console methods for older browsers
      const originalConsole = window.console;
      delete (window as any).console;
      
      // Should not throw errors when console is unavailable
      expect(() => {
        console.log('test');
        console.error('test error');
        console.warn('test warning');
      }).not.toThrow();
      
      // Restore console
      window.console = originalConsole;
    });
  });
});

// Browser-specific test suites
describe('Chrome-specific Tests', () => {
  test('should handle Chrome-specific features', () => {
    // Mock Chrome user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      configurable: true
    });
    
    // Test Chrome-specific functionality
    expect(navigator.userAgent).toContain('Chrome');
  });
});

describe('Firefox-specific Tests', () => {
  test('should handle Firefox-specific features', () => {
    // Mock Firefox user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
      configurable: true
    });
    
    // Test Firefox-specific functionality
    expect(navigator.userAgent).toContain('Firefox');
  });
});

describe('Safari-specific Tests', () => {
  test('should handle Safari-specific features', () => {
    // Mock Safari user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
      configurable: true
    });
    
    // Test Safari-specific functionality
    expect(navigator.userAgent).toContain('Safari');
  });
});

describe('Edge-specific Tests', () => {
  test('should handle Edge-specific features', () => {
    // Mock Edge user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
      configurable: true
    });
    
    // Test Edge-specific functionality
    expect(navigator.userAgent).toContain('Edg');
  });
});