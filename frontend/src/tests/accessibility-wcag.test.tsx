/**
 * Accessibility testing and WCAG compliance validation
 * Tests for WCAG 2.1 AA compliance and accessibility best practices
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { axe, toHaveNoViolations } from 'jest-axe';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock speech synthesis for screen reader testing
const mockSpeechSynthesis = {
  speak: jest.fn(),
  cancel: jest.fn(),
  pause: jest.fn(),
  resume: jest.fn(),
  getVoices: jest.fn(() => []),
  speaking: false,
  pending: false,
  paused: false,
};

Object.defineProperty(window, 'speechSynthesis', {
  value: mockSpeechSynthesis,
  writable: true,
});

// Color contrast testing utilities
const getContrastRatio = (foreground: string, background: string): number => {
  // Simplified contrast ratio calculation for testing
  // In real implementation, you'd use a proper color contrast library
  const getLuminance = (color: string): number => {
    // Mock luminance calculation
    return color === '#ffffff' ? 1 : color === '#000000' ? 0 : 0.5;
  };
  
  const l1 = getLuminance(foreground);
  const l2 = getLuminance(background);
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
};

// Focus management testing utilities
const getFocusableElements = (container: HTMLElement): HTMLElement[] => {
  const focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ];
  
  return Array.from(container.querySelectorAll(focusableSelectors.join(', ')));
};

describe('Accessibility and WCAG Compliance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('WCAG 2.1 Level A Compliance', () => {
    test('should have no accessibility violations in login form', async () => {
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn();
      
      const { container } = render(<LoginForm onLogin={mockOnLogin} />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have no accessibility violations in dashboard', async () => {
      const { Dashboard } = await import('../components/Dashboard');
      
      const { container } = render(<Dashboard />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have no accessibility violations in file upload', async () => {
      const { FileUpload } = await import('../components/FileUpload');
      const mockOnUpload = jest.fn();
      
      const { container } = render(<FileUpload onUpload={mockOnUpload} />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have no accessibility violations in 3D viewer', async () => {
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      const { container } = render(<Model3DViewer model={mockModelData} />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have no accessibility violations in results display', async () => {
      const { ResultsDisplay } = await import('../components/ResultsDisplay');
      const mockResults = {
        bom: {
          items: [
            { partNumber: 'ABC-123', description: 'Test Part', quantity: 1, material: 'Steel' }
          ]
        },
        weight: { total: 2.5, breakdown: [] },
        model3d: { id: '1', modelUrl: 'test.stl' }
      };
      
      const { container } = render(<ResultsDisplay results={mockResults} />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Keyboard Navigation (WCAG 2.1.1)', () => {
    test('should support keyboard navigation in forms', async () => {
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn();
      
      render(<LoginForm onLogin={mockOnLogin} />);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      // Test tab navigation
      usernameInput.focus();
      expect(document.activeElement).toBe(usernameInput);
      
      fireEvent.keyDown(usernameInput, { key: 'Tab' });
      expect(document.activeElement).toBe(passwordInput);
      
      fireEvent.keyDown(passwordInput, { key: 'Tab' });
      expect(document.activeElement).toBe(submitButton);
      
      // Test form submission with Enter key
      fireEvent.keyDown(submitButton, { key: 'Enter' });
      // Form should attempt to submit (validation will prevent actual submission)
    });

    test('should support keyboard navigation in file upload', async () => {
      const { FileUpload } = await import('../components/FileUpload');
      const mockOnUpload = jest.fn();
      
      render(<FileUpload onUpload={mockOnUpload} />);
      
      const fileInput = screen.getByLabelText(/upload/i);
      const dropZone = screen.getByTestId('file-drop-zone');
      
      // Drop zone should be focusable
      expect(dropZone).toHaveAttribute('tabindex', '0');
      
      // Should handle keyboard activation
      dropZone.focus();
      fireEvent.keyDown(dropZone, { key: 'Enter' });
      fireEvent.keyDown(dropZone, { key: ' ' });
      
      // Should trigger file input
      expect(fileInput).toBeInTheDocument();
    });

    test('should support keyboard navigation in 3D viewer', async () => {
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      const controls = screen.getAllByRole('button');
      
      // Viewer should be focusable
      expect(viewerContainer).toHaveAttribute('tabindex', '0');
      
      // All controls should be keyboard accessible
      controls.forEach(control => {
        expect(control).not.toHaveAttribute('tabindex', '-1');
      });
      
      // Test keyboard controls for 3D navigation
      viewerContainer.focus();
      fireEvent.keyDown(viewerContainer, { key: 'ArrowUp' });
      fireEvent.keyDown(viewerContainer, { key: 'ArrowDown' });
      fireEvent.keyDown(viewerContainer, { key: 'ArrowLeft' });
      fireEvent.keyDown(viewerContainer, { key: 'ArrowRight' });
      fireEvent.keyDown(viewerContainer, { key: '+' });
      fireEvent.keyDown(viewerContainer, { key: '-' });
    });

    test('should trap focus in modal dialogs', async () => {
      const { SharingDialog } = await import('../components/SharingDialog');
      const mockOnClose = jest.fn();
      const mockOnShare = jest.fn();
      
      render(
        <SharingDialog
          open={true}
          onClose={mockOnClose}
          onShare={mockOnShare}
          designId="1"
        />
      );
      
      const dialog = screen.getByRole('dialog');
      const focusableElements = getFocusableElements(dialog);
      
      expect(focusableElements.length).toBeGreaterThan(0);
      
      // First focusable element should be focused
      expect(document.activeElement).toBe(focusableElements[0]);
      
      // Tab should cycle through focusable elements
      fireEvent.keyDown(document.activeElement!, { key: 'Tab' });
      expect(document.activeElement).toBe(focusableElements[1] || focusableElements[0]);
      
      // Shift+Tab should go backwards
      fireEvent.keyDown(document.activeElement!, { key: 'Tab', shiftKey: true });
      expect(document.activeElement).toBe(focusableElements[0]);
      
      // Escape should close dialog
      fireEvent.keyDown(dialog, { key: 'Escape' });
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Color Contrast (WCAG 1.4.3)', () => {
    test('should meet minimum color contrast requirements', async () => {
      const { Dashboard } = await import('../components/Dashboard');
      
      const { container } = render(<Dashboard />);
      
      // Test text elements for contrast
      const textElements = container.querySelectorAll('p, span, h1, h2, h3, h4, h5, h6, button, a');
      
      textElements.forEach(element => {
        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;
        
        if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
          const contrastRatio = getContrastRatio(color, backgroundColor);
          
          // WCAG AA requires 4.5:1 for normal text, 3:1 for large text
          const fontSize = parseFloat(styles.fontSize);
          const fontWeight = styles.fontWeight;
          const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || fontWeight === '700'));
          
          const minimumRatio = isLargeText ? 3 : 4.5;
          expect(contrastRatio).toBeGreaterThanOrEqual(minimumRatio);
        }
      });
    });

    test('should provide sufficient contrast for interactive elements', async () => {
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn();
      
      const { container } = render(<LoginForm onLogin={mockOnLogin} />);
      
      const interactiveElements = container.querySelectorAll('button, input, select, textarea, a');
      
      interactiveElements.forEach(element => {
        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;
        const borderColor = styles.borderColor;
        
        // Interactive elements should have sufficient contrast
        if (color && backgroundColor) {
          const contrastRatio = getContrastRatio(color, backgroundColor);
          expect(contrastRatio).toBeGreaterThanOrEqual(3); // Minimum for interactive elements
        }
      });
    });
  });

  describe('Screen Reader Support (WCAG 4.1.2)', () => {
    test('should provide proper labels for form controls', async () => {
      const { RegisterForm } = await import('../components/auth/RegisterForm');
      const mockOnRegister = jest.fn();
      
      render(<RegisterForm onRegister={mockOnRegister} />);
      
      const inputs = screen.getAllByRole('textbox');
      const passwordInputs = screen.getAllByLabelText(/password/i);
      
      // All form controls should have labels
      [...inputs, ...passwordInputs].forEach(input => {
        const label = screen.getByLabelText(new RegExp(input.getAttribute('name') || '', 'i'));
        expect(label).toBeInTheDocument();
      });
    });

    test('should provide proper ARIA labels for complex components', async () => {
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      const controls = screen.getAllByRole('button');
      
      // 3D viewer should have proper ARIA labels
      expect(viewerContainer).toHaveAttribute('aria-label', 'Interactive 3D model viewer');
      expect(viewerContainer).toHaveAttribute('role', 'application');
      
      // All controls should have descriptive labels
      controls.forEach(control => {
        expect(control).toHaveAttribute('aria-label');
        const ariaLabel = control.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
        expect(ariaLabel!.length).toBeGreaterThan(0);
      });
    });

    test('should provide proper table headers and captions', async () => {
      const { ResultsDisplay } = await import('../components/ResultsDisplay');
      const mockResults = {
        bom: {
          items: [
            { partNumber: 'ABC-123', description: 'Test Part 1', quantity: 2, material: 'Steel' },
            { partNumber: 'DEF-456', description: 'Test Part 2', quantity: 1, material: 'Aluminum' },
          ]
        },
        weight: { total: 5.5, breakdown: [] },
        model3d: { id: '1', modelUrl: 'test.stl' }
      };
      
      render(<ResultsDisplay results={mockResults} />);
      
      const table = screen.getByRole('table');
      const caption = screen.getByText(/bill of materials/i);
      const columnHeaders = screen.getAllByRole('columnheader');
      
      // Table should have proper structure
      expect(table).toBeInTheDocument();
      expect(caption).toBeInTheDocument();
      expect(columnHeaders.length).toBeGreaterThan(0);
      
      // Headers should have proper scope
      columnHeaders.forEach(header => {
        expect(header).toHaveAttribute('scope', 'col');
      });
    });

    test('should provide status updates for dynamic content', async () => {
      const { FileUpload } = await import('../components/FileUpload');
      const mockOnUpload = jest.fn();
      
      render(<FileUpload onUpload={mockOnUpload} />);
      
      // Should have live region for status updates
      const statusRegion = screen.getByRole('status');
      expect(statusRegion).toBeInTheDocument();
      expect(statusRegion).toHaveAttribute('aria-live', 'polite');
      
      // Upload progress should be announced
      const fileInput = screen.getByLabelText(/upload/i);
      const file = new File(['test'], 'test.png', { type: 'image/png' });
      
      await userEvent.upload(fileInput, file);
      
      await waitFor(() => {
        expect(statusRegion).toHaveTextContent(/uploading/i);
      });
    });
  });

  describe('Focus Management (WCAG 2.4.3)', () => {
    test('should have logical focus order', async () => {
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn();
      
      const { container } = render(<LoginForm onLogin={mockOnLogin} />);
      
      const focusableElements = getFocusableElements(container);
      
      // Should have at least username, password, and submit button
      expect(focusableElements.length).toBeGreaterThanOrEqual(3);
      
      // Test focus order
      for (let i = 0; i < focusableElements.length - 1; i++) {
        focusableElements[i].focus();
        fireEvent.keyDown(focusableElements[i], { key: 'Tab' });
        expect(document.activeElement).toBe(focusableElements[i + 1]);
      }
    });

    test('should manage focus when content changes', async () => {
      const { App } = await import('../App');
      
      render(<App />);
      
      // Navigate to different page
      const dashboardLink = screen.getByRole('link', { name: /dashboard/i });
      fireEvent.click(dashboardLink);
      
      await waitFor(() => {
        // Focus should move to main content area
        const mainContent = screen.getByRole('main');
        expect(document.activeElement).toBe(mainContent);
      });
    });

    test('should provide skip links', async () => {
      const { App } = await import('../App');
      
      render(<App />);
      
      const skipLink = screen.getByText(/skip to main content/i);
      expect(skipLink).toBeInTheDocument();
      
      // Skip link should be focusable
      skipLink.focus();
      expect(document.activeElement).toBe(skipLink);
      
      // Activating skip link should move focus to main content
      fireEvent.click(skipLink);
      
      const mainContent = screen.getByRole('main');
      expect(document.activeElement).toBe(mainContent);
    });
  });

  describe('Error Handling and Feedback (WCAG 3.3.1)', () => {
    test('should provide clear error messages', async () => {
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn().mockRejectedValue(new Error('Invalid credentials'));
      
      render(<LoginForm onLogin={mockOnLogin} />);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      // Submit form with invalid data
      await userEvent.type(usernameInput, 'testuser');
      await userEvent.type(passwordInput, 'wrongpassword');
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        const errorMessage = screen.getByRole('alert');
        expect(errorMessage).toBeInTheDocument();
        expect(errorMessage).toHaveTextContent(/invalid credentials/i);
      });
    });

    test('should associate error messages with form fields', async () => {
      const { RegisterForm } = await import('../components/auth/RegisterForm');
      const mockOnRegister = jest.fn();
      
      render(<RegisterForm onRegister={mockOnRegister} />);
      
      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /register/i });
      
      // Submit form with invalid email
      await userEvent.type(emailInput, 'invalid-email');
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        const errorMessage = screen.getByText(/valid email/i);
        expect(errorMessage).toBeInTheDocument();
        
        // Error should be associated with input
        expect(emailInput).toHaveAttribute('aria-describedby');
        const describedBy = emailInput.getAttribute('aria-describedby');
        expect(document.getElementById(describedBy!)).toHaveTextContent(/valid email/i);
      });
    });

    test('should provide success feedback', async () => {
      const { FileUpload } = await import('../components/FileUpload');
      const mockOnUpload = jest.fn().mockResolvedValue({ success: true });
      
      render(<FileUpload onUpload={mockOnUpload} />);
      
      const fileInput = screen.getByLabelText(/upload/i);
      const file = new File(['test'], 'test.png', { type: 'image/png' });
      
      await userEvent.upload(fileInput, file);
      
      await waitFor(() => {
        const successMessage = screen.getByRole('status');
        expect(successMessage).toHaveTextContent(/upload.*successful/i);
      });
    });
  });

  describe('Responsive Design Accessibility', () => {
    test('should maintain accessibility on mobile', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', { value: 375 });
      Object.defineProperty(window, 'innerHeight', { value: 667 });
      
      const { Dashboard } = await import('../components/Dashboard');
      
      const { container } = render(<Dashboard />);
      
      // Should still pass accessibility tests on mobile
      const results = await axe(container);
      expect(results).toHaveNoViolations();
      
      // Touch targets should be large enough (minimum 44px)
      const buttons = container.querySelectorAll('button');
      buttons.forEach(button => {
        const styles = window.getComputedStyle(button);
        const minHeight = parseInt(styles.minHeight) || parseInt(styles.height);
        expect(minHeight).toBeGreaterThanOrEqual(44);
      });
    });

    test('should support high contrast mode', async () => {
      // Mock high contrast media query
      window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === '(prefers-contrast: high)',
        addListener: jest.fn(),
        removeListener: jest.fn(),
      }));
      
      const { Dashboard } = await import('../components/Dashboard');
      
      const { container } = render(<Dashboard />);
      
      // Should apply high contrast styles
      const dashboardContainer = screen.getByTestId('dashboard-container');
      expect(dashboardContainer).toHaveClass('high-contrast');
    });

    test('should respect reduced motion preferences', async () => {
      // Mock reduced motion preference
      window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        addListener: jest.fn(),
        removeListener: jest.fn(),
      }));
      
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      
      // Should apply reduced motion styles
      expect(viewerContainer).toHaveClass('reduced-motion');
    });
  });

  describe('Internationalization Accessibility', () => {
    test('should support right-to-left languages', async () => {
      // Mock RTL language
      document.documentElement.setAttribute('dir', 'rtl');
      document.documentElement.setAttribute('lang', 'ar');
      
      const { Dashboard } = await import('../components/Dashboard');
      
      const { container } = render(<Dashboard />);
      
      // Should still pass accessibility tests in RTL
      const results = await axe(container);
      expect(results).toHaveNoViolations();
      
      // Reset to LTR
      document.documentElement.setAttribute('dir', 'ltr');
      document.documentElement.setAttribute('lang', 'en');
    });

    test('should provide proper language attributes', async () => {
      const { App } = await import('../App');
      
      render(<App />);
      
      // Document should have language attribute
      expect(document.documentElement).toHaveAttribute('lang');
      
      // Any content in different languages should be marked
      const foreignContent = screen.queryAllByText(/[^\x00-\x7F]/); // Non-ASCII characters
      foreignContent.forEach(element => {
        if (element.textContent && element.textContent.length > 0) {
          expect(element.closest('[lang]')).toBeInTheDocument();
        }
      });
    });
  });
});