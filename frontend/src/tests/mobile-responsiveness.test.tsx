/**
 * Mobile responsiveness testing and optimization
 * Tests responsive design and mobile-specific functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Mock window.matchMedia for responsive testing
const mockMatchMedia = (query: string) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(), // deprecated
  removeListener: jest.fn(), // deprecated
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
});

// Viewport size presets
const VIEWPORT_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1920, height: 1080 },
  smallMobile: { width: 320, height: 568 },
  largeMobile: { width: 414, height: 896 },
  smallTablet: { width: 600, height: 800 },
  largeTablet: { width: 1024, height: 1366 },
};

// Helper function to set viewport size
const setViewportSize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });

  // Mock matchMedia for different breakpoints
  window.matchMedia = jest.fn().mockImplementation((query) => {
    const mediaQuery = mockMatchMedia(query);
    
    // Parse common media queries
    if (query.includes('max-width: 768px')) {
      mediaQuery.matches = width <= 768;
    } else if (query.includes('max-width: 1024px')) {
      mediaQuery.matches = width <= 1024;
    } else if (query.includes('min-width: 769px')) {
      mediaQuery.matches = width >= 769;
    } else if (query.includes('orientation: portrait')) {
      mediaQuery.matches = height > width;
    } else if (query.includes('orientation: landscape')) {
      mediaQuery.matches = width > height;
    }
    
    return mediaQuery;
  });

  // Trigger resize event
  fireEvent(window, new Event('resize'));
};

// Mock touch events
const mockTouchEvent = (type: string, touches: Array<{ clientX: number; clientY: number }>) => {
  return new TouchEvent(type, {
    touches: touches.map(touch => ({
      ...touch,
      identifier: 0,
      target: document.body,
      radiusX: 1,
      radiusY: 1,
      rotationAngle: 0,
      force: 1,
    })) as any,
    bubbles: true,
    cancelable: true,
  });
};

describe('Mobile Responsiveness Tests', () => {
  beforeEach(() => {
    // Reset viewport to desktop by default
    setViewportSize(VIEWPORT_SIZES.desktop.width, VIEWPORT_SIZES.desktop.height);
    jest.clearAllMocks();
  });

  describe('Responsive Layout Tests', () => {
    test('should adapt layout for mobile viewport', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { Dashboard } = await import('../components/Dashboard');
      render(<Dashboard />);
      
      const dashboardContainer = screen.getByTestId('dashboard-container');
      expect(dashboardContainer).toBeInTheDocument();
      
      // Check if mobile-specific classes are applied
      expect(dashboardContainer).toHaveClass('mobile-layout');
    });

    test('should adapt layout for tablet viewport', async () => {
      setViewportSize(VIEWPORT_SIZES.tablet.width, VIEWPORT_SIZES.tablet.height);
      
      const { Dashboard } = await import('../components/Dashboard');
      render(<Dashboard />);
      
      const dashboardContainer = screen.getByTestId('dashboard-container');
      expect(dashboardContainer).toHaveClass('tablet-layout');
    });

    test('should handle orientation changes', async () => {
      const { Dashboard } = await import('../components/Dashboard');
      render(<Dashboard />);
      
      // Portrait orientation
      setViewportSize(375, 667);
      await waitFor(() => {
        const container = screen.getByTestId('dashboard-container');
        expect(container).toHaveClass('portrait-orientation');
      });
      
      // Landscape orientation
      setViewportSize(667, 375);
      await waitFor(() => {
        const container = screen.getByTestId('dashboard-container');
        expect(container).toHaveClass('landscape-orientation');
      });
    });

    test('should handle very small screens', async () => {
      setViewportSize(VIEWPORT_SIZES.smallMobile.width, VIEWPORT_SIZES.smallMobile.height);
      
      const { Dashboard } = await import('../components/Dashboard');
      render(<Dashboard />);
      
      const dashboardContainer = screen.getByTestId('dashboard-container');
      expect(dashboardContainer).toHaveClass('small-mobile-layout');
    });
  });

  describe('Navigation Responsiveness', () => {
    test('should show mobile navigation menu', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { App } = await import('../App');
      render(<App />);
      
      // Should show hamburger menu on mobile
      const hamburgerButton = screen.getByTestId('mobile-menu-button');
      expect(hamburgerButton).toBeInTheDocument();
      
      // Desktop navigation should be hidden
      const desktopNav = screen.queryByTestId('desktop-navigation');
      expect(desktopNav).not.toBeVisible();
    });

    test('should toggle mobile menu', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { App } = await import('../App');
      render(<App />);
      
      const hamburgerButton = screen.getByTestId('mobile-menu-button');
      const mobileMenu = screen.getByTestId('mobile-menu');
      
      // Menu should be closed initially
      expect(mobileMenu).not.toBeVisible();
      
      // Open menu
      fireEvent.click(hamburgerButton);
      await waitFor(() => {
        expect(mobileMenu).toBeVisible();
      });
      
      // Close menu
      fireEvent.click(hamburgerButton);
      await waitFor(() => {
        expect(mobileMenu).not.toBeVisible();
      });
    });

    test('should close mobile menu when clicking outside', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { App } = await import('../App');
      render(<App />);
      
      const hamburgerButton = screen.getByTestId('mobile-menu-button');
      const mobileMenu = screen.getByTestId('mobile-menu');
      
      // Open menu
      fireEvent.click(hamburgerButton);
      await waitFor(() => {
        expect(mobileMenu).toBeVisible();
      });
      
      // Click outside menu
      fireEvent.click(document.body);
      await waitFor(() => {
        expect(mobileMenu).not.toBeVisible();
      });
    });
  });

  describe('Touch Interaction Tests', () => {
    test('should handle touch events for file upload', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { FileUpload } = await import('../components/FileUpload');
      const mockOnUpload = jest.fn();
      render(<FileUpload onUpload={mockOnUpload} />);
      
      const dropZone = screen.getByTestId('file-drop-zone');
      
      // Simulate touch drag and drop
      const touchStart = mockTouchEvent('touchstart', [{ clientX: 100, clientY: 100 }]);
      const touchMove = mockTouchEvent('touchmove', [{ clientX: 150, clientY: 150 }]);
      const touchEnd = mockTouchEvent('touchend', []);
      
      fireEvent(dropZone, touchStart);
      fireEvent(dropZone, touchMove);
      fireEvent(dropZone, touchEnd);
      
      // Should handle touch events without errors
      expect(dropZone).toBeInTheDocument();
    });

    test('should handle pinch-to-zoom for 3D viewer', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      
      // Simulate pinch gesture
      const pinchStart = mockTouchEvent('touchstart', [
        { clientX: 100, clientY: 100 },
        { clientX: 200, clientY: 200 }
      ]);
      
      const pinchMove = mockTouchEvent('touchmove', [
        { clientX: 80, clientY: 80 },
        { clientX: 220, clientY: 220 }
      ]);
      
      const pinchEnd = mockTouchEvent('touchend', []);
      
      fireEvent(viewerContainer, pinchStart);
      fireEvent(viewerContainer, pinchMove);
      fireEvent(viewerContainer, pinchEnd);
      
      // Should handle pinch gestures
      expect(viewerContainer).toBeInTheDocument();
    });

    test('should handle swipe gestures', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { DesignGallery } = await import('../components/DesignGallery');
      const mockDesigns = [
        { id: '1', name: 'Design 1', thumbnail: 'thumb1.jpg' },
        { id: '2', name: 'Design 2', thumbnail: 'thumb2.jpg' },
        { id: '3', name: 'Design 3', thumbnail: 'thumb3.jpg' },
      ];
      
      render(<DesignGallery designs={mockDesigns} />);
      
      const gallery = screen.getByTestId('design-gallery');
      
      // Simulate swipe left
      const swipeStart = mockTouchEvent('touchstart', [{ clientX: 200, clientY: 100 }]);
      const swipeMove = mockTouchEvent('touchmove', [{ clientX: 50, clientY: 100 }]);
      const swipeEnd = mockTouchEvent('touchend', []);
      
      fireEvent(gallery, swipeStart);
      fireEvent(gallery, swipeMove);
      fireEvent(gallery, swipeEnd);
      
      // Should handle swipe gestures
      expect(gallery).toBeInTheDocument();
    });
  });

  describe('Form Responsiveness', () => {
    test('should adapt forms for mobile input', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn();
      render(<LoginForm onLogin={mockOnLogin} />);
      
      const form = screen.getByTestId('login-form');
      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      
      // Form should have mobile-friendly styling
      expect(form).toHaveClass('mobile-form');
      
      // Inputs should be larger for touch
      expect(usernameInput).toHaveClass('mobile-input');
      expect(passwordInput).toHaveClass('mobile-input');
    });

    test('should handle virtual keyboard appearance', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn();
      render(<LoginForm onLogin={mockOnLogin} />);
      
      const usernameInput = screen.getByLabelText(/username/i);
      
      // Focus input (simulates virtual keyboard appearance)
      fireEvent.focus(usernameInput);
      
      // Simulate viewport height change due to virtual keyboard
      setViewportSize(VIEWPORT_SIZES.mobile.width, 400);
      
      await waitFor(() => {
        const form = screen.getByTestId('login-form');
        expect(form).toHaveClass('keyboard-visible');
      });
    });

    test('should provide appropriate input types for mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { RegisterForm } = await import('../components/auth/RegisterForm');
      const mockOnRegister = jest.fn();
      render(<RegisterForm onRegister={mockOnRegister} />);
      
      const emailInput = screen.getByLabelText(/email/i);
      const phoneInput = screen.getByLabelText(/phone/i);
      
      // Should use appropriate input types for mobile keyboards
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(phoneInput).toHaveAttribute('type', 'tel');
    });
  });

  describe('Content Responsiveness', () => {
    test('should adapt table layout for mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { ResultsDisplay } = await import('../components/ResultsDisplay');
      const mockResults = {
        bom: {
          items: [
            { partNumber: 'ABC-123', description: 'Test Part 1', quantity: 2, material: 'Steel' },
            { partNumber: 'DEF-456', description: 'Test Part 2', quantity: 1, material: 'Aluminum' },
          ]
        },
        weight: { total: 5.5, breakdown: [] },
        model3d: { id: '1', modelUrl: 'test.stl' }
      };
      
      render(<ResultsDisplay results={mockResults} />);
      
      const bomTable = screen.getByTestId('bom-table');
      
      // Table should use mobile-friendly layout
      expect(bomTable).toHaveClass('mobile-table');
    });

    test('should stack content vertically on mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { ResultsDisplay } = await import('../components/ResultsDisplay');
      const mockResults = {
        bom: { items: [] },
        weight: { total: 5.5, breakdown: [] },
        model3d: { id: '1', modelUrl: 'test.stl' }
      };
      
      render(<ResultsDisplay results={mockResults} />);
      
      const resultsContainer = screen.getByTestId('results-container');
      
      // Content should be stacked vertically on mobile
      expect(resultsContainer).toHaveClass('mobile-stack');
    });

    test('should hide non-essential content on small screens', async () => {
      setViewportSize(VIEWPORT_SIZES.smallMobile.width, VIEWPORT_SIZES.smallMobile.height);
      
      const { Dashboard } = await import('../components/Dashboard');
      render(<Dashboard />);
      
      // Non-essential elements should be hidden on very small screens
      const sidebar = screen.queryByTestId('dashboard-sidebar');
      expect(sidebar).not.toBeVisible();
      
      const quickStats = screen.queryByTestId('quick-stats');
      expect(quickStats).not.toBeVisible();
    });
  });

  describe('Performance on Mobile', () => {
    test('should lazy load images on mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { DesignGallery } = await import('../components/DesignGallery');
      const mockDesigns = Array.from({ length: 20 }, (_, i) => ({
        id: `${i + 1}`,
        name: `Design ${i + 1}`,
        thumbnail: `thumb${i + 1}.jpg`
      }));
      
      render(<DesignGallery designs={mockDesigns} />);
      
      const images = screen.getAllByRole('img');
      
      // Images should have lazy loading attributes
      images.forEach(img => {
        expect(img).toHaveAttribute('loading', 'lazy');
      });
    });

    test('should reduce animation complexity on mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      // Mock reduced motion preference
      window.matchMedia = jest.fn().mockImplementation((query) => {
        if (query === '(prefers-reduced-motion: reduce)') {
          return { matches: true, addListener: jest.fn(), removeListener: jest.fn() };
        }
        return mockMatchMedia(query);
      });
      
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      
      // Should have reduced animation class
      expect(viewerContainer).toHaveClass('reduced-motion');
    });

    test('should optimize touch target sizes', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { FileManagement } = await import('../components/FileManagement');
      const mockFiles = [
        { id: '1', name: 'file1.pdf', size: 1024, uploadDate: new Date() },
        { id: '2', name: 'file2.png', size: 2048, uploadDate: new Date() },
      ];
      
      render(<FileManagement files={mockFiles} />);
      
      const actionButtons = screen.getAllByRole('button');
      
      // Touch targets should be at least 44px (iOS) or 48px (Android)
      actionButtons.forEach(button => {
        const styles = window.getComputedStyle(button);
        const minSize = parseInt(styles.minHeight) || parseInt(styles.height);
        expect(minSize).toBeGreaterThanOrEqual(44);
      });
    });
  });

  describe('Accessibility on Mobile', () => {
    test('should provide proper focus management on mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { LoginForm } = await import('../components/auth/LoginForm');
      const mockOnLogin = jest.fn();
      render(<LoginForm onLogin={mockOnLogin} />);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      
      // Tab navigation should work properly
      usernameInput.focus();
      expect(document.activeElement).toBe(usernameInput);
      
      // Simulate tab key
      fireEvent.keyDown(usernameInput, { key: 'Tab' });
      expect(document.activeElement).toBe(passwordInput);
    });

    test('should provide appropriate ARIA labels for mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { App } = await import('../App');
      render(<App />);
      
      const hamburgerButton = screen.getByTestId('mobile-menu-button');
      const mobileMenu = screen.getByTestId('mobile-menu');
      
      // Mobile menu button should have proper ARIA attributes
      expect(hamburgerButton).toHaveAttribute('aria-label', 'Open navigation menu');
      expect(hamburgerButton).toHaveAttribute('aria-expanded', 'false');
      
      // Mobile menu should have proper ARIA attributes
      expect(mobileMenu).toHaveAttribute('role', 'navigation');
      expect(mobileMenu).toHaveAttribute('aria-label', 'Mobile navigation');
    });

    test('should support screen readers on mobile', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { ResultsDisplay } = await import('../components/ResultsDisplay');
      const mockResults = {
        bom: {
          items: [
            { partNumber: 'ABC-123', description: 'Test Part', quantity: 1, material: 'Steel' }
          ]
        },
        weight: { total: 2.5, breakdown: [] },
        model3d: { id: '1', modelUrl: 'test.stl' }
      };
      
      render(<ResultsDisplay results={mockResults} />);
      
      const bomTable = screen.getByTestId('bom-table');
      
      // Table should have proper ARIA attributes for screen readers
      expect(bomTable).toHaveAttribute('role', 'table');
      expect(bomTable).toHaveAttribute('aria-label', 'Bill of Materials');
      
      const tableHeaders = screen.getAllByRole('columnheader');
      expect(tableHeaders.length).toBeGreaterThan(0);
    });
  });

  describe('Mobile-specific Features', () => {
    test('should support device orientation lock', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const fullscreenButton = screen.getByTestId('fullscreen-button');
      
      // Mock screen orientation API
      Object.defineProperty(screen, 'orientation', {
        value: {
          lock: jest.fn(),
          unlock: jest.fn(),
        },
        writable: true,
      });
      
      fireEvent.click(fullscreenButton);
      
      // Should attempt to lock orientation in fullscreen
      expect(screen.orientation.lock).toHaveBeenCalledWith('landscape');
    });

    test('should handle device motion for 3D interaction', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      const { Model3DViewer } = await import('../components/Model3DViewer');
      const mockModelData = {
        id: '1',
        modelUrl: 'test-model.stl',
        name: 'Test Model'
      };
      
      render(<Model3DViewer model={mockModelData} />);
      
      const viewerContainer = screen.getByTestId('3d-viewer-container');
      
      // Mock device motion event
      const deviceMotionEvent = new Event('devicemotion') as any;
      deviceMotionEvent.accelerationIncludingGravity = {
        x: 1,
        y: 2,
        z: 3
      };
      
      fireEvent(window, deviceMotionEvent);
      
      // Should handle device motion without errors
      expect(viewerContainer).toBeInTheDocument();
    });

    test('should support haptic feedback', async () => {
      setViewportSize(VIEWPORT_SIZES.mobile.width, VIEWPORT_SIZES.mobile.height);
      
      // Mock vibration API
      Object.defineProperty(navigator, 'vibrate', {
        value: jest.fn(),
        writable: true,
      });
      
      const { FileUpload } = await import('../components/FileUpload');
      const mockOnUpload = jest.fn();
      render(<FileUpload onUpload={mockOnUpload} />);
      
      const uploadButton = screen.getByRole('button', { name: /upload/i });
      
      fireEvent.click(uploadButton);
      
      // Should provide haptic feedback on interaction
      expect(navigator.vibrate).toHaveBeenCalledWith(50);
    });
  });
});