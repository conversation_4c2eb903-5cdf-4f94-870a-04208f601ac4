/* Print-friendly styles for Results Display */
@media print {
  /* Hide non-essential elements */
  .no-print,
  .MuiAppBar-root,
  .MuiTabs-root,
  button,
  .MuiIconButton-root,
  .MuiMenu-root,
  .MuiDialog-root,
  .MuiSnackbar-root {
    display: none !important;
  }

  /* Page setup */
  @page {
    margin: 1in;
    size: letter;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
    color: black;
    background: white;
  }

  /* Typography adjustments */
  h1, h2, h3, h4, h5, h6 {
    color: black;
    page-break-after: avoid;
  }

  h1 { font-size: 18pt; }
  h2 { font-size: 16pt; }
  h3 { font-size: 14pt; }
  h4 { font-size: 13pt; }
  h5 { font-size: 12pt; }
  h6 { font-size: 11pt; }

  /* Table styles */
  table {
    border-collapse: collapse;
    width: 100%;
    page-break-inside: avoid;
  }

  th, td {
    border: 1px solid #333;
    padding: 8px;
    text-align: left;
  }

  th {
    background-color: #f0f0f0;
    font-weight: bold;
  }

  /* Card and paper styles */
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .MuiCard-root {
    border: 1px solid #ddd;
    box-shadow: none !important;
  }

  /* Grid adjustments */
  .MuiGrid-container {
    page-break-inside: avoid;
  }

  /* Chip styles */
  .MuiChip-root {
    border: 1px solid #333;
    background-color: transparent !important;
    color: black !important;
  }

  /* Alert styles */
  .MuiAlert-root {
    border: 1px solid #333;
    background-color: transparent !important;
    color: black !important;
  }

  /* Hide 3D viewer controls */
  .MuiToolbar-root {
    display: none !important;
  }

  /* Page breaks */
  .page-break {
    page-break-before: always;
  }

  .avoid-break {
    page-break-inside: avoid;
  }

  /* Print header */
  .print-header {
    display: block !important;
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #333;
    padding-bottom: 10px;
  }

  .print-header h1 {
    margin: 0;
    font-size: 20pt;
  }

  .print-header .subtitle {
    font-size: 12pt;
    color: #666;
  }

  /* Print footer */
  .print-footer {
    display: block !important;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 10pt;
    color: #666;
    border-top: 1px solid #333;
    padding-top: 5px;
  }

  /* Summary cards for print */
  .print-summary {
    display: flex !important;
    justify-content: space-around;
    margin: 20px 0;
    page-break-inside: avoid;
  }

  .print-summary-item {
    text-align: center;
    border: 1px solid #333;
    padding: 10px;
    flex: 1;
    margin: 0 5px;
  }

  .print-summary-item .value {
    font-size: 16pt;
    font-weight: bold;
  }

  .print-summary-item .label {
    font-size: 10pt;
    color: #666;
  }

  /* BOM table specific styles */
  .bom-table {
    width: 100%;
    font-size: 10pt;
  }

  .bom-table th {
    background-color: #e0e0e0;
    font-weight: bold;
    text-align: center;
  }

  .bom-table td {
    text-align: center;
  }

  .bom-table .part-number {
    font-weight: bold;
    text-align: left;
  }

  .bom-table .description {
    text-align: left;
    max-width: 200px;
    word-wrap: break-word;
  }

  /* Weight analysis styles */
  .weight-breakdown {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 20px 0;
  }

  .weight-item {
    border: 1px solid #333;
    padding: 10px;
    page-break-inside: avoid;
  }

  .weight-item .material {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .weight-item .details {
    font-size: 10pt;
    line-height: 1.2;
  }

  /* Hide interactive elements */
  .MuiSlider-root,
  .MuiSwitch-root,
  .MuiTextField-root,
  .MuiSelect-root,
  .MuiAutocomplete-root {
    display: none !important;
  }

  /* Confidence indicator */
  .confidence-indicator {
    border: 2px solid #ff9800;
    padding: 10px;
    margin: 10px 0;
    background-color: #fff3e0 !important;
  }

  .confidence-indicator.high {
    border-color: #4caf50;
    background-color: #e8f5e8 !important;
  }

  .confidence-indicator.low {
    border-color: #f44336;
    background-color: #ffebee !important;
  }
}

/* Print-specific classes that are hidden by default */
.print-only {
  display: none;
}

@media print {
  .print-only {
    display: block !important;
  }
}