import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Alert,
  Link,
  CircularProgress,
  InputAdornment,
  IconButton,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import { Visibility, VisibilityOff, ArrowBack } from '@mui/icons-material';
import { useNavigate, useSearchParams, Link as RouterLink } from 'react-router-dom';
import { authService } from '../../services/authService';
import { PasswordResetRequest, PasswordResetConfirm, ApiError } from '../../types/auth';

interface FormErrors {
  email?: string;
  token?: string;
  newPassword?: string;
  confirmPassword?: string;
  general?: string;
}

export const PasswordResetForm: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const resetToken = searchParams.get('token');
  
  const [step, setStep] = useState(resetToken ? 1 : 0); // 0: Request, 1: Confirm
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [successMessage, setSuccessMessage] = useState('');
  
  const [requestData, setRequestData] = useState<PasswordResetRequest>({
    email: ''
  });
  
  const [confirmData, setConfirmData] = useState<PasswordResetConfirm>({
    token: resetToken || '',
    newPassword: '',
    confirmPassword: ''
  });

  const validateRequestForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!requestData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(requestData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateConfirmForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!confirmData.token.trim()) {
      newErrors.token = 'Reset token is required';
    }

    if (!confirmData.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (confirmData.newPassword.length < 6) {
      newErrors.newPassword = 'Password must be at least 6 characters';
    }

    if (!confirmData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (confirmData.confirmPassword !== confirmData.newPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRequestInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setRequestData({ email: value });
    
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: undefined }));
    }
  };

  const handleConfirmInputChange = (field: keyof PasswordResetConfirm) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    setConfirmData(prev => ({ ...prev, [field]: value }));
    
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleRequestSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateRequestForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      await authService.requestPasswordReset(requestData);
      setSuccessMessage('Password reset instructions have been sent to your email.');
      setStep(1);
    } catch (error) {
      const apiError = error as ApiError;
      setErrors({
        general: apiError.message || 'Failed to send reset email. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateConfirmForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      await authService.confirmPasswordReset(confirmData);
      setSuccessMessage('Your password has been reset successfully. You can now sign in with your new password.');
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      const apiError = error as ApiError;
      setErrors({
        general: apiError.message || 'Failed to reset password. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const steps = ['Request Reset', 'Set New Password'];

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      bgcolor="grey.100"
      py={2}
    >
      <Paper elevation={3} sx={{ p: 4, maxWidth: 500, width: '100%', mx: 2 }}>
        <Box display="flex" alignItems="center" mb={2}>
          <IconButton
            onClick={() => navigate('/login')}
            sx={{ mr: 1 }}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h4" component="h1">
            Reset Password
          </Typography>
        </Box>

        <Stepper activeStep={step} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {successMessage && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {successMessage}
          </Alert>
        )}

        {errors.general && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {errors.general}
          </Alert>
        )}

        {step === 0 ? (
          // Request Reset Form
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Enter your email address and we'll send you instructions to reset your password.
            </Typography>

            <Box component="form" onSubmit={handleRequestSubmit} noValidate>
              <TextField
                fullWidth
                label="Email Address"
                type="email"
                value={requestData.email}
                onChange={handleRequestInputChange}
                error={!!errors.email}
                helperText={errors.email}
                margin="normal"
                autoComplete="email"
                autoFocus
                disabled={isLoading}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ mt: 3, mb: 2 }}
                disabled={isLoading}
                size="large"
              >
                {isLoading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  'Send Reset Instructions'
                )}
              </Button>
            </Box>
          </Box>
        ) : (
          // Confirm Reset Form
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Enter the reset token from your email and choose a new password.
            </Typography>

            <Box component="form" onSubmit={handleConfirmSubmit} noValidate>
              <TextField
                fullWidth
                label="Reset Token"
                value={confirmData.token}
                onChange={handleConfirmInputChange('token')}
                error={!!errors.token}
                helperText={errors.token || 'Check your email for the reset token'}
                margin="normal"
                disabled={isLoading || !!resetToken}
                autoFocus={!resetToken}
              />

              <TextField
                fullWidth
                label="New Password"
                type={showPassword ? 'text' : 'password'}
                value={confirmData.newPassword}
                onChange={handleConfirmInputChange('newPassword')}
                error={!!errors.newPassword}
                helperText={errors.newPassword}
                margin="normal"
                autoComplete="new-password"
                disabled={isLoading}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={togglePasswordVisibility}
                        edge="end"
                        disabled={isLoading}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                fullWidth
                label="Confirm New Password"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmData.confirmPassword}
                onChange={handleConfirmInputChange('confirmPassword')}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword}
                margin="normal"
                autoComplete="new-password"
                disabled={isLoading}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle confirm password visibility"
                        onClick={toggleConfirmPasswordVisibility}
                        edge="end"
                        disabled={isLoading}
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ mt: 3, mb: 2 }}
                disabled={isLoading}
                size="large"
              >
                {isLoading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  'Reset Password'
                )}
              </Button>
            </Box>
          </Box>
        )}

        <Box textAlign="center" mt={2}>
          <Typography variant="body2">
            Remember your password?{' '}
            <Link component={RouterLink} to="/login">
              Sign in
            </Link>
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};