import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { PasswordResetForm } from '../PasswordResetForm';
import { AuthService } from '../../../services/authService';

// Mock the AuthService
jest.mock('../../../services/authService');
const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useSearchParams: () => [new URLSearchParams()],
}));

const theme = createTheme();

const renderPasswordResetForm = (token?: string) => {
  // Mock useSearchParams to return token if provided
  if (token) {
    jest.doMock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useNavigate: () => mockNavigate,
      useSearchParams: () => [new URLSearchParams(`token=${token}`)],
    }));
  }

  return render(
    <ThemeProvider theme={theme}>
      <BrowserRouter>
        <PasswordResetForm />
      </BrowserRouter>
    </ThemeProvider>
  );
};

describe('PasswordResetForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders password reset request form initially', () => {
    renderPasswordResetForm();
    
    expect(screen.getByRole('heading', { name: /reset password/i })).toBeInTheDocument();
    expect(screen.getByText(/enter your email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send reset instructions/i })).toBeInTheDocument();
  });

  it('shows validation error for empty email', async () => {
    const user = userEvent.setup();
    renderPasswordResetForm();
    
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    });
  });

  it('shows validation error for invalid email format', async () => {
    const user = userEvent.setup();
    renderPasswordResetForm();
    
    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('submits password reset request successfully', async () => {
    const user = userEvent.setup();
    mockAuthService.requestPasswordReset.mockResolvedValue();
    
    renderPasswordResetForm();
    
    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockAuthService.requestPasswordReset).toHaveBeenCalledWith({
        email: '<EMAIL>'
      });
      expect(screen.getByText(/password reset instructions have been sent/i)).toBeInTheDocument();
    });
  });

  it('shows error message on request failure', async () => {
    const user = userEvent.setup();
    const errorMessage = 'Email not found';
    
    mockAuthService.requestPasswordReset.mockRejectedValue({
      code: 'EMAIL_NOT_FOUND',
      message: errorMessage
    });
    
    renderPasswordResetForm();
    
    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('advances to confirmation step after successful request', async () => {
    const user = userEvent.setup();
    mockAuthService.requestPasswordReset.mockResolvedValue();
    
    renderPasswordResetForm();
    
    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/enter the reset token from your email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/reset token/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/new password/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm new password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reset password/i })).toBeInTheDocument();
    });
  });

  it('validates confirmation form fields', async () => {
    const user = userEvent.setup();
    mockAuthService.requestPasswordReset.mockResolvedValue();
    
    renderPasswordResetForm();
    
    // First advance to confirmation step
    const emailInput = screen.getByLabelText(/email address/i);
    const requestButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(requestButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/reset token/i)).toBeInTheDocument();
    });
    
    // Try to submit empty confirmation form
    const resetButton = screen.getByRole('button', { name: /reset password/i });
    await user.click(resetButton);
    
    await waitFor(() => {
      expect(screen.getByText(/reset token is required/i)).toBeInTheDocument();
      expect(screen.getByText(/new password is required/i)).toBeInTheDocument();
      expect(screen.getByText(/please confirm your password/i)).toBeInTheDocument();
    });
  });

  it('validates password confirmation match', async () => {
    const user = userEvent.setup();
    mockAuthService.requestPasswordReset.mockResolvedValue();
    
    renderPasswordResetForm();
    
    // Advance to confirmation step
    const emailInput = screen.getByLabelText(/email address/i);
    const requestButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(requestButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/reset token/i)).toBeInTheDocument();
    });
    
    // Fill form with mismatched passwords
    const tokenInput = screen.getByLabelText(/reset token/i);
    const passwordInput = screen.getByLabelText(/new password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm new password/i);
    const resetButton = screen.getByRole('button', { name: /reset password/i });
    
    await user.type(tokenInput, 'reset-token-123');
    await user.type(passwordInput, 'newpassword123');
    await user.type(confirmPasswordInput, 'differentpassword');
    await user.click(resetButton);
    
    await waitFor(() => {
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
    });
  });

  it('submits password reset confirmation successfully', async () => {
    const user = userEvent.setup();
    mockAuthService.requestPasswordReset.mockResolvedValue();
    mockAuthService.confirmPasswordReset.mockResolvedValue();
    
    renderPasswordResetForm();
    
    // Advance to confirmation step
    const emailInput = screen.getByLabelText(/email address/i);
    const requestButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(requestButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/reset token/i)).toBeInTheDocument();
    });
    
    // Fill and submit confirmation form
    const tokenInput = screen.getByLabelText(/reset token/i);
    const passwordInput = screen.getByLabelText(/new password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm new password/i);
    const resetButton = screen.getByRole('button', { name: /reset password/i });
    
    await user.type(tokenInput, 'reset-token-123');
    await user.type(passwordInput, 'newpassword123');
    await user.type(confirmPasswordInput, 'newpassword123');
    await user.click(resetButton);
    
    await waitFor(() => {
      expect(mockAuthService.confirmPasswordReset).toHaveBeenCalledWith({
        token: 'reset-token-123',
        newPassword: 'newpassword123',
        confirmPassword: 'newpassword123'
      });
      expect(screen.getByText(/your password has been reset successfully/i)).toBeInTheDocument();
    });
  });

  it('toggles password visibility', async () => {
    const user = userEvent.setup();
    mockAuthService.requestPasswordReset.mockResolvedValue();
    
    renderPasswordResetForm();
    
    // Advance to confirmation step
    const emailInput = screen.getByLabelText(/email address/i);
    const requestButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(requestButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/new password/i)).toBeInTheDocument();
    });
    
    const passwordInput = screen.getByLabelText(/new password/i) as HTMLInputElement;
    const confirmPasswordInput = screen.getByLabelText(/confirm new password/i) as HTMLInputElement;
    const toggleButtons = screen.getAllByLabelText(/toggle.*password visibility/i);
    
    expect(passwordInput.type).toBe('password');
    expect(confirmPasswordInput.type).toBe('password');
    
    // Toggle password visibility
    await user.click(toggleButtons[0]);
    expect(passwordInput.type).toBe('text');
    
    // Toggle confirm password visibility
    await user.click(toggleButtons[1]);
    expect(confirmPasswordInput.type).toBe('text');
  });

  it('navigates back to login', async () => {
    const user = userEvent.setup();
    renderPasswordResetForm();
    
    const backButton = screen.getByLabelText(/back/i);
    await user.click(backButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });

  it('shows loading state during submission', async () => {
    const user = userEvent.setup();
    
    mockAuthService.requestPasswordReset.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );
    
    renderPasswordResetForm();
    
    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
    
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  });
});