import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { AuthProvider } from '../../../contexts/AuthContext';
import { LoginForm } from '../LoginForm';
import { RegisterForm } from '../RegisterForm';
import { UserProfile } from '../UserProfile';
import { ProtectedRoute } from '../ProtectedRoute';

// Mock the AuthService
jest.mock('../../../services/authService', () => ({
  AuthService: {
    isAuthenticated: jest.fn(() => false),
    getCurrentUser: jest.fn(),
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    updateProfile: jest.fn(),
    requestPasswordReset: jest.fn(),
    confirmPasswordReset: jest.fn(),
    refreshToken: jest.fn(),
  }
}));

const theme = createTheme();

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      <MemoryRouter>
        <AuthProvider>
          {component}
        </AuthProvider>
      </MemoryRouter>
    </ThemeProvider>
  );
};

describe('Authentication Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('LoginForm', () => {
    it('renders login form with required elements', () => {
      renderWithProviders(<LoginForm />);
      
      expect(screen.getByRole('heading', { name: /sign in/i })).toBeInTheDocument();
      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('has links to register and password reset', () => {
      renderWithProviders(<LoginForm />);
      
      expect(screen.getByText(/forgot your password/i)).toBeInTheDocument();
      expect(screen.getByText(/don't have an account/i)).toBeInTheDocument();
    });
  });

  describe('RegisterForm', () => {
    it('renders registration form with required elements', () => {
      renderWithProviders(<RegisterForm />);
      
      expect(screen.getByRole('heading', { name: /create account/i })).toBeInTheDocument();
      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
    });

    it('has link to login page', () => {
      renderWithProviders(<RegisterForm />);
      
      expect(screen.getByText(/already have an account/i)).toBeInTheDocument();
    });
  });

  describe('ProtectedRoute', () => {
    it('shows loading state initially', () => {
      renderWithProviders(
        <ProtectedRoute>
          <div>Protected Content</div>
        </ProtectedRoute>
      );
      
      // Should show loading spinner while checking authentication
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });
});