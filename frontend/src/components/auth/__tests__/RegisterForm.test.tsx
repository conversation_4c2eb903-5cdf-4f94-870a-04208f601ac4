import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { RegisterForm } from '../RegisterForm';
import { AuthProvider } from '../../../contexts/AuthContext';
import { AuthService } from '../../../services/authService';

// Mock the AuthService
jest.mock('../../../services/authService');
const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const theme = createTheme();

const renderRegisterForm = () => {
  return render(
    <ThemeProvider theme={theme}>
      <BrowserRouter>
        <AuthProvider>
          <RegisterForm />
        </AuthProvider>
      </BrowserRouter>
    </ThemeProvider>
  );
};

describe('RegisterForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders registration form with all required fields', () => {
    renderRegisterForm();
    
    expect(screen.getByRole('heading', { name: /create account/i })).toBeInTheDocument();
    expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
    expect(screen.getByText(/already have an account/i)).toBeInTheDocument();
  });

  it('shows validation errors for empty fields', async () => {
    const user = userEvent.setup();
    renderRegisterForm();
    
    const submitButton = screen.getByRole('button', { name: /create account/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      expect(screen.getByText(/please confirm your password/i)).toBeInTheDocument();
    });
  });

  it('validates username format', async () => {
    const user = userEvent.setup();
    renderRegisterForm();
    
    const usernameInput = screen.getByLabelText(/username/i);
    
    // Test short username
    await user.type(usernameInput, 'ab');
    await user.tab();
    await waitFor(() => {
      expect(screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument();
    });
    
    // Test invalid characters
    await user.clear(usernameInput);
    await user.type(usernameInput, 'user@name');
    await user.tab();
    await waitFor(() => {
      expect(screen.getByText(/username can only contain letters, numbers, and underscores/i)).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    const user = userEvent.setup();
    renderRegisterForm();
    
    const emailInput = screen.getByLabelText(/email/i);
    
    await user.type(emailInput, 'invalid-email');
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('shows password strength indicator', async () => {
    const user = userEvent.setup();
    renderRegisterForm();
    
    const passwordInput = screen.getByLabelText(/^password$/i);
    
    // Weak password
    await user.type(passwordInput, '123');
    await waitFor(() => {
      expect(screen.getByText(/password strength: weak/i)).toBeInTheDocument();
    });
    
    // Strong password
    await user.clear(passwordInput);
    await user.type(passwordInput, 'StrongPass123!');
    await waitFor(() => {
      expect(screen.getByText(/password strength: strong/i)).toBeInTheDocument();
    });
  });

  it('validates password confirmation', async () => {
    const user = userEvent.setup();
    renderRegisterForm();
    
    const passwordInput = screen.getByLabelText(/^password$/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
    
    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'different123');
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
    });
  });

  it('shows success indicators for valid fields', async () => {
    const user = userEvent.setup();
    renderRegisterForm();
    
    const usernameInput = screen.getByLabelText(/username/i);
    const emailInput = screen.getByLabelText(/email/i);
    
    await user.type(usernameInput, 'validuser');
    await user.type(emailInput, '<EMAIL>');
    
    await waitFor(() => {
      // Check for success icons (CheckCircle components)
      const successIcons = screen.getAllByTestId('CheckCircleIcon');
      expect(successIcons).toHaveLength(2);
    });
  });

  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    const mockUser = {
      id: 1,
      username: 'newuser',
      email: '<EMAIL>',
      isAdmin: false,
      isActive: true,
      createdAt: '2023-01-01T00:00:00Z'
    };
    
    mockAuthService.register.mockResolvedValue({
      user: mockUser,
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token'
    });
    
    renderRegisterForm();
    
    const usernameInput = screen.getByLabelText(/username/i);
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password$/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    await user.type(usernameInput, 'newuser');
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'password123');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockAuthService.register).toHaveBeenCalledWith({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      });
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('shows error message on registration failure', async () => {
    const user = userEvent.setup();
    const errorMessage = 'Username already exists';
    
    mockAuthService.register.mockRejectedValue({
      code: 'USERNAME_EXISTS',
      message: errorMessage
    });
    
    renderRegisterForm();
    
    const usernameInput = screen.getByLabelText(/username/i);
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password$/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    await user.type(usernameInput, 'existinguser');
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'password123');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('toggles password visibility', async () => {
    const user = userEvent.setup();
    renderRegisterForm();
    
    const passwordInput = screen.getByLabelText(/^password$/i) as HTMLInputElement;
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i) as HTMLInputElement;
    const toggleButtons = screen.getAllByLabelText(/toggle.*password visibility/i);
    
    expect(passwordInput.type).toBe('password');
    expect(confirmPasswordInput.type).toBe('password');
    
    // Toggle password visibility
    await user.click(toggleButtons[0]);
    expect(passwordInput.type).toBe('text');
    
    // Toggle confirm password visibility
    await user.click(toggleButtons[1]);
    expect(confirmPasswordInput.type).toBe('text');
  });

  it('shows loading state during submission', async () => {
    const user = userEvent.setup();
    
    mockAuthService.register.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );
    
    renderRegisterForm();
    
    const usernameInput = screen.getByLabelText(/username/i);
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password$/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /create account/i });
    
    await user.type(usernameInput, 'newuser');
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'password123');
    await user.click(submitButton);
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
    
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  });
});