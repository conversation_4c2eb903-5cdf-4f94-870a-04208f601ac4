import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  CloudUpload,
  CheckCircle,
  Schedule,
  Error,
  Storage,
  Timeline,
  Visibility,
  Share,
  FileCopy
} from '@mui/icons-material';
import { format, formatDistanceToNow } from 'date-fns';
import { DesignStats as IDesignStats, RecentActivity } from '../types/design';

interface DesignStatsProps {
  stats: IDesignStats;
  loading?: boolean;
}

export const DesignStats: React.FC<DesignStatsProps> = ({ stats, loading = false }) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatProcessingTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'upload':
        return <CloudUpload color="primary" />;
      case 'complete':
        return <CheckCircle color="success" />;
      case 'share':
        return <Share color="info" />;
      case 'view':
        return <Visibility color="action" />;
      default:
        return <Timeline color="action" />;
    }
  };

  const getActivityColor = (type: RecentActivity['type']) => {
    switch (type) {
      case 'upload':
        return 'primary';
      case 'complete':
        return 'success';
      case 'share':
        return 'info';
      case 'view':
        return 'default';
      default:
        return 'default';
    }
  };

  const completionRate = stats.totalDesigns > 0 
    ? (stats.completedDesigns / stats.totalDesigns) * 100 
    : 0;

  if (loading) {
    return (
      <Box>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((i) => (
            <Grid item xs={12} sm={6} md={3} key={i}>
              <Card>
                <CardContent>
                  <Box height={80} display="flex" alignItems="center" justifyContent="center">
                    <LinearProgress sx={{ width: '100%' }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box>
      {/* Overview Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Total Designs
                  </Typography>
                  <Typography variant="h4" component="div">
                    {stats.totalDesigns}
                  </Typography>
                </Box>
                <CloudUpload color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Completed
                  </Typography>
                  <Typography variant="h4" component="div">
                    {stats.completedDesigns}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    {completionRate.toFixed(1)}% completion rate
                  </Typography>
                </Box>
                <CheckCircle color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Processing
                  </Typography>
                  <Typography variant="h4" component="div">
                    {stats.processingDesigns}
                  </Typography>
                  {stats.processingDesigns > 0 && (
                    <Typography variant="body2" color="warning.main">
                      In progress
                    </Typography>
                  )}
                </Box>
                <Schedule color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Errors
                  </Typography>
                  <Typography variant="h4" component="div">
                    {stats.errorDesigns}
                  </Typography>
                  {stats.errorDesigns > 0 && (
                    <Typography variant="body2" color="error.main">
                      Need attention
                    </Typography>
                  )}
                </Box>
                <Error color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Additional Stats */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Storage Usage
              </Typography>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Storage color="info" />
                <Box flexGrow={1}>
                  <Typography variant="h5">
                    {formatFileSize(stats.totalFileSize)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total file storage used
                  </Typography>
                </Box>
              </Box>
              <LinearProgress
                variant="determinate"
                value={Math.min((stats.totalFileSize / (1024 * 1024 * 1024)) * 10, 100)} // Assume 10GB limit
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance
              </Typography>
              <Box display="flex" alignItems="center" gap={2}>
                <Timeline color="info" />
                <Box>
                  <Typography variant="h5">
                    {formatProcessingTime(stats.averageProcessingTime)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average processing time
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Activity
          </Typography>
          {stats.recentActivity.length === 0 ? (
            <Typography variant="body2" color="text.secondary" textAlign="center" py={3}>
              No recent activity
            </Typography>
          ) : (
            <List>
              {stats.recentActivity.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem>
                    <ListItemIcon>
                      {getActivityIcon(activity.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body1">
                            {activity.designName}
                          </Typography>
                          <Chip
                            label={activity.type}
                            size="small"
                            color={getActivityColor(activity.type) as any}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {activity.details}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < stats.recentActivity.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};