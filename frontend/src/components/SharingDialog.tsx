import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Box,
  Chip,
  Autocomplete,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Share as ShareIcon,
} from '@mui/icons-material';
import sharingService, {
  DesignShare,
  UserSearchResult,
  ShareRequest,
} from '../services/sharingService';

interface SharingDialogProps {
  open: boolean;
  onClose: () => void;
  designId: number;
  designName: string;
}

const SharingDialog: React.FC<SharingDialogProps> = ({
  open,
  onClose,
  designId,
  designName,
}) => {
  const [shares, setShares] = useState<DesignShare[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // New share form state
  const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null);
  const [permissionLevel, setPermissionLevel] = useState<'view' | 'edit'>('view');
  const [userSearchOptions, setUserSearchOptions] = useState<UserSearchResult[]>([]);
  const [userSearchLoading, setUserSearchLoading] = useState(false);

  // Load existing shares
  const loadShares = async () => {
    try {
      setLoading(true);
      const response = await sharingService.getDesignShares(designId);
      setShares(response.shares);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load shares');
    } finally {
      setLoading(false);
    }
  };

  // Search users
  const searchUsers = async (query: string) => {
    if (query.length < 2) {
      setUserSearchOptions([]);
      return;
    }

    try {
      setUserSearchLoading(true);
      const response = await sharingService.searchUsers(query);
      setUserSearchOptions(response.users);
    } catch (err) {
      console.error('Failed to search users:', err);
      setUserSearchOptions([]);
    } finally {
      setUserSearchLoading(false);
    }
  };

  // Grant access
  const handleGrantAccess = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError(null);
      
      const request: ShareRequest = {
        design_id: designId,
        shared_with_email: selectedUser.email,
        permission_level: permissionLevel,
      };

      const response = await sharingService.grantAccess(request);
      setSuccess(response.message);
      
      // Reset form
      setSelectedUser(null);
      setPermissionLevel('view');
      
      // Reload shares
      await loadShares();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to share design');
    } finally {
      setLoading(false);
    }
  };

  // Revoke access
  const handleRevokeAccess = async (share: DesignShare) => {
    try {
      setLoading(true);
      setError(null);
      
      await sharingService.revokeAccess(designId, share.user_id);
      setSuccess(`Access revoked for ${share.username}`);
      
      // Reload shares
      await loadShares();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to revoke access');
    } finally {
      setLoading(false);
    }
  };

  // Update permission
  const handleUpdatePermission = async (share: DesignShare, newPermission: 'view' | 'edit') => {
    try {
      setLoading(true);
      setError(null);
      
      await sharingService.updatePermission(designId, share.user_id, newPermission);
      setSuccess(`Permission updated for ${share.username}`);
      
      // Reload shares
      await loadShares();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update permission');
    } finally {
      setLoading(false);
    }
  };

  // Load shares when dialog opens
  useEffect(() => {
    if (open) {
      loadShares();
      setError(null);
      setSuccess(null);
    }
  }, [open, designId]);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <ShareIcon />
          Share "{designName}"
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* Add new share section */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Share with new user
          </Typography>
          
          <Box display="flex" gap={2} alignItems="flex-end">
            <Autocomplete
              sx={{ flex: 1 }}
              options={userSearchOptions}
              getOptionLabel={(option) => `${option.username} (${option.email})`}
              value={selectedUser}
              onChange={(_, newValue) => setSelectedUser(newValue)}
              onInputChange={(_, newInputValue) => {
                searchUsers(newInputValue);
              }}
              loading={userSearchLoading}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Search users by username or email"
                  variant="outlined"
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {userSearchLoading ? <CircularProgress color="inherit" size={20} /> : null}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
            
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>Permission</InputLabel>
              <Select
                value={permissionLevel}
                onChange={(e) => setPermissionLevel(e.target.value as 'view' | 'edit')}
                label="Permission"
              >
                <MenuItem value="view">View</MenuItem>
                <MenuItem value="edit">Edit</MenuItem>
              </Select>
            </FormControl>
            
            <Button
              variant="contained"
              onClick={handleGrantAccess}
              disabled={!selectedUser || loading}
              startIcon={<ShareIcon />}
            >
              Share
            </Button>
          </Box>
        </Box>

        {/* Existing shares section */}
        <Typography variant="h6" gutterBottom>
          Current shares ({shares.length})
        </Typography>
        
        {loading && shares.length === 0 ? (
          <Box display="flex" justifyContent="center" p={2}>
            <CircularProgress />
          </Box>
        ) : shares.length === 0 ? (
          <Typography color="textSecondary" sx={{ p: 2, textAlign: 'center' }}>
            This design is not shared with anyone yet.
          </Typography>
        ) : (
          <List>
            {shares.map((share) => (
              <ListItem key={share.share_id} divider>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="subtitle1">
                        {share.username}
                      </Typography>
                      <Chip
                        size="small"
                        icon={share.permission_level === 'edit' ? <EditIcon /> : <ViewIcon />}
                        label={share.permission_level}
                        color={share.permission_level === 'edit' ? 'primary' : 'default'}
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        {share.email}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        Shared on {new Date(share.shared_at).toLocaleDateString()}
                      </Typography>
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <Box display="flex" gap={1}>
                    <FormControl size="small" sx={{ minWidth: 80 }}>
                      <Select
                        value={share.permission_level}
                        onChange={(e) => 
                          handleUpdatePermission(share, e.target.value as 'view' | 'edit')
                        }
                        disabled={loading}
                      >
                        <MenuItem value="view">View</MenuItem>
                        <MenuItem value="edit">Edit</MenuItem>
                      </Select>
                    </FormControl>
                    
                    <IconButton
                      edge="end"
                      onClick={() => handleRevokeAccess(share)}
                      disabled={loading}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default SharingDialog;