import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Tab,
  Tabs,
  Alert,
  Snackbar
} from '@mui/material';
import { 
  AccountCircle, 
  ExitToApp, 
  Person, 
  CloudUpload, 
  Dashboard as DashboardIcon, 
  Share,
  Analytics,
  Refresh
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { UploadContainer } from './UploadContainer';
import { DesignGallery } from './DesignGallery';
import { DesignFilters } from './DesignFilters';
import { DesignStats } from './DesignStats';
import SharingDialog from './SharingDialog';
import { 
  Design, 
  DesignFilters as IDesignFilters, 
  DesignAction, 
  DesignStats as IDesignStats 
} from '../types/design';
import { DesignService } from '../services/designService';

export const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [currentTab, setCurrentTab] = useState(1); // Start with My Designs tab
  
  // State for designs and data
  const [designs, setDesigns] = useState<Design[]>([]);
  const [sharedDesigns, setSharedDesigns] = useState<Design[]>([]);
  const [recentDesigns, setRecentDesigns] = useState<Design[]>([]);
  const [designStats, setDesignStats] = useState<IDesignStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Sharing dialog state
  const [sharingDialogOpen, setSharingDialogOpen] = useState(false);
  const [selectedDesignForSharing, setSelectedDesignForSharing] = useState<Design | null>(null);
  
  // Filters state
  const [filters, setFilters] = useState<IDesignFilters>({
    search: '',
    status: [],
    material: [],
    dateRange: {},
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleProfile = () => {
    handleClose();
    navigate('/profile');
  };

  const handleLogout = async () => {
    handleClose();
    await logout();
    navigate('/login');
  };

  const getInitials = (username: string) => {
    return username.substring(0, 2).toUpperCase();
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Load designs data
  const loadDesigns = useCallback(async () => {
    if (currentTab !== 1) return; // Only load for My Designs tab
    
    setLoading(true);
    setError(null);
    
    try {
      const userDesigns = await DesignService.getUserDesigns(filters);
      setDesigns(userDesigns);
    } catch (err) {
      setError('Failed to load designs');
      console.error('Error loading designs:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, currentTab]);

  // Load shared designs
  const loadSharedDesigns = useCallback(async () => {
    if (currentTab !== 2) return; // Only load for Shared tab
    
    setLoading(true);
    setError(null);
    
    try {
      const shared = await DesignService.getSharedDesigns();
      setSharedDesigns(shared);
    } catch (err) {
      setError('Failed to load shared designs');
      console.error('Error loading shared designs:', err);
    } finally {
      setLoading(false);
    }
  }, [currentTab]);

  // Load recent designs and stats
  const loadDashboardData = useCallback(async () => {
    try {
      const [recent, stats] = await Promise.all([
        DesignService.getRecentDesigns(),
        DesignService.getDesignStats()
      ]);
      setRecentDesigns(recent);
      setDesignStats(stats);
    } catch (err) {
      console.error('Error loading dashboard data:', err);
    }
  }, []);

  // Load data when tab changes
  useEffect(() => {
    if (currentTab === 1) {
      loadDesigns();
    } else if (currentTab === 2) {
      loadSharedDesigns();
    }
  }, [currentTab, loadDesigns, loadSharedDesigns]);

  // Load dashboard data on mount
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Handle design actions
  const handleDesignAction = async (action: DesignAction) => {
    try {
      switch (action.type) {
        case 'rename':
          await DesignService.renameDesign({
            id: action.designId,
            newName: action.payload.newName
          });
          break;
        
        case 'delete':
          await DesignService.deleteDesign(action.designId);
          break;
        
        case 'duplicate':
          await DesignService.duplicateDesign({
            originalId: action.designId,
            newName: action.payload.newName
          });
          break;
        
        case 'share':
          // Open sharing dialog instead of direct sharing
          const designToShare = designs.find(d => d.id === action.designId) || 
                               sharedDesigns.find(d => d.id === action.designId);
          if (designToShare) {
            setSelectedDesignForSharing(designToShare);
            setSharingDialogOpen(true);
          }
          return; // Don't reload data here, dialog will handle it
        
        case 'download':
          const blob = await DesignService.downloadDesign(action.designId, action.payload.format);
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `design-${action.designId}-${action.payload.format}.zip`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
          break;
      }
      
      // Reload data after action
      if (currentTab === 1) {
        loadDesigns();
      } else if (currentTab === 2) {
        loadSharedDesigns();
      }
      loadDashboardData();
      
    } catch (err) {
      setError(`Failed to ${action.type} design`);
      console.error(`Error ${action.type} design:`, err);
    }
  };

  // Handle design click (navigate to design view)
  const handleDesignClick = (design: Design) => {
    navigate(`/design/${design.id}`);
  };

  // Handle filters change
  const handleFiltersChange = (newFilters: IDesignFilters) => {
    setFilters(newFilters);
  };

  // Refresh data
  const handleRefresh = () => {
    if (currentTab === 1) {
      loadDesigns();
    } else if (currentTab === 2) {
      loadSharedDesigns();
    }
    loadDashboardData();
  };

  const renderTabContent = () => {
    switch (currentTab) {
      case 0:
        return <UploadContainer />;
      case 1:
        return (
          <Box>
            {/* Header with refresh button */}
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Box>
                <Typography variant="h4" gutterBottom>
                  My Designs
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage your uploaded designs and analysis results
                </Typography>
              </Box>
              <IconButton onClick={handleRefresh} color="primary">
                <Refresh />
              </IconButton>
            </Box>

            {/* Statistics Dashboard */}
            {designStats && (
              <Box mb={4}>
                <DesignStats stats={designStats} loading={!designStats} />
              </Box>
            )}

            {/* Filters */}
            <DesignFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              designCount={designs.length}
            />

            {/* Design Gallery */}
            <DesignGallery
              designs={designs}
              onDesignAction={handleDesignAction}
              onDesignClick={handleDesignClick}
              loading={loading}
            />
          </Box>
        );
      case 2:
        return (
          <Box>
            {/* Header */}
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Box>
                <Typography variant="h4" gutterBottom>
                  Shared Designs
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Designs shared with you by other users
                </Typography>
              </Box>
              <IconButton onClick={handleRefresh} color="primary">
                <Refresh />
              </IconButton>
            </Box>

            {/* Shared Design Gallery */}
            <DesignGallery
              designs={sharedDesigns}
              onDesignAction={handleDesignAction}
              onDesignClick={handleDesignClick}
              loading={loading}
            />
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h4" gutterBottom>
              Analytics
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Detailed analytics and usage statistics
            </Typography>
            
            {designStats && (
              <DesignStats stats={designStats} loading={!designStats} />
            )}
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Box>
      {/* App Bar */}
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            3D BOM Generator
          </Typography>
          
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="body2">
              Welcome, {user?.username}
            </Typography>
            
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              color="inherit"
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.dark' }}>
                {user ? getInitials(user.username) : <AccountCircle />}
              </Avatar>
            </IconButton>
            
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              <MenuItem onClick={handleProfile}>
                <Person sx={{ mr: 1 }} />
                Profile
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <ExitToApp sx={{ mr: 1 }} />
                Logout
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box>
        {/* Navigation Tabs */}
        <Paper square elevation={1}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab
              icon={<CloudUpload />}
              label="Upload"
              iconPosition="start"
            />
            <Tab
              icon={<DashboardIcon />}
              label="My Designs"
              iconPosition="start"
            />
            <Tab
              icon={<Share />}
              label="Shared"
              iconPosition="start"
            />
            <Tab
              icon={<Analytics />}
              label="Analytics"
              iconPosition="start"
            />
          </Tabs>
        </Paper>

        {/* Tab Content */}
        <Box p={3}>
          {renderTabContent()}
        </Box>

        {/* Admin Panel */}
        {user?.isAdmin && (
          <Box p={3}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Administrator Tools
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Manage users, system settings, and monitor application usage.
              </Typography>
              <Button variant="contained" color="secondary">
                Admin Panel
              </Button>
            </Paper>
          </Box>
        )}
      </Box>

      {/* Error Snackbar */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      {/* Sharing Dialog */}
      {selectedDesignForSharing && (
        <SharingDialog
          open={sharingDialogOpen}
          onClose={() => {
            setSharingDialogOpen(false);
            setSelectedDesignForSharing(null);
            // Refresh data after sharing changes
            if (currentTab === 1) {
              loadDesigns();
            } else if (currentTab === 2) {
              loadSharedDesigns();
            }
          }}
          designId={selectedDesignForSharing.id}
          designName={selectedDesignForSharing.name}
        />
      )}
    </Box>
  );
};