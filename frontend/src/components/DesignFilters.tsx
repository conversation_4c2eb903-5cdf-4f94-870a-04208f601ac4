import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  Paper,
  Grid,
  Button,
  Collapse,
  IconButton,
  Typography,
  Divider
} from '@mui/material';
import {
  Search,
  FilterList,
  Clear,
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DesignFilters as IDesignFilters } from '../types/design';
import { DesignService } from '../services/designService';

interface DesignFiltersProps {
  filters: IDesignFilters;
  onFiltersChange: (filters: IDesignFilters) => void;
  designCount: number;
}

const STATUS_OPTIONS = [
  { value: 'uploaded', label: 'Uploaded' },
  { value: 'processing', label: 'Processing' },
  { value: 'completed', label: 'Completed' },
  { value: 'error', label: 'Error' }
];

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'createdAt', label: 'Created Date' },
  { value: 'updatedAt', label: 'Updated Date' },
  { value: 'status', label: 'Status' }
];

export const DesignFilters: React.FC<DesignFiltersProps> = ({
  filters,
  onFiltersChange,
  designCount
}) => {
  const [expanded, setExpanded] = useState(false);
  const [availableMaterials, setAvailableMaterials] = useState<string[]>([]);

  useEffect(() => {
    // Load available materials for filtering
    const loadMaterials = async () => {
      try {
        const materials = await DesignService.getAvailableMaterials();
        setAvailableMaterials(materials);
      } catch (error) {
        console.error('Error loading materials:', error);
      }
    };
    loadMaterials();
  }, []);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFiltersChange({
      ...filters,
      search: event.target.value
    });
  };

  const handleStatusChange = (event: any) => {
    const value = event.target.value;
    onFiltersChange({
      ...filters,
      status: typeof value === 'string' ? value.split(',') : value
    });
  };

  const handleMaterialChange = (event: any) => {
    const value = event.target.value;
    onFiltersChange({
      ...filters,
      material: typeof value === 'string' ? value.split(',') : value
    });
  };

  const handleSortChange = (field: 'sortBy' | 'sortOrder', value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value
    });
  };

  const handleDateChange = (field: 'start' | 'end', date: Date | null) => {
    onFiltersChange({
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [field]: date ? date.toISOString().split('T')[0] : undefined
      }
    });
  };

  const handleClearFilters = () => {
    onFiltersChange({
      search: '',
      status: [],
      material: [],
      dateRange: {},
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
  };

  const hasActiveFilters = 
    filters.search ||
    filters.status.length > 0 ||
    filters.material.length > 0 ||
    filters.dateRange.start ||
    filters.dateRange.end;

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Paper sx={{ p: 2, mb: 3 }}>
        {/* Search and basic controls */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search designs..."
              value={filters.search}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Sort By</InputLabel>
              <Select
                value={filters.sortBy}
                label="Sort By"
                onChange={(e) => handleSortChange('sortBy', e.target.value)}
              >
                {SORT_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Order</InputLabel>
              <Select
                value={filters.sortOrder}
                label="Order"
                onChange={(e) => handleSortChange('sortOrder', e.target.value)}
              >
                <MenuItem value="asc">Ascending</MenuItem>
                <MenuItem value="desc">Descending</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={1}>
            <Box display="flex" gap={1}>
              <IconButton
                onClick={() => setExpanded(!expanded)}
                color={hasActiveFilters ? 'primary' : 'default'}
              >
                <FilterList />
              </IconButton>
              {hasActiveFilters && (
                <IconButton onClick={handleClearFilters} color="error">
                  <Clear />
                </IconButton>
              )}
            </Box>
          </Grid>
        </Grid>

        {/* Advanced filters */}
        <Collapse in={expanded}>
          <Divider sx={{ my: 2 }} />
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  multiple
                  value={filters.status}
                  onChange={handleStatusChange}
                  input={<OutlinedInput label="Status" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip
                          key={value}
                          label={STATUS_OPTIONS.find(opt => opt.value === value)?.label || value}
                          size="small"
                        />
                      ))}
                    </Box>
                  )}
                >
                  {STATUS_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Material</InputLabel>
                <Select
                  multiple
                  value={filters.material}
                  onChange={handleMaterialChange}
                  input={<OutlinedInput label="Material" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {availableMaterials.map((material) => (
                    <MenuItem key={material} value={material}>
                      {material}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box display="flex" gap={1}>
                <DatePicker
                  label="From Date"
                  value={filters.dateRange.start ? new Date(filters.dateRange.start) : null}
                  onChange={(date) => handleDateChange('start', date)}
                  slotProps={{
                    textField: { size: 'medium', sx: { flex: 1 } }
                  }}
                />
                <DatePicker
                  label="To Date"
                  value={filters.dateRange.end ? new Date(filters.dateRange.end) : null}
                  onChange={(date) => handleDateChange('end', date)}
                  slotProps={{
                    textField: { size: 'medium', sx: { flex: 1 } }
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Collapse>

        {/* Results count */}
        <Box mt={2} display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="body2" color="text.secondary">
            {designCount} design{designCount !== 1 ? 's' : ''} found
          </Typography>
          
          {hasActiveFilters && (
            <Button
              size="small"
              onClick={handleClearFilters}
              startIcon={<Clear />}
            >
              Clear Filters
            </Button>
          )}
        </Box>
      </Paper>
    </LocalizationProvider>
  );
};