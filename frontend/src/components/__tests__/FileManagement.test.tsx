import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { FileManagement } from '../FileManagement';
import { UploadedFile } from '../../types/file';

// Mock file for testing
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

const createMockUploadedFile = (overrides: Partial<UploadedFile> = {}): UploadedFile => ({
  id: '1',
  name: 'test.pdf',
  size: 1024,
  type: 'application/pdf',
  lastModified: Date.now(),
  file: createMockFile('test.pdf', 1024, 'application/pdf'),
  status: 'completed',
  progress: 100,
  ...overrides
});

const mockProps = {
  files: [] as UploadedFile[],
  onRename: jest.fn(),
  onDuplicate: jest.fn(),
  onRemove: jest.fn()
};

describe('FileManagement Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders nothing when no files are provided', () => {
    const { container } = render(<FileManagement {...mockProps} files={[]} />);
    expect(container.firstChild).toBeNull();
  });

  it('renders manage files button when files exist', () => {
    const files = [createMockUploadedFile()];
    render(<FileManagement {...mockProps} files={files} />);
    
    expect(screen.getByText('Manage Files (1)')).toBeInTheDocument();
    expect(screen.getByText('Clear All')).toBeInTheDocument();
  });

  it('opens file management dialog when manage button is clicked', async () => {
    const files = [createMockUploadedFile()];
    render(<FileManagement {...mockProps} files={files} />);
    
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('File Management')).toBeInTheDocument();
  });

  it('displays file list in management dialog', async () => {
    const files = [
      createMockUploadedFile({
        id: '1',
        name: 'file1.pdf',
        size: 1024,
        status: 'completed'
      }),
      createMockUploadedFile({
        id: '2',
        name: 'file2.png',
        size: 2048,
        status: 'processing'
      })
    ];
    
    render(<FileManagement {...mockProps} files={files} />);
    
    const manageButton = screen.getByText('Manage Files (2)');
    await userEvent.click(manageButton);
    
    expect(screen.getByText('file1.pdf')).toBeInTheDocument();
    expect(screen.getByText('file2.png')).toBeInTheDocument();
    expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    expect(screen.getByText('PROCESSING')).toBeInTheDocument();
  });

  it('opens rename dialog when rename button is clicked', async () => {
    const files = [createMockUploadedFile({ name: 'original.pdf' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open management dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    // Click rename button
    const renameButton = screen.getByTitle('Rename file');
    await userEvent.click(renameButton);
    
    expect(screen.getByText('Rename File')).toBeInTheDocument();
    expect(screen.getByDisplayValue('original.pdf')).toBeInTheDocument();
  });

  it('handles file rename with validation', async () => {
    const files = [createMockUploadedFile({ name: 'original.pdf' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open management dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    // Open rename dialog
    const renameButton = screen.getByTitle('Rename file');
    await userEvent.click(renameButton);
    
    // Change name
    const nameInput = screen.getByDisplayValue('original.pdf');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, 'renamed.pdf');
    
    // Confirm rename
    const confirmButton = screen.getByText('Rename');
    await userEvent.click(confirmButton);
    
    expect(mockProps.onRename).toHaveBeenCalledWith('1', 'renamed.pdf');
  });

  it('validates empty file names', async () => {
    const files = [createMockUploadedFile()];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialogs
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const renameButton = screen.getByTitle('Rename file');
    await userEvent.click(renameButton);
    
    // Clear name
    const nameInput = screen.getByDisplayValue('test.pdf');
    await userEvent.clear(nameInput);
    
    expect(screen.getByText('File name cannot be empty')).toBeInTheDocument();
    
    const confirmButton = screen.getByText('Rename');
    expect(confirmButton).toBeDisabled();
  });

  it('validates duplicate file names', async () => {
    const files = [
      createMockUploadedFile({ id: '1', name: 'file1.pdf' }),
      createMockUploadedFile({ id: '2', name: 'file2.pdf' })
    ];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialogs
    const manageButton = screen.getByText('Manage Files (2)');
    await userEvent.click(manageButton);
    
    const renameButtons = screen.getAllByTitle('Rename file');
    await userEvent.click(renameButtons[0]);
    
    // Try to rename to existing name
    const nameInput = screen.getByDisplayValue('file1.pdf');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, 'file2.pdf');
    
    expect(screen.getByText('A file with this name already exists')).toBeInTheDocument();
  });

  it('validates same file name', async () => {
    const files = [createMockUploadedFile({ name: 'test.pdf' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialogs
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const renameButton = screen.getByTitle('Rename file');
    await userEvent.click(renameButton);
    
    // Try to confirm without changing name
    const confirmButton = screen.getByText('Rename');
    await userEvent.click(confirmButton);
    
    expect(screen.getByText('New name must be different from current name')).toBeInTheDocument();
  });

  it('cancels rename dialog', async () => {
    const files = [createMockUploadedFile()];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialogs
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const renameButton = screen.getByTitle('Rename file');
    await userEvent.click(renameButton);
    
    expect(screen.getByText('Rename File')).toBeInTheDocument();
    
    // Cancel
    const cancelButton = screen.getByText('Cancel');
    await userEvent.click(cancelButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Rename File')).not.toBeInTheDocument();
    });
    
    expect(mockProps.onRename).not.toHaveBeenCalled();
  });

  it('handles file duplication', async () => {
    const files = [createMockUploadedFile({ status: 'completed' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open management dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    // Click duplicate button
    const duplicateButton = screen.getByTitle('Duplicate file');
    await userEvent.click(duplicateButton);
    
    expect(mockProps.onDuplicate).toHaveBeenCalledWith('1');
  });

  it('disables duplicate button for non-completed files', async () => {
    const files = [createMockUploadedFile({ status: 'processing' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open management dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const duplicateButton = screen.getByTitle('Duplicate file');
    expect(duplicateButton).toBeDisabled();
  });

  it('opens delete confirmation dialog', async () => {
    const files = [createMockUploadedFile({ name: 'to-delete.pdf' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open management dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    // Click delete button
    const deleteButton = screen.getByTitle('Remove file');
    await userEvent.click(deleteButton);
    
    expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to delete "to-delete.pdf"?')).toBeInTheDocument();
  });

  it('confirms file deletion', async () => {
    const files = [createMockUploadedFile()];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialogs
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const deleteButton = screen.getByTitle('Remove file');
    await userEvent.click(deleteButton);
    
    // Confirm deletion
    const confirmButton = screen.getByText('Delete');
    await userEvent.click(confirmButton);
    
    expect(mockProps.onRemove).toHaveBeenCalledWith('1');
  });

  it('cancels file deletion', async () => {
    const files = [createMockUploadedFile()];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialogs
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const deleteButton = screen.getByTitle('Remove file');
    await userEvent.click(deleteButton);
    
    // Cancel deletion
    const cancelButton = screen.getByText('Cancel');
    await userEvent.click(cancelButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Confirm Delete')).not.toBeInTheDocument();
    });
    
    expect(mockProps.onRemove).not.toHaveBeenCalled();
  });

  it('shows warning for completed files in delete dialog', async () => {
    const files = [createMockUploadedFile({ status: 'completed' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialogs
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const deleteButton = screen.getByTitle('Remove file');
    await userEvent.click(deleteButton);
    
    expect(screen.getByText('This will also remove any analysis results and generated models.')).toBeInTheDocument();
  });

  it('handles clear all files', async () => {
    const files = [
      createMockUploadedFile({ id: '1' }),
      createMockUploadedFile({ id: '2' })
    ];
    render(<FileManagement {...mockProps} files={files} />);
    
    const clearAllButton = screen.getByText('Clear All');
    await userEvent.click(clearAllButton);
    
    expect(mockProps.onRemove).toHaveBeenCalledTimes(2);
    expect(mockProps.onRemove).toHaveBeenCalledWith('1');
    expect(mockProps.onRemove).toHaveBeenCalledWith('2');
  });

  it('disables rename button for uploading files', async () => {
    const files = [createMockUploadedFile({ status: 'uploading' })];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open management dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    const renameButton = screen.getByTitle('Rename file');
    expect(renameButton).toBeDisabled();
  });

  it('displays correct file information in list', async () => {
    const files = [createMockUploadedFile({
      name: 'detailed-file.pdf',
      size: 2048,
      type: 'application/pdf',
      lastModified: new Date('2023-01-01').getTime()
    })];
    
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open management dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    expect(screen.getByText('detailed-file.pdf')).toBeInTheDocument();
    expect(screen.getByText('Size: 2 KB')).toBeInTheDocument();
    expect(screen.getByText('Type: application/pdf')).toBeInTheDocument();
    expect(screen.getByText('Modified: 1/1/2023')).toBeInTheDocument();
  });

  it('closes management dialog', async () => {
    const files = [createMockUploadedFile()];
    render(<FileManagement {...mockProps} files={files} />);
    
    // Open dialog
    const manageButton = screen.getByText('Manage Files (1)');
    await userEvent.click(manageButton);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    
    // Close dialog
    const closeButton = screen.getByText('Close');
    await userEvent.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });
});