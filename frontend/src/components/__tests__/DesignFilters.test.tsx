import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DesignFilters } from '../DesignFilters';
import { DesignFilters as IDesignFilters } from '../../types/design';
import { DesignService } from '../../services/designService';

// Mock the DesignService
jest.mock('../../services/designService');
const mockDesignService = DesignService as jest.Mocked<typeof DesignService>;

// Mock date picker components
jest.mock('@mui/x-date-pickers/DatePicker', () => ({
  DatePicker: ({ label, onChange, value }: any) => (
    <input
      data-testid={`date-picker-${label.toLowerCase().replace(' ', '-')}`}
      type="date"
      onChange={(e) => onChange(e.target.value ? new Date(e.target.value) : null)}
      value={value ? value.toISOString().split('T')[0] : ''}
    />
  )
}));

jest.mock('@mui/x-date-pickers/LocalizationProvider', () => ({
  LocalizationProvider: ({ children }: any) => <div>{children}</div>
}));

jest.mock('@mui/x-date-pickers/AdapterDateFns', () => ({
  AdapterDateFns: jest.fn()
}));

const mockFilters: IDesignFilters = {
  search: '',
  status: [],
  material: [],
  dateRange: {},
  sortBy: 'createdAt',
  sortOrder: 'desc'
};

const mockMaterials = ['Steel', 'Aluminum', 'Plastic', 'Brass'];

describe('DesignFilters', () => {
  const mockOnFiltersChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockDesignService.getAvailableMaterials.mockResolvedValue(mockMaterials);
  });

  it('renders search input', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    expect(screen.getByPlaceholderText('Search designs...')).toBeInTheDocument();
  });

  it('renders sort controls', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Use getAllByText to handle multiple elements with same text
    expect(screen.getAllByText('Sort By')[0]).toBeInTheDocument();
    expect(screen.getAllByText('Order')[0]).toBeInTheDocument();
  });

  it('displays design count', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={5}
      />
    );

    expect(screen.getByText('5 designs found')).toBeInTheDocument();
  });

  it('displays singular form for single design', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={1}
      />
    );

    expect(screen.getByText('1 design found')).toBeInTheDocument();
  });

  it('handles search input changes', async () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    const searchInput = screen.getByPlaceholderText('Search designs...');
    fireEvent.change(searchInput, { target: { value: 'test design' } });

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      ...mockFilters,
      search: 'test design'
    });
  });

  it('renders sort by options', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Check that the sort by select shows the current value
    expect(screen.getByText('Created Date')).toBeInTheDocument();
  });

  it('renders sort order options', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Check that the sort order select shows the current value
    expect(screen.getByText('Descending')).toBeInTheDocument();
  });

  it('expands advanced filters when filter button clicked', async () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Find the filter icon button by its icon
    const filterButton = screen.getByTestId('FilterListIcon').closest('button');
    expect(filterButton).toBeInTheDocument();
    
    fireEvent.click(filterButton!);

    await waitFor(() => {
      // Use getAllByText to handle multiple elements with same text
      expect(screen.getAllByText('Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Material')[0]).toBeInTheDocument();
    });
  });

  it('loads available materials on mount', async () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    await waitFor(() => {
      expect(mockDesignService.getAvailableMaterials).toHaveBeenCalled();
    });
  });

  it('renders with active filters', () => {
    const filtersWithData: IDesignFilters = {
      search: 'test',
      status: ['completed'],
      material: ['Steel'],
      dateRange: { start: '2024-01-01' },
      sortBy: 'name',
      sortOrder: 'asc'
    };

    render(
      <DesignFilters
        filters={filtersWithData}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Should show search value
    expect(screen.getByDisplayValue('test')).toBeInTheDocument();
  });

  it('renders filter button', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Should have a filter button (icon button)
    const filterIcon = screen.getByTestId('FilterListIcon');
    expect(filterIcon).toBeInTheDocument();
  });

  it('clears all filters when clear button clicked', async () => {
    const filtersWithData: IDesignFilters = {
      search: 'test',
      status: ['completed'],
      material: ['Steel'],
      dateRange: { start: '2024-01-01' },
      sortBy: 'name',
      sortOrder: 'asc'
    };

    render(
      <DesignFilters
        filters={filtersWithData}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    const clearButton = screen.getByRole('button', { name: /clear/i });
    fireEvent.click(clearButton);

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      search: '',
      status: [],
      material: [],
      dateRange: {},
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
  });

  it('shows clear filters button when filters are active', () => {
    const filtersWithData: IDesignFilters = {
      search: 'test',
      status: [],
      material: [],
      dateRange: {},
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    render(
      <DesignFilters
        filters={filtersWithData}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    expect(screen.getByText('Clear Filters')).toBeInTheDocument();
  });

  it('shows active filter state', () => {
    const filtersWithData: IDesignFilters = {
      search: 'test',
      status: [],
      material: [],
      dateRange: {},
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    render(
      <DesignFilters
        filters={filtersWithData}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Should show clear filters button when filters are active
    expect(screen.getByText('Clear Filters')).toBeInTheDocument();
  });

  it('renders component without errors', () => {
    render(
      <DesignFilters
        filters={mockFilters}
        onFiltersChange={mockOnFiltersChange}
        designCount={10}
      />
    );

    // Basic rendering test - should not throw errors
    expect(screen.getByPlaceholderText('Search designs...')).toBeInTheDocument();
    expect(screen.getByText('10 designs found')).toBeInTheDocument();
  });
});