import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ResultsDisplay from '../ResultsDisplay';
import { Design, BOMItem, Model3D, AnalysisResult } from '../../types/design';

// Mock the 3D viewer component
jest.mock('../Model3DViewer', () => {
  return function MockModel3DViewer() {
    return <div data-testid="model-3d-viewer">3D Model Viewer</div>;
  };
});

// Mock the sharing dialog
jest.mock('../SharingDialog', () => {
  return function MockSharingDialog({ open, onClose }: any) {
    if (!open) return null;
    return (
      <div data-testid="sharing-dialog">
        <div>Sharing Dialog</div>
        <button onClick={onClose}>Close</button>
      </div>
    );
  };
});

const theme = createTheme();

const mockBOMItems: BOMItem[] = [
  {
    id: '1',
    designId: 'design-1',
    partNumber: 'PART-001',
    description: 'Main Housing',
    quantity: 1,
    material: 'Aluminum 6061',
    volume: 0.001,
    weight: 2.7,
    unitWeight: 2.7
  },
  {
    id: '2',
    designId: 'design-1',
    partNumber: 'PART-002',
    description: 'Mounting Bracket',
    quantity: 2,
    material: 'Steel A36',
    volume: 0.0005,
    weight: 7.85,
    unitWeight: 3.925
  }
];

const mockModel3D: Model3D = {
  id: 'model-1',
  designId: 'design-1',
  modelFilePath: '/models/design-1.stl',
  createdAt: '2025-01-18T10:00:00Z'
};

const mockAnalysisResult: AnalysisResult = {
  id: 'analysis-1',
  designId: 'design-1',
  analysisData: { features: 15, dimensions: 8 },
  confidenceScore: 0.85,
  processingTime: 120,
  createdAt: '2025-01-18T09:30:00Z'
};

const mockDesign: Design = {
  id: 'design-1',
  userId: 1,
  name: 'Test Assembly',
  originalFilename: 'test-drawing.pdf',
  filePath: '/uploads/test-drawing.pdf',
  status: 'completed',
  createdAt: '2025-01-18T09:00:00Z',
  updatedAt: '2025-01-18T10:30:00Z',
  fileSize: 2048000,
  fileType: 'application/pdf',
  analysisResults: mockAnalysisResult,
  bomItems: mockBOMItems,
  model3D: mockModel3D,
  processingProgress: {
    stage: 'complete',
    progress: 100,
    message: 'Analysis complete',
    completedAt: '2025-01-18T10:30:00Z'
  },
  isShared: false,
  viewCount: 5,
  lastViewed: '2025-01-18T10:00:00Z'
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Mock fetch for API calls
global.fetch = jest.fn();

describe('ResultsDisplay Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  describe('Basic Rendering', () => {
    it('renders the main results display with design information', () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      expect(screen.getByText('Test Assembly')).toBeInTheDocument();
      expect(screen.getByText('completed')).toBeInTheDocument();
      expect(screen.getByText((content, element) => {
        return element?.textContent === 'File: test-drawing.pdf';
      })).toBeInTheDocument();
    });

    it('displays confidence score alert', () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      expect(screen.getByText(/Analysis Confidence: 85.0%/)).toBeInTheDocument();
    });

    it('renders all navigation tabs', () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      expect(screen.getByRole('tab', { name: /Overview/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /Bill of Materials/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /Weight Analysis/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /3D Model/i })).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    it('switches to BOM tab and displays table', async () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      fireEvent.click(screen.getByRole('tab', { name: /Bill of Materials/i }));

      await waitFor(() => {
        expect(screen.getByText('PART-001')).toBeInTheDocument();
        expect(screen.getByText('Main Housing')).toBeInTheDocument();
      });
    });

    it('switches to Weight Analysis tab', async () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      fireEvent.click(screen.getByRole('tab', { name: /Weight Analysis/i }));

      await waitFor(() => {
        expect(screen.getByText('Total Assembly Weight')).toBeInTheDocument();
      });
    });

    it('switches to 3D Model tab and displays viewer', async () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      fireEvent.click(screen.getByRole('tab', { name: /3D Model/i }));

      await waitFor(() => {
        expect(screen.getByTestId('model-3d-viewer')).toBeInTheDocument();
      });
    });
  });

  describe('BOM Functionality', () => {
    it('allows searching in BOM table', async () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      fireEvent.click(screen.getByRole('tab', { name: /Bill of Materials/i }));

      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText('Search parts, descriptions, or materials...');
        fireEvent.change(searchInput, { target: { value: 'Housing' } });
      });

      await waitFor(() => {
        expect(screen.getByText('Main Housing')).toBeInTheDocument();
      });
    });

    it('provides export options for BOM', async () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      fireEvent.click(screen.getByRole('tab', { name: /Bill of Materials/i }));

      await waitFor(() => {
        expect(screen.getByText('CSV')).toBeInTheDocument();
        expect(screen.getByText('Excel')).toBeInTheDocument();
        expect(screen.getByText('PDF')).toBeInTheDocument();
      });
    });
  });

  describe('Sharing Functionality', () => {
    it('opens sharing dialog when share button is clicked', async () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      fireEvent.click(screen.getByText('Share'));

      await waitFor(() => {
        expect(screen.getByTestId('sharing-dialog')).toBeInTheDocument();
      });
    });
  });

  describe('Print Functionality', () => {
    it('opens print dialog when print button is clicked', async () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      fireEvent.click(screen.getByText('Print'));

      await waitFor(() => {
        expect(screen.getByText('Print Report')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles missing 3D model gracefully', async () => {
      const designWithoutModel = {
        ...mockDesign,
        model3D: undefined
      };

      renderWithProviders(<ResultsDisplay design={designWithoutModel} />);

      fireEvent.click(screen.getByRole('tab', { name: /3D Model/i }));

      await waitFor(() => {
        expect(screen.getByText('3D Model Not Available')).toBeInTheDocument();
      });
    });

    it('handles empty BOM gracefully', async () => {
      const designWithoutBOM = {
        ...mockDesign,
        bomItems: []
      };

      renderWithProviders(<ResultsDisplay design={designWithoutBOM} />);

      fireEvent.click(screen.getByRole('tab', { name: /Bill of Materials/i }));

      await waitFor(() => {
        expect(screen.getByText('No parts found in BOM')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation', () => {
    it('calls onBack when back button is clicked', () => {
      const mockOnBack = jest.fn();
      renderWithProviders(<ResultsDisplay design={mockDesign} onBack={mockOnBack} />);

      fireEvent.click(screen.getByText('Back'));

      expect(mockOnBack).toHaveBeenCalled();
    });

    it('does not show back button when onBack is not provided', () => {
      renderWithProviders(<ResultsDisplay design={mockDesign} />);

      expect(screen.queryByText('Back')).not.toBeInTheDocument();
    });
  });
});

describe('Integration Tests', () => {
  it('completes full results viewing workflow', async () => {
    renderWithProviders(<ResultsDisplay design={mockDesign} />);

    // 1. View overview
    expect(screen.getByText('Test Assembly')).toBeInTheDocument();

    // 2. Navigate to BOM
    fireEvent.click(screen.getByRole('tab', { name: /Bill of Materials/i }));
    await waitFor(() => {
      expect(screen.getByText('PART-001')).toBeInTheDocument();
    });

    // 3. Navigate to Weight Analysis
    fireEvent.click(screen.getByRole('tab', { name: /Weight Analysis/i }));
    await waitFor(() => {
      expect(screen.getByText('Total Assembly Weight')).toBeInTheDocument();
    });

    // 4. Navigate to 3D Model
    fireEvent.click(screen.getByRole('tab', { name: /3D Model/i }));
    await waitFor(() => {
      expect(screen.getByTestId('model-3d-viewer')).toBeInTheDocument();
    });

    // 5. Test sharing
    fireEvent.click(screen.getByText('Share'));
    await waitFor(() => {
      expect(screen.getByTestId('sharing-dialog')).toBeInTheDocument();
    });
  });

  it('handles workflow with missing data gracefully', async () => {
    const incompleteDesign = {
      ...mockDesign,
      bomItems: [],
      model3D: undefined,
      analysisResults: undefined
    };

    renderWithProviders(<ResultsDisplay design={incompleteDesign} />);

    // Should still render without errors
    expect(screen.getByText('Test Assembly')).toBeInTheDocument();

    // Navigate to BOM tab
    fireEvent.click(screen.getByRole('tab', { name: /Bill of Materials/i }));
    await waitFor(() => {
      expect(screen.getByText('No parts found in BOM')).toBeInTheDocument();
    });

    // Navigate to 3D Model tab
    fireEvent.click(screen.getByRole('tab', { name: /3D Model/i }));
    await waitFor(() => {
      expect(screen.getByText('3D Model Not Available')).toBeInTheDocument();
    });
  });
});