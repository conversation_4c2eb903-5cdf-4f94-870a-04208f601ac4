import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import SharingDialog from '../SharingDialog';
import sharingService from '../../services/sharingService';

// Mock the sharing service
jest.mock('../../services/sharingService');
const mockSharingService = sharingService as jest.Mocked<typeof sharingService>;

describe('SharingDialog', () => {
  const defaultProps = {
    open: true,
    onClose: jest.fn(),
    designId: 1,
    designName: 'Test Design',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders dialog with design name', () => {
    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: [],
    });

    render(<SharingDialog {...defaultProps} />);

    expect(screen.getByText('Share "Test Design"')).toBeInTheDocument();
  });

  it('loads and displays existing shares', async () => {
    const mockShares = [
      {
        share_id: 1,
        user_id: 2,
        username: 'testuser',
        email: '<EMAIL>',
        permission_level: 'view',
        shared_at: '2023-01-01T00:00:00Z',
      },
    ];

    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: mockShares,
    });

    render(<SharingDialog {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('testuser')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('view')).toBeInTheDocument();
    });
  });

  it('searches for users when typing in autocomplete', async () => {
    const user = userEvent.setup();
    
    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: [],
    });

    mockSharingService.searchUsers.mockResolvedValue({
      query: 'test',
      users: [
        {
          user_id: 2,
          username: 'testuser',
          email: '<EMAIL>',
        },
      ],
    });

    render(<SharingDialog {...defaultProps} />);

    const searchInput = screen.getByLabelText('Search users by username or email');
    await user.type(searchInput, 'test');

    await waitFor(() => {
      expect(mockSharingService.searchUsers).toHaveBeenCalledWith('test');
    });
  });

  it('grants access when share button is clicked', async () => {
    const user = userEvent.setup();
    
    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: [],
    });

    mockSharingService.searchUsers.mockResolvedValue({
      query: 'test',
      users: [
        {
          user_id: 2,
          username: 'testuser',
          email: '<EMAIL>',
        },
      ],
    });

    mockSharingService.grantAccess.mockResolvedValue({
      success: true,
      message: 'Design shared successfully',
      share: {
        share_id: 1,
        design_id: 1,
        shared_with_user_id: 2,
        shared_with_username: 'testuser',
        shared_with_email: '<EMAIL>',
        permission_level: 'view',
        shared_at: '2023-01-01T00:00:00Z',
      },
    });

    render(<SharingDialog {...defaultProps} />);

    // Search for user
    const searchInput = screen.getByLabelText('Search users by username or email');
    await user.type(searchInput, 'test');

    // Wait for search results and select user
    await waitFor(() => {
      const option = screen.getByText('testuser (<EMAIL>)');
      fireEvent.click(option);
    });

    // Click share button
    const shareButton = screen.getByRole('button', { name: /share/i });
    await user.click(shareButton);

    await waitFor(() => {
      expect(mockSharingService.grantAccess).toHaveBeenCalledWith({
        design_id: 1,
        shared_with_email: '<EMAIL>',
        permission_level: 'view',
      });
    });
  });

  it('revokes access when delete button is clicked', async () => {
    const user = userEvent.setup();
    
    const mockShares = [
      {
        share_id: 1,
        user_id: 2,
        username: 'testuser',
        email: '<EMAIL>',
        permission_level: 'view',
        shared_at: '2023-01-01T00:00:00Z',
      },
    ];

    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: mockShares,
    });

    mockSharingService.revokeAccess.mockResolvedValue({
      success: true,
      message: 'Access revoked successfully',
    });

    render(<SharingDialog {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('testuser')).toBeInTheDocument();
    });

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    await user.click(deleteButton);

    await waitFor(() => {
      expect(mockSharingService.revokeAccess).toHaveBeenCalledWith(1, 2);
    });
  });

  it('updates permission when permission dropdown is changed', async () => {
    const user = userEvent.setup();
    
    const mockShares = [
      {
        share_id: 1,
        user_id: 2,
        username: 'testuser',
        email: '<EMAIL>',
        permission_level: 'view',
        shared_at: '2023-01-01T00:00:00Z',
      },
    ];

    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: mockShares,
    });

    mockSharingService.updatePermission.mockResolvedValue({
      success: true,
      message: 'Permission updated successfully',
      share: {
        share_id: 1,
        design_id: 1,
        shared_with_user_id: 2,
        shared_with_username: 'testuser',
        shared_with_email: '<EMAIL>',
        permission_level: 'edit',
        shared_at: '2023-01-01T00:00:00Z',
      },
    });

    render(<SharingDialog {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('testuser')).toBeInTheDocument();
    });

    // Find and click the permission dropdown
    const permissionSelect = screen.getByDisplayValue('view');
    await user.click(permissionSelect);

    // Select edit option
    const editOption = screen.getByRole('option', { name: 'edit' });
    await user.click(editOption);

    await waitFor(() => {
      expect(mockSharingService.updatePermission).toHaveBeenCalledWith(1, 2, 'edit');
    });
  });

  it('displays error messages', async () => {
    mockSharingService.getDesignShares.mockRejectedValue(
      new Error('Failed to load shares')
    );

    render(<SharingDialog {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Failed to load shares')).toBeInTheDocument();
    });
  });

  it('displays success messages', async () => {
    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: [],
    });

    mockSharingService.grantAccess.mockResolvedValue({
      success: true,
      message: 'Design shared successfully',
      share: {
        share_id: 1,
        design_id: 1,
        shared_with_user_id: 2,
        shared_with_username: 'testuser',
        shared_with_email: '<EMAIL>',
        permission_level: 'view',
        shared_at: '2023-01-01T00:00:00Z',
      },
    });

    render(<SharingDialog {...defaultProps} />);

    // Simulate successful share action
    await waitFor(() => {
      // This would be triggered by the actual sharing action
      // For this test, we'll just verify the service was set up correctly
      expect(mockSharingService.getDesignShares).toHaveBeenCalled();
    });
  });

  it('closes dialog when close button is clicked', async () => {
    const user = userEvent.setup();
    const onClose = jest.fn();
    
    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: [],
    });

    render(<SharingDialog {...defaultProps} onClose={onClose} />);

    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);

    expect(onClose).toHaveBeenCalled();
  });

  it('does not render when dialog is closed', () => {
    mockSharingService.getDesignShares.mockResolvedValue({
      design_id: 1,
      shares: [],
    });

    render(<SharingDialog {...defaultProps} open={false} />);

    expect(screen.queryByText('Share "Test Design"')).not.toBeInTheDocument();
  });
});