import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { FilePreview } from '../FilePreview';
import { UploadedFile } from '../../types/file';

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

// Mock file for testing
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

const createMockUploadedFile = (overrides: Partial<UploadedFile> = {}): UploadedFile => ({
  id: '1',
  name: 'test.pdf',
  size: 1024,
  type: 'application/pdf',
  lastModified: Date.now(),
  file: createMockFile('test.pdf', 1024, 'application/pdf'),
  status: 'completed',
  progress: 100,
  ...overrides
});

describe('FilePreview Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clean up any existing DOM elements
    document.body.innerHTML = '';
  });

  it('renders image preview when previewUrl is available', () => {
    const file = createMockUploadedFile({
      name: 'image.png',
      type: 'image/png',
      previewUrl: 'mock-preview-url'
    });
    
    render(<FilePreview file={file} />);
    
    const image = screen.getByAltText('image.png');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'mock-preview-url');
  });

  it('renders file icon when no preview is available', () => {
    const file = createMockUploadedFile({
      name: 'document.pdf',
      type: 'application/pdf'
    });
    
    render(<FilePreview file={file} />);
    
    // Should show PDF icon since no preview URL
    expect(screen.getByTestId('PictureAsPdfIcon')).toBeInTheDocument();
  });

  it('displays correct file information', () => {
    const file = createMockUploadedFile({
      name: 'test-document.pdf',
      size: 2048,
      type: 'application/pdf',
      lastModified: new Date('2023-01-01').getTime()
    });
    
    render(<FilePreview file={file} />);
    
    expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    expect(screen.getByText('application/pdf')).toBeInTheDocument();
    expect(screen.getByText('2 KB')).toBeInTheDocument();
    expect(screen.getByText('1/1/2023')).toBeInTheDocument();
  });

  it('opens full-size dialog when preview is clicked', async () => {
    const file = createMockUploadedFile({
      name: 'image.png',
      type: 'image/png',
      previewUrl: 'mock-preview-url'
    });
    
    render(<FilePreview file={file} />);
    
    const image = screen.getByAltText('image.png');
    await userEvent.click(image);
    
    // Dialog should open
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getAllByText('image.png')).toHaveLength(2); // One in card, one in dialog
  });

  it('opens full-size dialog when zoom button is clicked', async () => {
    const file = createMockUploadedFile();
    
    render(<FilePreview file={file} />);
    
    const zoomButton = screen.getByTitle('View Full Size');
    await userEvent.click(zoomButton);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('closes dialog when close button is clicked', async () => {
    const file = createMockUploadedFile({
      previewUrl: 'mock-preview-url'
    });
    
    render(<FilePreview file={file} />);
    
    // Open dialog
    const image = screen.getByAltText('test.pdf');
    await userEvent.click(image);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    
    // Close dialog
    const closeButton = screen.getByText('Close');
    await userEvent.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  it('handles file download', async () => {
    // Mock DOM methods
    const mockClick = jest.fn();
    const mockAppendChild = jest.fn();
    const mockRemoveChild = jest.fn();
    
    const mockAnchor = {
      href: '',
      download: '',
      click: mockClick
    };
    
    jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as any);
    jest.spyOn(document.body, 'appendChild').mockImplementation(mockAppendChild);
    jest.spyOn(document.body, 'removeChild').mockImplementation(mockRemoveChild);
    
    const file = createMockUploadedFile();
    render(<FilePreview file={file} />);
    
    const downloadButton = screen.getByTitle('Download File');
    await userEvent.click(downloadButton);
    
    expect(document.createElement).toHaveBeenCalledWith('a');
    expect(mockClick).toHaveBeenCalled();
    expect(mockAppendChild).toHaveBeenCalledWith(mockAnchor);
    expect(mockRemoveChild).toHaveBeenCalledWith(mockAnchor);
  });

  it('shows correct icon for different file types', () => {
    const testCases = [
      { type: 'application/pdf', expectedIcon: 'PictureAsPdfIcon' },
      { type: 'image/png', expectedIcon: 'ImageIcon' },
      { type: 'image/jpeg', expectedIcon: 'ImageIcon' },
      { type: 'text/plain', expectedIcon: 'DescriptionIcon' }
    ];
    
    testCases.forEach(({ type, expectedIcon }) => {
      const file = createMockUploadedFile({
        type,
        name: `test.${type.split('/')[1]}`
      });
      
      const { unmount } = render(<FilePreview file={file} />);
      
      expect(screen.getByTestId(expectedIcon)).toBeInTheDocument();
      
      unmount();
    });
  });

  it('shows DXF icon for DXF files', () => {
    const file = createMockUploadedFile({
      name: 'drawing.dxf',
      type: 'application/dxf'
    });
    
    render(<FilePreview file={file} />);
    
    expect(screen.getByTestId('DescriptionIcon')).toBeInTheDocument();
  });

  it('formats file sizes correctly', () => {
    const testCases = [
      { size: 0, expected: '0 Bytes' },
      { size: 1024, expected: '1 KB' },
      { size: 1024 * 1024, expected: '1 MB' },
      { size: 1536, expected: '1.5 KB' }
    ];
    
    testCases.forEach(({ size, expected }) => {
      const file = createMockUploadedFile({ size });
      
      const { unmount } = render(<FilePreview file={file} />);
      
      expect(screen.getByText(expected)).toBeInTheDocument();
      
      unmount();
    });
  });

  it('shows preview not available message for non-image files in dialog', async () => {
    const file = createMockUploadedFile({
      type: 'application/pdf'
    });
    
    render(<FilePreview file={file} />);
    
    const zoomButton = screen.getByTitle('View Full Size');
    await userEvent.click(zoomButton);
    
    expect(screen.getByText('Preview not available')).toBeInTheDocument();
    expect(screen.getByText('PDF files cannot be previewed in browser')).toBeInTheDocument();
  });

  it('respects maxHeight prop', () => {
    const file = createMockUploadedFile({
      previewUrl: 'mock-preview-url'
    });
    
    render(<FilePreview file={file} maxHeight={300} />);
    
    const image = screen.getByAltText('test.pdf');
    expect(image).toHaveStyle({ height: '300px' });
  });

  it('hides details when showDetails is false', () => {
    const file = createMockUploadedFile();
    
    render(<FilePreview file={file} showDetails={false} />);
    
    // File name should not be visible when details are hidden
    expect(screen.queryByText('test.pdf')).not.toBeInTheDocument();
    expect(screen.queryByText('application/pdf')).not.toBeInTheDocument();
  });

  it('shows download button in dialog', async () => {
    const file = createMockUploadedFile();
    
    render(<FilePreview file={file} />);
    
    const zoomButton = screen.getByTitle('View Full Size');
    await userEvent.click(zoomButton);
    
    expect(screen.getByText('Download')).toBeInTheDocument();
  });
});