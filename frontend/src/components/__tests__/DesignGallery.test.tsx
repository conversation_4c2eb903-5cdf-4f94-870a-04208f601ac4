import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DesignGallery } from '../DesignGallery';
import { Design, DesignAction } from '../../types/design';
import { DesignService } from '../../services/designService';

// Mock the DesignService
jest.mock('../../services/designService');
const mockDesignService = DesignService as jest.Mocked<typeof DesignService>;

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'MMM dd, yyyy') return 'Jan 15, 2024';
    return '2024-01-15';
  })
}));

const mockDesigns: Design[] = [
  {
    id: '1',
    userId: 1,
    name: 'Test Design 1',
    originalFilename: 'test1.pdf',
    filePath: '/files/test1.pdf',
    status: 'completed',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    fileSize: 1024000,
    fileType: 'pdf',
    processingProgress: {
      stage: 'complete',
      progress: 100,
      message: 'Processing complete'
    },
    isShared: false,
    viewCount: 5,
    thumbnailUrl: 'http://example.com/thumb1.jpg'
  },
  {
    id: '2',
    userId: 1,
    name: 'Test Design 2',
    originalFilename: 'test2.dxf',
    filePath: '/files/test2.dxf',
    status: 'processing',
    createdAt: '2024-01-14T09:00:00Z',
    updatedAt: '2024-01-14T09:00:00Z',
    fileSize: 512000,
    fileType: 'dxf',
    processingProgress: {
      stage: 'analysis',
      progress: 60,
      message: 'Analyzing drawing features'
    },
    isShared: true,
    viewCount: 2
  },
  {
    id: '3',
    userId: 1,
    name: 'Test Design 3',
    originalFilename: 'test3.png',
    filePath: '/files/test3.png',
    status: 'error',
    createdAt: '2024-01-13T08:00:00Z',
    updatedAt: '2024-01-13T08:00:00Z',
    fileSize: 2048000,
    fileType: 'png',
    processingProgress: {
      stage: 'error',
      progress: 0,
      message: 'Processing failed',
      error: 'Unable to extract features'
    },
    isShared: false,
    viewCount: 1
  }
];

describe('DesignGallery', () => {
  const mockOnDesignAction = jest.fn();
  const mockOnDesignClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockDesignService.markAsViewed.mockResolvedValue();
  });

  it('renders design cards correctly', () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    expect(screen.getByText('Test Design 1')).toBeInTheDocument();
    expect(screen.getByText('Test Design 2')).toBeInTheDocument();
    expect(screen.getByText('Test Design 3')).toBeInTheDocument();
  });

  it('displays correct status chips', () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByText('Processing')).toBeInTheDocument();
    expect(screen.getByText('Error')).toBeInTheDocument();
  });

  it('shows processing progress for processing designs', () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    expect(screen.getByText('Analyzing drawing features')).toBeInTheDocument();
  });

  it('displays file size and type correctly', () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Check that file sizes are displayed - at least the first one should be visible
    expect(screen.getByText(/1000 KB/)).toBeInTheDocument();
    
    // Check that file types are displayed - use regex to handle text split across elements
    expect(screen.getByText(/PDF/)).toBeInTheDocument();
  });

  it('shows view counts', () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    expect(screen.getByText('5 views')).toBeInTheDocument();
    expect(screen.getByText('2 views')).toBeInTheDocument();
    expect(screen.getByText('1 views')).toBeInTheDocument();
  });

  it('handles design click and marks as viewed', async () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    const designCard = screen.getByText('Test Design 1').closest('.MuiCard-root');
    fireEvent.click(designCard!);

    expect(mockDesignService.markAsViewed).toHaveBeenCalledWith('1');
    expect(mockOnDesignClick).toHaveBeenCalledWith(mockDesigns[0]);
  });

  it('opens context menu on menu button click', async () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Find menu buttons by their MoreVert icon
    const menuButtons = screen.getAllByTestId('MoreVertIcon');
    const menuButton = menuButtons[0].closest('button');
    fireEvent.click(menuButton!);

    await waitFor(() => {
      expect(screen.getByText('Rename')).toBeInTheDocument();
      expect(screen.getByText('Duplicate')).toBeInTheDocument();
      expect(screen.getByText('Share')).toBeInTheDocument();
      expect(screen.getByText('Download All')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });
  });

  it('handles rename action', async () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Open menu
    const menuButtons = screen.getAllByTestId('MoreVertIcon');
    const menuButton = menuButtons[0].closest('button');
    fireEvent.click(menuButton!);

    // Click rename
    await waitFor(() => {
      fireEvent.click(screen.getByText('Rename'));
    });

    // Enter new name
    await waitFor(() => {
      const nameInput = screen.getByLabelText('New Name');
      fireEvent.change(nameInput, { target: { value: 'New Design Name' } });
    });

    // Click rename button
    const renameButton = screen.getByRole('button', { name: 'Rename' });
    fireEvent.click(renameButton);

    expect(mockOnDesignAction).toHaveBeenCalledWith({
      type: 'rename',
      designId: '1',
      payload: { newName: 'New Design Name' }
    });
  });

  it('handles delete action with confirmation', async () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Open menu
    const menuButtons = screen.getAllByTestId('MoreVertIcon');
    const menuButton = menuButtons[0].closest('button');
    fireEvent.click(menuButton!);

    // Click delete
    await waitFor(() => {
      fireEvent.click(screen.getByText('Delete'));
    });

    // Confirm deletion
    await waitFor(() => {
      expect(screen.getByText(/Are you sure you want to delete/)).toBeInTheDocument();
    });
    
    const deleteButton = screen.getByRole('button', { name: 'Delete' });
    fireEvent.click(deleteButton);

    expect(mockOnDesignAction).toHaveBeenCalledWith({
      type: 'delete',
      designId: '1'
    });
  });

  it('handles duplicate action', async () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Open menu
    const menuButtons = screen.getAllByTestId('MoreVertIcon');
    const menuButton = menuButtons[0].closest('button');
    fireEvent.click(menuButton!);

    // Click duplicate
    await waitFor(() => {
      fireEvent.click(screen.getByText('Duplicate'));
    });

    // Enter new name
    await waitFor(() => {
      const nameInput = screen.getByLabelText('New Name');
      fireEvent.change(nameInput, { target: { value: 'Duplicated Design' } });
    });

    // Click duplicate button
    const duplicateButton = screen.getByRole('button', { name: 'Duplicate' });
    fireEvent.click(duplicateButton);

    expect(mockOnDesignAction).toHaveBeenCalledWith({
      type: 'duplicate',
      designId: '1',
      payload: { newName: 'Duplicated Design' }
    });
  });

  it('handles share action', async () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Open menu
    const menuButtons = screen.getAllByTestId('MoreVertIcon');
    const menuButton = menuButtons[0].closest('button');
    fireEvent.click(menuButton!);

    // Click share
    await waitFor(() => {
      fireEvent.click(screen.getByText('Share'));
    });

    // Enter email
    await waitFor(() => {
      const emailInput = screen.getByLabelText('User Email');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    });

    // Select permission level
    const permissionSelect = screen.getByLabelText('Permission Level');
    fireEvent.change(permissionSelect, { target: { value: 'edit' } });

    // Click share button
    const shareButton = screen.getByRole('button', { name: 'Share' });
    fireEvent.click(shareButton);

    expect(mockOnDesignAction).toHaveBeenCalledWith({
      type: 'share',
      designId: '1',
      payload: { userEmail: '<EMAIL>', permissionLevel: 'edit' }
    });
  });

  it('shows loading state', () => {
    render(
      <DesignGallery
        designs={[]}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
        loading={true}
      />
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('shows empty state when no designs', () => {
    render(
      <DesignGallery
        designs={[]}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
        loading={false}
      />
    );

    expect(screen.getByText('No designs found')).toBeInTheDocument();
    expect(screen.getByText('Upload your first engineering drawing to get started')).toBeInTheDocument();
  });

  it('displays shared indicator for shared designs', () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Test Design 2 is shared
    const sharedIcons = screen.getAllByLabelText('Shared design');
    expect(sharedIcons).toHaveLength(1);
  });

  it('prevents rename with empty name', async () => {
    render(
      <DesignGallery
        designs={mockDesigns}
        onDesignAction={mockOnDesignAction}
        onDesignClick={mockOnDesignClick}
      />
    );

    // Open menu and click rename
    const menuButtons = screen.getAllByTestId('MoreVertIcon');
    const menuButton = menuButtons[0].closest('button');
    fireEvent.click(menuButton!);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('Rename'));
    });

    // Try to rename with empty name
    await waitFor(() => {
      const renameButton = screen.getByRole('button', { name: 'Rename' });
      expect(renameButton).toBeDisabled();
    });
  });
});