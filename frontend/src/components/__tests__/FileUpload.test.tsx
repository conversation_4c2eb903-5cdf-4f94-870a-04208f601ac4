import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { FileUpload } from '../FileUpload';
import { UploadedFile } from '../../types/file';

// Mock file for testing
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

const mockProps = {
  onFileUpload: jest.fn(),
  onFileRemove: jest.fn(),
  onFileRename: jest.fn(),
  onFileDuplicate: jest.fn(),
  onRetryUpload: jest.fn(),
  uploadedFiles: [] as UploadedFile[],
  maxFiles: 10
};

describe('FileUpload Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any object URLs
    jest.restoreAllMocks();
  });

  it('renders upload area with correct text', () => {
    render(<FileUpload {...mockProps} />);
    
    expect(screen.getByText('Drag & Drop Files Here')).toBeInTheDocument();
    expect(screen.getByText('or click to browse files')).toBeInTheDocument();
    expect(screen.getByText('Choose Files')).toBeInTheDocument();
  });

  it('displays supported formats and file limits', () => {
    render(<FileUpload {...mockProps} />);
    
    expect(screen.getByText(/Supported formats:/)).toBeInTheDocument();
    expect(screen.getByText(/Maximum file size: 50MB/)).toBeInTheDocument();
    expect(screen.getByText(/Maximum files: 10/)).toBeInTheDocument();
  });

  it('handles file selection through input', async () => {
    render(<FileUpload {...mockProps} />);
    
    const file = createMockFile('test.pdf', 1024, 'application/pdf');
    const input = screen.getByRole('button', { name: /choose files/i }).parentElement?.querySelector('input[type="file"]');
    
    if (input) {
      await userEvent.upload(input, file);
      
      await waitFor(() => {
        expect(mockProps.onFileUpload).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              name: 'test.pdf',
              size: 1024,
              type: 'application/pdf'
            })
          ])
        );
      });
    }
  });

  it('validates file types correctly', async () => {
    render(<FileUpload {...mockProps} />);
    
    const invalidFile = createMockFile('test.txt', 1024, 'text/plain');
    const input = screen.getByRole('button', { name: /choose files/i }).parentElement?.querySelector('input[type="file"]');
    
    if (input) {
      await userEvent.upload(input, invalidFile);
      
      await waitFor(() => {
        expect(screen.getByText(/Upload Errors:/)).toBeInTheDocument();
        expect(screen.getByText(/Unsupported file type/)).toBeInTheDocument();
      });
      
      expect(mockProps.onFileUpload).not.toHaveBeenCalled();
    }
  });

  it('validates file size limits', async () => {
    render(<FileUpload {...mockProps} />);
    
    const largeFile = createMockFile('large.pdf', 60 * 1024 * 1024, 'application/pdf'); // 60MB
    const input = screen.getByRole('button', { name: /choose files/i }).parentElement?.querySelector('input[type="file"]');
    
    if (input) {
      await userEvent.upload(input, largeFile);
      
      await waitFor(() => {
        expect(screen.getByText(/Upload Errors:/)).toBeInTheDocument();
        expect(screen.getByText(/File size too large/)).toBeInTheDocument();
      });
      
      expect(mockProps.onFileUpload).not.toHaveBeenCalled();
    }
  });

  it('prevents duplicate file uploads', async () => {
    const existingFile: UploadedFile = {
      id: '1',
      name: 'existing.pdf',
      size: 1024,
      type: 'application/pdf',
      lastModified: Date.now(),
      file: createMockFile('existing.pdf', 1024, 'application/pdf'),
      status: 'completed',
      progress: 100
    };
    
    render(<FileUpload {...mockProps} uploadedFiles={[existingFile]} />);
    
    const duplicateFile = createMockFile('existing.pdf', 1024, 'application/pdf');
    const input = screen.getByRole('button', { name: /choose files/i }).parentElement?.querySelector('input[type="file"]');
    
    if (input) {
      await userEvent.upload(input, duplicateFile);
      
      await waitFor(() => {
        expect(screen.getByText(/Upload Errors:/)).toBeInTheDocument();
        expect(screen.getByText(/already uploaded/)).toBeInTheDocument();
      });
      
      expect(mockProps.onFileUpload).not.toHaveBeenCalled();
    }
  });

  it('enforces maximum file limit', async () => {
    const maxFiles = 2;
    const existingFiles: UploadedFile[] = [
      {
        id: '1',
        name: 'file1.pdf',
        size: 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        file: createMockFile('file1.pdf', 1024, 'application/pdf'),
        status: 'completed',
        progress: 100
      },
      {
        id: '2',
        name: 'file2.pdf',
        size: 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        file: createMockFile('file2.pdf', 1024, 'application/pdf'),
        status: 'completed',
        progress: 100
      }
    ];
    
    render(<FileUpload {...mockProps} uploadedFiles={existingFiles} maxFiles={maxFiles} />);
    
    const newFile = createMockFile('file3.pdf', 1024, 'application/pdf');
    const input = screen.getByRole('button', { name: /choose files/i }).parentElement?.querySelector('input[type="file"]');
    
    if (input) {
      await userEvent.upload(input, newFile);
      
      await waitFor(() => {
        expect(screen.getByText(/Upload Errors:/)).toBeInTheDocument();
        expect(screen.getByText(/Cannot upload more than 2 files/)).toBeInTheDocument();
      });
      
      expect(mockProps.onFileUpload).not.toHaveBeenCalled();
    }
  });

  it('displays uploaded files with correct status', () => {
    const uploadedFiles: UploadedFile[] = [
      {
        id: '1',
        name: 'completed.pdf',
        size: 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        file: createMockFile('completed.pdf', 1024, 'application/pdf'),
        status: 'completed',
        progress: 100
      },
      {
        id: '2',
        name: 'error.pdf',
        size: 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        file: createMockFile('error.pdf', 1024, 'application/pdf'),
        status: 'error',
        progress: 0,
        error: 'Upload failed'
      }
    ];
    
    render(<FileUpload {...mockProps} uploadedFiles={uploadedFiles} />);
    
    expect(screen.getByText('completed.pdf')).toBeInTheDocument();
    expect(screen.getByText('error.pdf')).toBeInTheDocument();
    expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    expect(screen.getByText('ERROR')).toBeInTheDocument();
    expect(screen.getByText('Upload failed')).toBeInTheDocument();
  });

  it('handles file removal', async () => {
    const uploadedFiles: UploadedFile[] = [
      {
        id: '1',
        name: 'test.pdf',
        size: 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        file: createMockFile('test.pdf', 1024, 'application/pdf'),
        status: 'completed',
        progress: 100
      }
    ];
    
    render(<FileUpload {...mockProps} uploadedFiles={uploadedFiles} />);
    
    const removeButton = screen.getByTitle('Remove File');
    await userEvent.click(removeButton);
    
    expect(mockProps.onFileRemove).toHaveBeenCalledWith('1');
  });

  it('handles retry upload for failed files', async () => {
    const uploadedFiles: UploadedFile[] = [
      {
        id: '1',
        name: 'failed.pdf',
        size: 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        file: createMockFile('failed.pdf', 1024, 'application/pdf'),
        status: 'error',
        progress: 0,
        error: 'Network error'
      }
    ];
    
    render(<FileUpload {...mockProps} uploadedFiles={uploadedFiles} />);
    
    const retryButton = screen.getByTitle('Retry Upload');
    await userEvent.click(retryButton);
    
    expect(mockProps.onRetryUpload).toHaveBeenCalledWith('1');
  });

  it('shows progress bar for uploading files', () => {
    const uploadedFiles: UploadedFile[] = [
      {
        id: '1',
        name: 'uploading.pdf',
        size: 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        file: createMockFile('uploading.pdf', 1024, 'application/pdf'),
        status: 'uploading',
        progress: 50
      }
    ];
    
    render(<FileUpload {...mockProps} uploadedFiles={uploadedFiles} />);
    
    expect(screen.getByText('50% - Uploading')).toBeInTheDocument();
    expect(screen.getAllByRole('progressbar')).toHaveLength(2); // One in status icon, one in progress section
  });

  it('clears validation errors when close button is clicked', async () => {
    render(<FileUpload {...mockProps} />);
    
    // Trigger validation error
    const invalidFile = createMockFile('test.txt', 1024, 'text/plain');
    const input = screen.getByRole('button', { name: /choose files/i }).parentElement?.querySelector('input[type="file"]');
    
    if (input) {
      await userEvent.upload(input, invalidFile);
      
      await waitFor(() => {
        expect(screen.getByText(/Upload Errors:/)).toBeInTheDocument();
      });
      
      // Click close button
      const closeButton = screen.getByLabelText('close');
      await userEvent.click(closeButton);
      
      expect(screen.queryByText(/Upload Errors:/)).not.toBeInTheDocument();
    }
  });

  it('handles drag and drop events', async () => {
    render(<FileUpload {...mockProps} />);
    
    const dropZone = screen.getByText('Drag & Drop Files Here').closest('div');
    const file = createMockFile('dropped.pdf', 1024, 'application/pdf');
    
    if (dropZone) {
      // Simulate drag enter
      fireEvent.dragEnter(dropZone, {
        dataTransfer: {
          files: [file]
        }
      });
      
      // Simulate drop
      fireEvent.drop(dropZone, {
        dataTransfer: {
          files: [file]
        }
      });
      
      await waitFor(() => {
        expect(mockProps.onFileUpload).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              name: 'dropped.pdf',
              size: 1024,
              type: 'application/pdf'
            })
          ])
        );
      });
    }
  });
});