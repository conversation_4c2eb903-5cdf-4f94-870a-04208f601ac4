import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';

import ErrorDisplay, { ErrorDetails, RetryOptions } from '../ErrorDisplay';
import ConfidenceDisplay, { OverallConfidence, ConfidenceMetric } from '../ConfidenceDisplay';

// Mock theme for Material-UI components
const theme = createTheme();

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('ErrorDisplay Component', () => {
  const mockError: ErrorDetails = {
    code: 'IMAGE_QUALITY_POOR',
    category: 'processing',
    severity: 'medium',
    message: 'The drawing quality is too poor for accurate analysis.',
    suggestions: [
      'Upload a higher resolution image (minimum 300 DPI recommended)',
      'Ensure the drawing has clear, dark lines on light background'
    ],
    retry_possible: true,
    timestamp: '2025-01-18T10:30:00Z',
    confidence_score: 0.3,
    failed_sections: ['title_block', 'dimensions']
  };

  const mockRetryOptions: RetryOptions = {
    can_retry: true,
    retry_suggestions: [
      'Upload a higher resolution image',
      'Improve image contrast and clarity'
    ],
    estimated_wait_time: 5,
    retry_with_changes: true,
    manual_alternatives: [
      'Use manual input for dimensions',
      'Manually create BOM from drawing'
    ]
  };

  test('renders error message and severity', () => {
    renderWithTheme(<ErrorDisplay error={mockError} />);
    
    expect(screen.getByText(mockError.message)).toBeInTheDocument();
    expect(screen.getByText('MEDIUM')).toBeInTheDocument();
    expect(screen.getByText('PROCESSING')).toBeInTheDocument();
  });

  test('displays confidence score with progress bar', () => {
    renderWithTheme(<ErrorDisplay error={mockError} />);
    
    expect(screen.getByText('Confidence Score: 30%')).toBeInTheDocument();
    
    // Check for progress bar (LinearProgress component)
    const progressBar = document.querySelector('.MuiLinearProgress-root');
    expect(progressBar).toBeInTheDocument();
  });

  test('shows failed sections as chips', () => {
    renderWithTheme(<ErrorDisplay error={mockError} />);
    
    expect(screen.getByText('Affected Sections:')).toBeInTheDocument();
    expect(screen.getByText('title block')).toBeInTheDocument();
    expect(screen.getByText('dimensions')).toBeInTheDocument();
  });

  test('displays retry button when retry is possible', () => {
    const mockOnRetry = jest.fn();
    
    renderWithTheme(
      <ErrorDisplay 
        error={mockError} 
        retryOptions={mockRetryOptions}
        onRetry={mockOnRetry}
      />
    );
    
    const retryButton = screen.getByRole('button', { name: /retry/i });
    expect(retryButton).toBeInTheDocument();
    expect(screen.getByText('(~5s)')).toBeInTheDocument();
    
    fireEvent.click(retryButton);
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  test('displays manual correction button', () => {
    const mockOnManualCorrection = jest.fn();
    
    renderWithTheme(
      <ErrorDisplay 
        error={mockError} 
        onManualCorrection={mockOnManualCorrection}
      />
    );
    
    const manualButton = screen.getByRole('button', { name: /manual correction/i });
    expect(manualButton).toBeInTheDocument();
    
    fireEvent.click(manualButton);
    expect(mockOnManualCorrection).toHaveBeenCalledTimes(1);
  });

  test('expands to show detailed suggestions', async () => {
    renderWithTheme(<ErrorDisplay error={mockError} retryOptions={mockRetryOptions} />);
    
    // Click expand button
    const expandButton = screen.getByLabelText('expand');
    fireEvent.click(expandButton);
    
    // Wait for expansion and check that suggestions are displayed
    await waitFor(() => {
      expect(screen.getByText('Upload a higher resolution image (minimum 300 DPI recommended)')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Ensure the drawing has clear, dark lines on light background')).toBeInTheDocument();
  });

  test('shows retry suggestions when expanded', async () => {
    renderWithTheme(<ErrorDisplay error={mockError} retryOptions={mockRetryOptions} />);
    
    // Expand details
    const expandButton = screen.getByLabelText('expand');
    fireEvent.click(expandButton);
    
    await waitFor(() => {
      expect(screen.getByText('Before Retrying:')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Upload a higher resolution image')).toBeInTheDocument();
    expect(screen.getByText('Improve image contrast and clarity')).toBeInTheDocument();
  });

  test('shows manual alternatives when expanded', async () => {
    renderWithTheme(<ErrorDisplay error={mockError} retryOptions={mockRetryOptions} />);
    
    // Expand details
    const expandButton = screen.getByLabelText('expand');
    fireEvent.click(expandButton);
    
    await waitFor(() => {
      expect(screen.getByText('Alternative Actions:')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Use manual input for dimensions')).toBeInTheDocument();
    expect(screen.getByText('Manually create BOM from drawing')).toBeInTheDocument();
  });

  test('displays technical details when enabled', async () => {
    const errorWithTechnicalDetails: ErrorDetails = {
      ...mockError,
      technical_details: {
        debug_info: 'test_value',
        error_context: { line: 42, function: 'processImage' }
      }
    };
    
    renderWithTheme(
      <ErrorDisplay 
        error={errorWithTechnicalDetails} 
        showTechnicalDetails={true}
      />
    );
    
    // Expand details
    const expandButton = screen.getByLabelText('expand');
    fireEvent.click(expandButton);
    
    await waitFor(() => {
      expect(screen.getByText('Technical Details')).toBeInTheDocument();
    });
    
    // Click to show technical details
    const technicalButton = screen.getByText('Technical Details');
    fireEvent.click(technicalButton);
    
    await waitFor(() => {
      expect(screen.getByText(/"debug_info": "test_value"/)).toBeInTheDocument();
    });
  });

  test('formats timestamp correctly', () => {
    renderWithTheme(<ErrorDisplay error={mockError} />);
    
    // Expand to see timestamp
    const expandButton = screen.getByLabelText('expand');
    fireEvent.click(expandButton);
    
    // Check that timestamp is formatted (exact format may vary by locale)
    expect(screen.getByText(/Time:/)).toBeInTheDocument();
  });

  test('handles different severity levels', () => {
    const criticalError: ErrorDetails = {
      ...mockError,
      severity: 'critical'
    };
    
    renderWithTheme(<ErrorDisplay error={criticalError} />);
    
    expect(screen.getByText('CRITICAL')).toBeInTheDocument();
    // Critical errors should have error color styling
  });

  test('handles non-retryable errors', () => {
    const nonRetryableOptions: RetryOptions = {
      ...mockRetryOptions,
      can_retry: false
    };
    
    renderWithTheme(
      <ErrorDisplay 
        error={mockError} 
        retryOptions={nonRetryableOptions}
      />
    );
    
    // Retry button should not be present
    expect(screen.queryByRole('button', { name: /retry/i })).not.toBeInTheDocument();
  });
});

describe('ConfidenceDisplay Component', () => {
  const mockConfidenceMetric: ConfidenceMetric = {
    component: 'image_quality',
    score: 0.75,
    level: 'high',
    details: {
      resolution: 300,
      contrast: 0.8,
      noise_level: 0.1
    },
    issues: ['Minor blur detected'],
    suggestions: ['Consider higher resolution for better accuracy']
  };

  const mockOverallConfidence: OverallConfidence = {
    overall_score: 0.72,
    overall_level: 'high',
    component_scores: {
      image_quality: mockConfidenceMetric,
      ocr_text: {
        component: 'ocr_text',
        score: 0.65,
        level: 'medium',
        details: {
          text_confidence: 0.65,
          dimensions_found: 3
        },
        issues: ['Some text unclear'],
        suggestions: ['Improve text clarity']
      }
    },
    critical_issues: [],
    warnings: ['Some text unclear'],
    recommendations: [
      'Consider higher resolution for better accuracy',
      'Improve text clarity'
    ]
  };

  test('renders overall confidence score and level', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} />);
    
    expect(screen.getByText('Analysis Confidence: Fair')).toBeInTheDocument();
    expect(screen.getAllByText('HIGH').length).toBeGreaterThan(0);
    expect(screen.getByText('72%')).toBeInTheDocument();
  });

  test('displays overall score progress bar', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} />);
    
    // Check for progress bar
    const progressBars = document.querySelectorAll('.MuiLinearProgress-root');
    expect(progressBars.length).toBeGreaterThan(0);
  });

  test('shows warnings when present', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} />);
    
    expect(screen.getByText('Warnings:')).toBeInTheDocument();
    expect(screen.getAllByText('Some text unclear').length).toBeGreaterThan(0);
  });

  test('displays critical issues alert', () => {
    const confidenceWithCriticalIssues: OverallConfidence = {
      ...mockOverallConfidence,
      critical_issues: ['Analysis failed completely', 'No readable text found']
    };
    
    renderWithTheme(<ConfidenceDisplay confidence={confidenceWithCriticalIssues} />);
    
    expect(screen.getByText('Critical Issues Detected:')).toBeInTheDocument();
    expect(screen.getByText('Analysis failed completely')).toBeInTheDocument();
    expect(screen.getByText('No readable text found')).toBeInTheDocument();
  });

  test('shows component analysis details', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} showDetails={true} />);
    
    expect(screen.getByText('Component Analysis')).toBeInTheDocument();
    expect(screen.getByText('Image Quality')).toBeInTheDocument();
    expect(screen.getByText('Ocr Text')).toBeInTheDocument();
  });

  test('expands component details on click', async () => {
    const mockOnComponentClick = jest.fn();
    
    renderWithTheme(
      <ConfidenceDisplay 
        confidence={mockOverallConfidence} 
        showDetails={true}
        onComponentClick={mockOnComponentClick}
      />
    );
    
    // Find and click on a component accordion - look for the accordion summary
    const accordionSummary = screen.getByText('Image Quality').closest('.MuiAccordionSummary-root');
    if (accordionSummary) {
      fireEvent.click(accordionSummary);
      
      await waitFor(() => {
        expect(screen.getAllByText('Details:').length).toBeGreaterThan(0);
      });
      
      // Check component details are shown
      expect(screen.getByText(/resolution: 300/)).toBeInTheDocument();
      expect(screen.getByText(/contrast: 0.8/)).toBeInTheDocument();
    } else {
      // If accordion structure is different, just verify the component renders
      expect(screen.getByText('Image Quality')).toBeInTheDocument();
    }
  });

  test('shows component issues and suggestions', async () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} showDetails={true} />);
    
    // Find and click on a component accordion - look for the accordion summary
    const accordionSummary = screen.getByText('Image Quality').closest('.MuiAccordionSummary-root');
    if (accordionSummary) {
      fireEvent.click(accordionSummary);
      
      await waitFor(() => {
        expect(screen.getAllByText('Issues:').length).toBeGreaterThan(0);
        expect(screen.getAllByText('Suggestions:').length).toBeGreaterThan(0);
      });
      
      expect(screen.getByText('Minor blur detected')).toBeInTheDocument();
      expect(screen.getAllByText('Consider higher resolution for better accuracy').length).toBeGreaterThan(0);
    } else {
      // If accordion structure is different, just verify the component renders
      expect(screen.getByText('Image Quality')).toBeInTheDocument();
    }
  });

  test('displays recommendations section', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} />);
    
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
    expect(screen.getAllByText('Consider higher resolution for better accuracy').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Improve text clarity').length).toBeGreaterThan(0);
  });

  test('handles different confidence levels with appropriate colors', () => {
    const lowConfidence: OverallConfidence = {
      ...mockOverallConfidence,
      overall_score: 0.3,
      overall_level: 'low'
    };
    
    renderWithTheme(<ConfidenceDisplay confidence={lowConfidence} />);
    
    expect(screen.getByText('LOW')).toBeInTheDocument();
    expect(screen.getByText('Analysis Confidence: Poor')).toBeInTheDocument();
  });

  test('hides details when showDetails is false', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} showDetails={false} />);
    
    expect(screen.queryByText('Component Analysis')).not.toBeInTheDocument();
  });

  test('handles empty component scores', () => {
    const emptyConfidence: OverallConfidence = {
      overall_score: 0.5,
      overall_level: 'medium',
      component_scores: {},
      critical_issues: [],
      warnings: [],
      recommendations: []
    };
    
    renderWithTheme(<ConfidenceDisplay confidence={emptyConfidence} showDetails={true} />);
    
    expect(screen.getByText('Component Analysis')).toBeInTheDocument();
    // Should not crash with empty component scores
  });

  test('formats component names correctly', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} showDetails={true} />);
    
    // Component names should be formatted from snake_case to Title Case
    expect(screen.getByText('Image Quality')).toBeInTheDocument();
    expect(screen.getByText('Ocr Text')).toBeInTheDocument();
  });

  test('shows confidence level chips for components', () => {
    renderWithTheme(<ConfidenceDisplay confidence={mockOverallConfidence} showDetails={true} />);
    
    // Should show confidence level chips for each component
    const chips = screen.getAllByText('HIGH');
    expect(chips.length).toBeGreaterThan(0);
    
    expect(screen.getByText('MEDIUM')).toBeInTheDocument();
  });
});

describe('Error Handling Integration', () => {
  test('error display and confidence display work together', () => {
    const errorWithLowConfidence: ErrorDetails = {
      code: 'PARTIAL_ANALYSIS_FAILURE',
      category: 'processing',
      severity: 'medium',
      message: 'Analysis completed with some limitations.',
      suggestions: ['Review results and add missing information manually'],
      retry_possible: true,
      timestamp: '2025-01-18T10:30:00Z',
      confidence_score: 0.4
    };
    
    const lowConfidence: OverallConfidence = {
      overall_score: 0.4,
      overall_level: 'medium',
      component_scores: {},
      critical_issues: [],
      warnings: ['Low confidence in some components'],
      recommendations: ['Manual review recommended']
    };
    
    const { container } = render(
      <ThemeProvider theme={theme}>
        <div>
          <ErrorDisplay error={errorWithLowConfidence} />
          <ConfidenceDisplay confidence={lowConfidence} />
        </div>
      </ThemeProvider>
    );
    
    // Both components should render without conflicts
    expect(screen.getByText('Analysis completed with some limitations.')).toBeInTheDocument();
    expect(screen.getByText(/Analysis Confidence:/)).toBeInTheDocument();
    expect(screen.getByText('Low confidence in some components')).toBeInTheDocument();
  });
});