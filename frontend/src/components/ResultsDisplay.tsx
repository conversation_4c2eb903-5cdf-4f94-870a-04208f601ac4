import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  IconButton,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  TextField,
  InputAdornment,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar
} from '@mui/material';
import {
  Download,
  Share,
  Print,
  Search,
  FilterList,
  Visibility,
  GetApp,
  Assessment,
  Build,
  Scale,
  ViewInAr,
  MoreVert,
  FileDownload,
  PictureAsPdf,
  TableChart
} from '@mui/icons-material';
import { Design, BOMItem } from '../types/design';
import { Model3D } from '../types/model3d';
import Model3DViewer from './Model3DViewer';
import SharingDialog from './SharingDialog';

interface ResultsDisplayProps {
  design: Design;
  onBack?: () => void;
  onShare?: (designId: string) => void;
}

interface BOMTableProps {
  bomItems: BOMItem[];
  onExport: (format: 'csv' | 'excel' | 'pdf') => void;
}

interface WeightSummaryProps {
  bomItems: BOMItem[];
  totalWeight: number;
}

interface SortConfig {
  key: keyof BOMItem;
  direction: 'asc' | 'desc';
}

// BOM Table Component
const BOMTable: React.FC<BOMTableProps> = ({ bomItems, onExport }) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'partNumber', direction: 'asc' });
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredItems, setFilteredItems] = useState<BOMItem[]>(bomItems);

  // Filter and sort items
  useEffect(() => {
    let filtered = bomItems.filter(item =>
      item.partNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.material.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort items
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' 
          ? aValue - bValue
          : bValue - aValue;
      }
      
      return 0;
    });

    setFilteredItems(filtered);
  }, [bomItems, searchTerm, sortConfig]);

  const handleSort = (key: keyof BOMItem) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const formatWeight = (weight: number) => {
    if (weight < 1) {
      return `${(weight * 1000).toFixed(1)} g`;
    }
    return `${weight.toFixed(2)} kg`;
  };

  const formatVolume = (volume: number) => {
    if (volume < 0.001) {
      return `${(volume * 1000000).toFixed(1)} cm³`;
    }
    return `${volume.toFixed(4)} m³`;
  };

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" component="h2">
          Bill of Materials
        </Typography>
        <Box display="flex" gap={1}>
          <Button
            startIcon={<TableChart />}
            onClick={() => onExport('csv')}
            size="small"
          >
            CSV
          </Button>
          <Button
            startIcon={<FileDownload />}
            onClick={() => onExport('excel')}
            size="small"
          >
            Excel
          </Button>
          <Button
            startIcon={<PictureAsPdf />}
            onClick={() => onExport('pdf')}
            size="small"
          >
            PDF
          </Button>
        </Box>
      </Box>

      {/* Search and Filter */}
      <Box mb={2}>
        <TextField
          fullWidth
          size="small"
          placeholder="Search parts, descriptions, or materials..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* BOM Table */}
      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>
                <TableSortLabel
                  active={sortConfig.key === 'partNumber'}
                  direction={sortConfig.key === 'partNumber' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('partNumber')}
                >
                  Part Number
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortConfig.key === 'description'}
                  direction={sortConfig.key === 'description' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('description')}
                >
                  Description
                </TableSortLabel>
              </TableCell>
              <TableCell align="center">
                <TableSortLabel
                  active={sortConfig.key === 'quantity'}
                  direction={sortConfig.key === 'quantity' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('quantity')}
                >
                  Qty
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortConfig.key === 'material'}
                  direction={sortConfig.key === 'material' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('material')}
                >
                  Material
                </TableSortLabel>
              </TableCell>
              <TableCell align="right">
                <TableSortLabel
                  active={sortConfig.key === 'volume'}
                  direction={sortConfig.key === 'volume' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('volume')}
                >
                  Volume
                </TableSortLabel>
              </TableCell>
              <TableCell align="right">
                <TableSortLabel
                  active={sortConfig.key === 'unitWeight'}
                  direction={sortConfig.key === 'unitWeight' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('unitWeight')}
                >
                  Unit Weight
                </TableSortLabel>
              </TableCell>
              <TableCell align="right">
                <TableSortLabel
                  active={sortConfig.key === 'weight'}
                  direction={sortConfig.key === 'weight' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('weight')}
                >
                  Total Weight
                </TableSortLabel>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredItems.map((item) => (
              <TableRow key={item.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {item.partNumber}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {item.description}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Chip 
                    label={item.quantity} 
                    size="small" 
                    color="primary" 
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip 
                    label={item.material} 
                    size="small" 
                    color="secondary"
                  />
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2">
                    {formatVolume(item.volume)}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2">
                    {formatWeight(item.unitWeight)}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2" fontWeight="medium">
                    {formatWeight(item.weight)}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredItems.length === 0 && (
        <Box textAlign="center" py={4}>
          <Typography color="text.secondary">
            {searchTerm ? 'No parts match your search criteria' : 'No parts found in BOM'}
          </Typography>
        </Box>
      )}

      <Box mt={2} display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="body2" color="text.secondary">
          Showing {filteredItems.length} of {bomItems.length} parts
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Total Parts: {bomItems.reduce((sum, item) => sum + item.quantity, 0)}
        </Typography>
      </Box>
    </Paper>
  );
};

// Weight Summary Component
const WeightSummary: React.FC<WeightSummaryProps> = ({ bomItems, totalWeight }) => {
  const materialBreakdown = bomItems.reduce((acc, item) => {
    if (!acc[item.material]) {
      acc[item.material] = { weight: 0, count: 0, volume: 0 };
    }
    acc[item.material].weight += item.weight;
    acc[item.material].count += item.quantity;
    acc[item.material].volume += item.volume * item.quantity;
    return acc;
  }, {} as Record<string, { weight: number; count: number; volume: number }>);

  const formatWeight = (weight: number) => {
    if (weight < 1) {
      return `${(weight * 1000).toFixed(1)} g`;
    }
    return `${weight.toFixed(2)} kg`;
  };

  const formatVolume = (volume: number) => {
    if (volume < 0.001) {
      return `${(volume * 1000000).toFixed(1)} cm³`;
    }
    return `${volume.toFixed(4)} m³`;
  };

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Typography variant="h6" component="h2" gutterBottom>
        Weight Analysis
      </Typography>

      {/* Total Weight Card */}
      <Card variant="outlined" sx={{ mb: 3, bgcolor: 'primary.50' }}>
        <CardContent sx={{ textAlign: 'center' }}>
          <Scale sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
          <Typography variant="h4" color="primary.main" gutterBottom>
            {formatWeight(totalWeight)}
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Total Assembly Weight
          </Typography>
        </CardContent>
      </Card>

      {/* Material Breakdown */}
      <Typography variant="subtitle1" gutterBottom>
        Material Breakdown
      </Typography>
      <Grid container spacing={2}>
        {Object.entries(materialBreakdown).map(([material, data]) => (
          <Grid item xs={12} sm={6} md={4} key={material}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  {material}
                </Typography>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Weight:
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {formatWeight(data.weight)}
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Parts:
                  </Typography>
                  <Typography variant="body2">
                    {data.count}
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Volume:
                  </Typography>
                  <Typography variant="body2">
                    {formatVolume(data.volume)}
                  </Typography>
                </Box>
                <Box mt={1}>
                  <Typography variant="caption" color="text.secondary">
                    {((data.weight / totalWeight) * 100).toFixed(1)}% of total
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

// Main Results Display Component
const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ design, onBack, onShare }) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [sharingDialogOpen, setSharingDialogOpen] = useState(false);
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const totalWeight = design.bomItems?.reduce((sum, item) => sum + item.weight, 0) || 0;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleBOMExport = async (format: 'csv' | 'excel' | 'pdf') => {
    setLoading(true);
    try {
      // Implementation would call API to generate and download BOM export
      const response = await fetch(`/api/designs/${design.id}/bom/export/${format}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });

      if (!response.ok) throw new Error('Export failed');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${design.name}-bom.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccessMessage(`BOM exported as ${format.toUpperCase()}`);
    } catch (err) {
      setError(`Failed to export BOM as ${format.toUpperCase()}`);
    } finally {
      setLoading(false);
    }
  };

  const handleModelExport = (format: string, data: Blob) => {
    const url = window.URL.createObjectURL(data);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${design.name}-model.${format}`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    setSuccessMessage(`3D model exported as ${format.toUpperCase()}`);
  };

  const handlePrintReport = () => {
    window.print();
    setPrintDialogOpen(false);
  };

  const handleDownloadAll = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/designs/${design.id}/download/all`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });

      if (!response.ok) throw new Error('Download failed');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${design.name}-complete.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccessMessage('Complete design package downloaded');
    } catch (err) {
      setError('Failed to download complete package');
    } finally {
      setLoading(false);
      setExportMenuAnchor(null);
    }
  };

  return (
    <Box>
      {/* Header */}
      <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h4" gutterBottom>
              {design.name}
            </Typography>
            <Box display="flex" gap={2} alignItems="center">
              <Chip 
                label={design.status} 
                color={design.status === 'completed' ? 'success' : 'default'}
                size="small"
              />
              <Typography variant="body2" color="text.secondary">
                Created: {new Date(design.createdAt).toLocaleDateString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                File: {design.originalFilename}
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1}>
            {onBack && (
              <Button onClick={onBack} variant="outlined">
                Back
              </Button>
            )}
            <Button
              startIcon={<Share />}
              onClick={() => setSharingDialogOpen(true)}
              variant="outlined"
            >
              Share
            </Button>
            <Button
              startIcon={<Print />}
              onClick={() => setPrintDialogOpen(true)}
              variant="outlined"
            >
              Print
            </Button>
            <IconButton
              onClick={(e) => setExportMenuAnchor(e.currentTarget)}
              color="primary"
            >
              <MoreVert />
            </IconButton>
          </Box>
        </Box>
      </Paper>

      {/* Analysis Confidence */}
      {design.analysisResults && (
        <Alert 
          severity={design.analysisResults.confidenceScore > 0.8 ? 'success' : 'warning'}
          sx={{ mb: 3 }}
        >
          <Typography variant="body2">
            Analysis Confidence: {(design.analysisResults.confidenceScore * 100).toFixed(1)}%
            {design.analysisResults.confidenceScore < 0.8 && 
              ' - Some results may need manual verification'
            }
          </Typography>
        </Alert>
      )}

      {/* Tabs */}
      <Paper square elevation={1}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab
            icon={<Assessment />}
            label="Overview"
          />
          <Tab
            icon={<Build />}
            label="Bill of Materials"
          />
          <Tab
            icon={<Scale />}
            label="Weight Analysis"
          />
          <Tab
            icon={<ViewInAr />}
            label="3D Model"
          />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <Box mt={3}>
        {currentTab === 0 && (
          <Grid container spacing={3}>
            {/* Summary Cards */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Build sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                  <Typography variant="h5" gutterBottom>
                    {design.bomItems?.length || 0}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    Total Parts
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Scale sx={{ fontSize: 48, color: 'secondary.main', mb: 1 }} />
                  <Typography variant="h5" gutterBottom>
                    {totalWeight < 1 ? `${(totalWeight * 1000).toFixed(1)} g` : `${totalWeight.toFixed(2)} kg`}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    Total Weight
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <ViewInAr sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
                  <Typography variant="h5" gutterBottom>
                    {design.model3D ? 'Available' : 'Generating'}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    3D Model
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Quick Preview */}
            <Grid item xs={12} md={6}>
              {design.bomItems && design.bomItems.length > 0 && (
                <BOMTable 
                  bomItems={design.bomItems.slice(0, 5)} 
                  onExport={handleBOMExport}
                />
              )}
            </Grid>
            <Grid item xs={12} md={6}>
              {design.model3D && (
                <Model3DViewer
                  model={design.model3D}
                  width={400}
                  height={300}
                  onExport={handleModelExport}
                />
              )}
            </Grid>
          </Grid>
        )}

        {currentTab === 1 && design.bomItems && (
          <BOMTable 
            bomItems={design.bomItems} 
            onExport={handleBOMExport}
          />
        )}

        {currentTab === 2 && design.bomItems && (
          <WeightSummary 
            bomItems={design.bomItems} 
            totalWeight={totalWeight}
          />
        )}

        {currentTab === 3 && (
          <Box>
            {design.model3D ? (
              <Model3DViewer
                model={design.model3D}
                width={window.innerWidth - 100}
                height={600}
                onExport={handleModelExport}
              />
            ) : (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <ViewInAr sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  3D Model Not Available
                </Typography>
                <Typography color="text.secondary">
                  The 3D model is still being generated or failed to generate.
                </Typography>
              </Paper>
            )}
          </Box>
        )}
      </Box>

      {/* Export Menu */}
      <Menu
        anchorEl={exportMenuAnchor}
        open={Boolean(exportMenuAnchor)}
        onClose={() => setExportMenuAnchor(null)}
      >
        <MenuItem onClick={handleDownloadAll}>
          <GetApp sx={{ mr: 1 }} />
          Download Complete Package
        </MenuItem>
        <MenuItem onClick={() => handleBOMExport('csv')}>
          <TableChart sx={{ mr: 1 }} />
          Export BOM as CSV
        </MenuItem>
        <MenuItem onClick={() => handleBOMExport('pdf')}>
          <PictureAsPdf sx={{ mr: 1 }} />
          Export BOM as PDF
        </MenuItem>
      </Menu>

      {/* Print Dialog */}
      <Dialog open={printDialogOpen} onClose={() => setPrintDialogOpen(false)}>
        <DialogTitle>Print Report</DialogTitle>
        <DialogContent>
          <Typography>
            This will print a comprehensive report including BOM, weight analysis, and design summary.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPrintDialogOpen(false)}>Cancel</Button>
          <Button onClick={handlePrintReport} variant="contained">Print</Button>
        </DialogActions>
      </Dialog>

      {/* Sharing Dialog */}
      <SharingDialog
        open={sharingDialogOpen}
        onClose={() => setSharingDialogOpen(false)}
        designId={design.id}
        designName={design.name}
      />

      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255,255,255,0.8)',
            zIndex: 9999
          }}
        >
          <CircularProgress />
        </Box>
      )}

      {/* Success/Error Messages */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={4000}
        onClose={() => setSuccessMessage(null)}
        message={successMessage}
      />
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
      >
        <Alert severity="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ResultsDisplay;