import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Alert,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip
} from '@mui/material';
import {
  CloudDownload,
  Visibility,
  Error as ErrorIcon
} from '@mui/icons-material';
import Model3DViewer from './Model3DViewer';
import { Model3D, ViewerError } from '../types/model3d';

const Model3DViewerDemo: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState<Model3D | null>(null);
  const [error, setError] = useState<ViewerError | null>(null);
  const [exportStatus, setExportStatus] = useState<string>('');

  // Sample 3D models for demonstration
  const sampleModels: Model3D[] = [
    {
      id: 'sample-1',
      designId: 'design-1',
      modelFilePath: '/samples/bracket.stl',
      openscadScript: 'linear_extrude(height=10) square([50,30]);',
      generationTime: 3500,
      fileSize: 245760,
      createdAt: '2025-01-18T10:00:00Z'
    },
    {
      id: 'sample-2',
      designId: 'design-2',
      modelFilePath: '/samples/gear.stl',
      openscadScript: 'cylinder(h=5, r=25, $fn=60);',
      generationTime: 5200,
      fileSize: 512000,
      createdAt: '2025-01-18T11:30:00Z'
    },
    {
      id: 'sample-3',
      designId: 'design-3',
      modelFilePath: '/samples/housing.stl',
      openscadScript: 'difference() { cube([40,40,20]); translate([5,5,5]) cube([30,30,15]); }',
      generationTime: 4100,
      fileSize: 387200,
      createdAt: '2025-01-18T14:15:00Z'
    }
  ];

  const handleModelSelect = (model: Model3D) => {
    setSelectedModel(model);
    setError(null);
    setExportStatus('');
  };

  const handleViewerError = (error: ViewerError) => {
    setError(error);
    console.error('3D Viewer Error:', error);
  };

  const handleExport = (format: string, data: Blob) => {
    // Create download link
    const url = URL.createObjectURL(data);
    const link = document.createElement('a');
    link.href = url;
    link.download = `model_${selectedModel?.id}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setExportStatus(`Successfully exported ${format.toUpperCase()} file`);
    setTimeout(() => setExportStatus(''), 3000);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatGenerationTime = (ms: number): string => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        3D Model Viewer Demo
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        This demo showcases the Three.js 3D model viewer component with interactive controls,
        export functionality, and responsive design for mobile devices.
      </Typography>

      <Grid container spacing={3}>
        {/* Model Selection Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Sample Models
            </Typography>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {sampleModels.map((model) => (
                <Card 
                  key={model.id}
                  variant={selectedModel?.id === model.id ? "outlined" : "elevation"}
                  sx={{ 
                    cursor: 'pointer',
                    border: selectedModel?.id === model.id ? 2 : 0,
                    borderColor: 'primary.main'
                  }}
                  onClick={() => handleModelSelect(model)}
                >
                  <CardContent sx={{ pb: 1 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Model {model.id.split('-')[1]}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                      <Chip 
                        label={formatFileSize(model.fileSize || 0)}
                        size="small" 
                        variant="outlined"
                      />
                      <Chip 
                        label={formatGenerationTime(model.generationTime!)} 
                        size="small" 
                        variant="outlined"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {model.openscadScript}
                    </Typography>
                  </CardContent>
                  
                  <CardActions sx={{ pt: 0 }}>
                    <Button 
                      size="small" 
                      startIcon={<Visibility />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleModelSelect(model);
                      }}
                    >
                      View 3D
                    </Button>
                  </CardActions>
                </Card>
              ))}
            </Box>

            {/* Feature List */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Features
              </Typography>
              <Box component="ul" sx={{ pl: 2, m: 0 }}>
                <Typography component="li" variant="body2">
                  Interactive 3D model display
                </Typography>
                <Typography component="li" variant="body2">
                  STL file loading and rendering
                </Typography>
                <Typography component="li" variant="body2">
                  Camera controls (rotate, zoom, pan)
                </Typography>
                <Typography component="li" variant="body2">
                  Wireframe and transparency modes
                </Typography>
                <Typography component="li" variant="body2">
                  Model export (STL, screenshots)
                </Typography>
                <Typography component="li" variant="body2">
                  Fullscreen viewing
                </Typography>
                <Typography component="li" variant="body2">
                  Mobile responsive design
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* 3D Viewer Panel */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            {selectedModel ? (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    3D Model: {selectedModel.id}
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<CloudDownload />}
                    size="small"
                    onClick={() => {
                      // Simulate export
                      const blob = new Blob(['mock stl data'], { type: 'application/octet-stream' });
                      handleExport('stl', blob);
                    }}
                  >
                    Download STL
                  </Button>
                </Box>

                {/* Status Messages */}
                {error && (
                  <Alert 
                    severity="error" 
                    icon={<ErrorIcon />}
                    sx={{ mb: 2 }}
                    onClose={() => setError(null)}
                  >
                    {error.message}
                    {error.details && (
                      <Typography variant="caption" display="block">
                        {error.details}
                      </Typography>
                    )}
                  </Alert>
                )}

                {exportStatus && (
                  <Alert severity="success" sx={{ mb: 2 }}>
                    {exportStatus}
                  </Alert>
                )}

                {/* 3D Viewer Component */}
                <Model3DViewer
                  model={selectedModel}
                  width={600}
                  height={400}
                  onError={handleViewerError}
                  onExport={handleExport}
                />

                {/* Model Information */}
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Model Information
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">
                        File Size
                      </Typography>
                      <Typography variant="body2">
                        {formatFileSize(selectedModel.fileSize)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">
                        Generation Time
                      </Typography>
                      <Typography variant="body2">
                        {formatGenerationTime(selectedModel.generationTime!)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">
                        Created
                      </Typography>
                      <Typography variant="body2">
                        {new Date(selectedModel.createdAt).toLocaleDateString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">
                        Design ID
                      </Typography>
                      <Typography variant="body2">
                        {selectedModel.designId}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </>
            ) : (
              <Box 
                sx={{ 
                  display: 'flex', 
                  flexDirection: 'column',
                  alignItems: 'center', 
                  justifyContent: 'center',
                  height: 400,
                  color: 'text.secondary'
                }}
              >
                <Visibility sx={{ fontSize: 64, mb: 2, opacity: 0.5 }} />
                <Typography variant="h6" gutterBottom>
                  No Model Selected
                </Typography>
                <Typography variant="body2" textAlign="center">
                  Select a sample model from the left panel to view it in 3D
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Model3DViewerDemo;