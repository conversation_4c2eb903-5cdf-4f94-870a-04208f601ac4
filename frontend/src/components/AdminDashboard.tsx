/**
 * Admin Dashboard Component
 * Main admin panel for system management
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Container,
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Error as ErrorIcon,
  Science as ScienceIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { SystemStatsCard } from './admin/SystemStatsCard';
import { UserManagement } from './admin/UserManagement';
import { SystemHealth } from './admin/SystemHealth';
import { ErrorLogs } from './admin/ErrorLogs';
import { MaterialsManagement } from './admin/MaterialsManagement';
import { UserActivityLogs } from './admin/UserActivityLogs';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `admin-tab-${index}`,
    'aria-controls': `admin-tabpanel-${index}`,
  };
}

export const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if user is admin
  useEffect(() => {
    if (user && !user.is_admin) {
      setError('Access denied. Admin privileges required.');
    }
  }, [user]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error('Logout error:', err);
    }
  };

  if (!user?.is_admin) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">
          Access denied. Admin privileges required to view this page.
        </Alert>
      </Container>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Admin App Bar */}
      <AppBar position="static" color="primary">
        <Toolbar>
          <SettingsIcon sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Admin Dashboard
          </Typography>
          <Typography variant="body2" sx={{ mr: 2 }}>
            Welcome, {user.username}
          </Typography>
          <Button color="inherit" onClick={handleLogout}>
            Logout
          </Button>
        </Toolbar>
      </AppBar>

      {error && (
        <Alert severity="error" sx={{ m: 2 }}>
          {error}
        </Alert>
      )}

      {/* Admin Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          aria-label="admin dashboard tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            icon={<DashboardIcon />}
            label="Overview"
            {...a11yProps(0)}
          />
          <Tab
            icon={<PeopleIcon />}
            label="Users"
            {...a11yProps(1)}
          />
          <Tab
            icon={<AssessmentIcon />}
            label="System Health"
            {...a11yProps(2)}
          />
          <Tab
            icon={<ErrorIcon />}
            label="Error Logs"
            {...a11yProps(3)}
          />
          <Tab
            icon={<ScienceIcon />}
            label="Materials"
            {...a11yProps(4)}
          />
          <Tab
            label="Activity Logs"
            {...a11yProps(5)}
          />
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <TabPanel value={currentTab} index={0}>
        <SystemStatsCard />
      </TabPanel>

      <TabPanel value={currentTab} index={1}>
        <UserManagement />
      </TabPanel>

      <TabPanel value={currentTab} index={2}>
        <SystemHealth />
      </TabPanel>

      <TabPanel value={currentTab} index={3}>
        <ErrorLogs />
      </TabPanel>

      <TabPanel value={currentTab} index={4}>
        <MaterialsManagement />
      </TabPanel>

      <TabPanel value={currentTab} index={5}>
        <UserActivityLogs />
      </TabPanel>

      {loading && (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          position="fixed"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bgcolor="rgba(255, 255, 255, 0.8)"
          zIndex={9999}
        >
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};