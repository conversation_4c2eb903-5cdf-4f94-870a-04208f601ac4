/**
 * Fabrication Progress Component for Individual Designs
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Build as BuildIcon,
  Timeline as TimelineIcon,
  Assignment as AssignmentIcon,
  Edit as EditIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import fabricationService, {
  DesignProgress,
  PartProgress,
  FabricationStage,
  FabricationTeam,
  TimelineData,
  PartStatusUpdateRequest,
  PartAssignmentRequest,
} from '../services/fabricationService';

interface FabricationProgressProps {
  designId: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`progress-tabpanel-${index}`}
      aria-labelledby={`progress-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const FabricationProgress: React.FC<FabricationProgressProps> = ({ designId }) => {
  const [tabValue, setTabValue] = useState(0);
  const [designProgress, setDesignProgress] = useState<DesignProgress | null>(null);
  const [timelineData, setTimelineData] = useState<TimelineData | null>(null);
  const [stages, setStages] = useState<FabricationStage[]>([]);
  const [teams, setTeams] = useState<FabricationTeam[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dialog states
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [assignmentDialogOpen, setAssignmentDialogOpen] = useState(false);
  const [selectedPart, setSelectedPart] = useState<PartProgress | null>(null);

  // Form states
  const [selectedStageId, setSelectedStageId] = useState<number | ''>('');
  const [statusNotes, setStatusNotes] = useState('');
  const [estimatedCompletion, setEstimatedCompletion] = useState<Date | null>(null);
  const [assignedUserId, setAssignedUserId] = useState<number | ''>('');
  const [assignedTeamId, setAssignedTeamId] = useState<number | ''>('');

  useEffect(() => {
    loadProgressData();
  }, [designId]);

  const loadProgressData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [progress, timeline, stagesData, teamsData] = await Promise.all([
        fabricationService.getDesignProgress(designId),
        fabricationService.getTimelineData(designId),
        fabricationService.getFabricationStages(),
        fabricationService.getFabricationTeams(),
      ]);
      
      setDesignProgress(progress);
      setTimelineData(timeline);
      setStages(stagesData);
      setTeams(teamsData);
    } catch (err) {
      console.error('Error loading progress data:', err);
      setError('Failed to load progress data');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const openStatusDialog = (part: PartProgress) => {
    setSelectedPart(part);
    setSelectedStageId(part.current_stage?.id || '');
    setStatusNotes(part.notes || '');
    setEstimatedCompletion(part.estimated_completion ? new Date(part.estimated_completion) : null);
    setStatusDialogOpen(true);
  };

  const openAssignmentDialog = (part: PartProgress) => {
    setSelectedPart(part);
    setAssignedUserId(part.assigned_to || '');
    setAssignedTeamId(part.assigned_team_id || '');
    setAssignmentDialogOpen(true);
  };

  const handleStatusUpdate = async () => {
    if (!selectedPart || !selectedStageId) return;

    try {
      const updateData: PartStatusUpdateRequest = {
        stage_id: selectedStageId as number,
        notes: statusNotes || undefined,
        estimated_completion: estimatedCompletion?.toISOString() || undefined,
      };

      await fabricationService.updatePartStatus(designId, selectedPart.part_id, updateData);
      await loadProgressData();
      setStatusDialogOpen(false);
      resetStatusForm();
    } catch (err) {
      console.error('Error updating part status:', err);
      setError('Failed to update part status');
    }
  };

  const handleAssignmentUpdate = async () => {
    if (!selectedPart) return;

    try {
      const assignmentData: PartAssignmentRequest = {
        assigned_to: assignedUserId || undefined,
        assigned_team_id: assignedTeamId || undefined,
      };

      await fabricationService.assignPart(designId, selectedPart.part_id, assignmentData);
      await loadProgressData();
      setAssignmentDialogOpen(false);
      resetAssignmentForm();
    } catch (err) {
      console.error('Error updating part assignment:', err);
      setError('Failed to update part assignment');
    }
  };

  const resetStatusForm = () => {
    setSelectedPart(null);
    setSelectedStageId('');
    setStatusNotes('');
    setEstimatedCompletion(null);
  };

  const resetAssignmentForm = () => {
    setSelectedPart(null);
    setAssignedUserId('');
    setAssignedTeamId('');
  };

  const getStageColor = (stageName?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!stageName) return 'default';
    
    switch (stageName.toLowerCase()) {
      case 'not started': return 'default';
      case 'material prep': return 'info';
      case 'machining': return 'primary';
      case 'welding/assembly': return 'secondary';
      case 'quality check': return 'warning';
      case 'finishing': return 'info';
      case 'completed': return 'success';
      default: return 'primary';
    }
  };

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const isOverdue = (estimatedCompletion?: string, actualCompletion?: string): boolean => {
    if (!estimatedCompletion || actualCompletion) return false;
    return new Date(estimatedCompletion) < new Date();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
        <Button onClick={loadProgressData} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ width: '100%' }}>
        {designProgress && (
          <>
            <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
              <BuildIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              {designProgress.design_name} - Fabrication Progress
            </Typography>

            {/* Progress Summary */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography color="textSecondary" gutterBottom>
                      Total Parts
                    </Typography>
                    <Typography variant="h4">
                      {designProgress.total_parts}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography color="textSecondary" gutterBottom>
                      Completed Parts
                    </Typography>
                    <Typography variant="h4">
                      {designProgress.completed_parts}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography color="textSecondary" gutterBottom>
                      Progress
                    </Typography>
                    <Typography variant="h4">
                      {designProgress.completion_percentage}%
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography color="textSecondary" gutterBottom>
                      Overall Status
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={designProgress.completion_percentage}
                      color={designProgress.completion_percentage === 100 ? 'success' : 'primary'}
                      sx={{ height: 10, borderRadius: 5, mt: 1 }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="progress tabs">
                <Tab
                  label="Parts Progress"
                  icon={<AssignmentIcon />}
                  id="progress-tab-0"
                  aria-controls="progress-tabpanel-0"
                />
                <Tab
                  label="Timeline"
                  icon={<TimelineIcon />}
                  id="progress-tab-1"
                  aria-controls="progress-tabpanel-1"
                />
              </Tabs>
            </Box>

            {/* Parts Progress Tab */}
            <TabPanel value={tabValue} index={0}>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Part Number</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell align="center">Current Stage</TableCell>
                      <TableCell align="center">Assigned To</TableCell>
                      <TableCell align="center">Due Date</TableCell>
                      <TableCell align="center">Status</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {designProgress.parts_progress.map((part) => (
                      <TableRow key={part.part_id}>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight="medium">
                            {part.part_number || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>{part.part_description || 'N/A'}</TableCell>
                        <TableCell align="center">
                          <Chip
                            label={part.current_stage?.name || 'Not Started'}
                            color={getStageColor(part.current_stage?.name)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="center">
                          {part.assigned_user_name && (
                            <Chip
                              icon={<PersonIcon />}
                              label={part.assigned_user_name}
                              size="small"
                              variant="outlined"
                            />
                          )}
                          {part.assigned_team_name && (
                            <Chip
                              icon={<GroupIcon />}
                              label={part.assigned_team_name}
                              size="small"
                              variant="outlined"
                              sx={{ ml: part.assigned_user_name ? 1 : 0 }}
                            />
                          )}
                          {!part.assigned_user_name && !part.assigned_team_name && (
                            <Typography variant="body2" color="textSecondary">
                              Unassigned
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell align="center">
                          {part.estimated_completion ? (
                            <Box>
                              <Typography variant="body2">
                                {formatDate(part.estimated_completion)}
                              </Typography>
                              {isOverdue(part.estimated_completion, part.actual_completion) && (
                                <Chip
                                  icon={<WarningIcon />}
                                  label="Overdue"
                                  color="error"
                                  size="small"
                                  sx={{ mt: 0.5 }}
                                />
                              )}
                            </Box>
                          ) : (
                            <Typography variant="body2" color="textSecondary">
                              Not set
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell align="center">
                          {part.actual_completion ? (
                            <Chip
                              icon={<CheckCircleIcon />}
                              label="Completed"
                              color="success"
                              size="small"
                            />
                          ) : (
                            <Chip
                              icon={<ScheduleIcon />}
                              label="In Progress"
                              color="primary"
                              size="small"
                            />
                          )}
                        </TableCell>
                        <TableCell align="center">
                          <Tooltip title="Update Status">
                            <IconButton
                              size="small"
                              onClick={() => openStatusDialog(part)}
                              color="primary"
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Assign">
                            <IconButton
                              size="small"
                              onClick={() => openAssignmentDialog(part)}
                              color="secondary"
                            >
                              <PersonIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            {/* Timeline Tab */}
            <TabPanel value={tabValue} index={1}>
              {timelineData && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Project Timeline
                  </Typography>
                  {timelineData.timeline_data.map((item) => (
                    <Card key={item.part_id} sx={{ mb: 2 }}>
                      <CardContent>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="h6">
                              {item.part_number || 'Unknown Part'}
                            </Typography>
                            <Typography color="textSecondary" gutterBottom>
                              {item.description || 'No description'}
                            </Typography>
                            <Chip
                              label={item.current_stage}
                              color={getStageColor(item.current_stage)}
                              size="small"
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="body2">
                              <strong>Started:</strong> {formatDateTime(item.started_at)}
                            </Typography>
                            <Typography variant="body2">
                              <strong>Est. Completion:</strong> {formatDateTime(item.estimated_completion)}
                            </Typography>
                            {item.actual_completion && (
                              <Typography variant="body2">
                                <strong>Completed:</strong> {formatDateTime(item.actual_completion)}
                              </Typography>
                            )}
                            {item.assigned_to && (
                              <Typography variant="body2">
                                <strong>Assigned to:</strong> {item.assigned_to}
                              </Typography>
                            )}
                            {item.assigned_team && (
                              <Typography variant="body2">
                                <strong>Team:</strong> {item.assigned_team}
                              </Typography>
                            )}
                          </Grid>
                        </Grid>
                        
                        {/* Stage History */}
                        {item.stage_history.length > 0 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Stage History:
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                              {item.stage_history.map((history, index) => (
                                <Chip
                                  key={index}
                                  label={`${history.stage_name} (${history.duration_minutes || 0}min)`}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              )}
            </TabPanel>
          </>
        )}

        {/* Status Update Dialog */}
        <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Update Part Status</DialogTitle>
          <DialogContent>
            {selectedPart && (
              <Box sx={{ pt: 1 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {selectedPart.part_number} - {selectedPart.part_description}
                </Typography>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Fabrication Stage</InputLabel>
                  <Select
                    value={selectedStageId}
                    onChange={(e) => setSelectedStageId(e.target.value as number | '')}
                    label="Fabrication Stage"
                  >
                    {stages.map((stage) => (
                      <MenuItem key={stage.id} value={stage.id}>
                        {stage.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={statusNotes}
                  onChange={(e) => setStatusNotes(e.target.value)}
                  sx={{ mb: 2 }}
                />

                <DateTimePicker
                  label="Estimated Completion"
                  value={estimatedCompletion}
                  onChange={(newValue) => setEstimatedCompletion(newValue)}
                  slotProps={{
                    textField: {
                      fullWidth: true
                    }
                  }}
                />
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleStatusUpdate} variant="contained">
              Update Status
            </Button>
          </DialogActions>
        </Dialog>

        {/* Assignment Dialog */}
        <Dialog open={assignmentDialogOpen} onClose={() => setAssignmentDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Assign Part</DialogTitle>
          <DialogContent>
            {selectedPart && (
              <Box sx={{ pt: 1 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {selectedPart.part_number} - {selectedPart.part_description}
                </Typography>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Assign to User</InputLabel>
                  <Select
                    value={assignedUserId}
                    onChange={(e) => setAssignedUserId(e.target.value as number | '')}
                    label="Assign to User"
                  >
                    <MenuItem value="">None</MenuItem>
                    {/* Note: In a real implementation, you'd fetch users from an API */}
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>Assign to Team</InputLabel>
                  <Select
                    value={assignedTeamId}
                    onChange={(e) => setAssignedTeamId(e.target.value as number | '')}
                    label="Assign to Team"
                  >
                    <MenuItem value="">None</MenuItem>
                    {teams.map((team) => (
                      <MenuItem key={team.id} value={team.id}>
                        {team.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAssignmentDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleAssignmentUpdate} variant="contained">
              Update Assignment
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default FabricationProgress;