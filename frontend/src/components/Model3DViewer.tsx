import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  Box,
  Paper,
  IconButton,
  Toolbar,
  Typography,
  Slider,
  FormControlLabel,
  Switch,
  Menu,
  MenuItem,
  Button,
  Alert,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Fullscreen,
  FullscreenExit,
  Settings,
  Download,
  Refresh,
  GridOn,
  GridOff,
  Visibility,
  VisibilityOff,
  CameraAlt,
  RotateRight
} from '@mui/icons-material';
import * as THREE from 'three';
import { OrbitControls } from 'three-stdlib';
import { STLLoader } from 'three-stdlib';
import { STLExporter } from 'three-stdlib';
import { Model3D, ViewerSettings, ViewerError, ExportOptions } from '../types/model3d';

interface Model3DViewerProps {
  model?: Model3D;
  width?: number;
  height?: number;
  onError?: (error: ViewerError) => void;
  onExport?: (format: string, data: Blob) => void;
}

const Model3DViewer: React.FC<Model3DViewerProps> = ({
  model,
  width = 800,
  height = 600,
  onError,
  onExport
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const controlsRef = useRef<OrbitControls>();
  const modelMeshRef = useRef<THREE.Mesh>();
  const animationIdRef = useRef<number>();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ViewerError | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settingsAnchor, setSettingsAnchor] = useState<null | HTMLElement>(null);
  const [exportAnchor, setExportAnchor] = useState<null | HTMLElement>(null);
  
  const [settings, setSettings] = useState<ViewerSettings>({
    wireframe: false,
    transparency: 0,
    showGrid: true,
    backgroundColor: '#f5f5f5'
  });

  // Initialize Three.js scene
  const initScene = useCallback(() => {
    if (!mountRef.current) return;

    // Scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(settings.backgroundColor);
    sceneRef.current = scene;

    // Camera
    const camera = new THREE.PerspectiveCamera(
      75,
      width / height,
      0.1,
      1000
    );
    camera.position.set(5, 5, 5);
    cameraRef.current = camera;

    // Renderer
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true,
      preserveDrawingBuffer: true // For screenshots
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.screenSpacePanning = false;
    controls.minDistance = 1;
    controls.maxDistance = 100;
    controls.maxPolarAngle = Math.PI;
    controlsRef.current = controls;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // Grid
    if (settings.showGrid) {
      const gridHelper = new THREE.GridHelper(20, 20);
      gridHelper.name = 'grid';
      scene.add(gridHelper);
    }

    // Add renderer to DOM
    mountRef.current.appendChild(renderer.domElement);

    // Animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();

  }, [width, height, settings.backgroundColor, settings.showGrid]);

  // Load STL model
  const loadModel = useCallback(async (modelPath: string) => {
    if (!sceneRef.current) return;

    setLoading(true);
    setError(null);

    try {
      const loader = new STLLoader();
      
      const geometry = await new Promise<THREE.BufferGeometry>((resolve, reject) => {
        loader.load(
          modelPath,
          (geometry) => resolve(geometry),
          undefined,
          (error) => reject(error)
        );
      });

      // Remove existing model
      if (modelMeshRef.current) {
        sceneRef.current.remove(modelMeshRef.current);
        modelMeshRef.current.geometry.dispose();
        if (Array.isArray(modelMeshRef.current.material)) {
          modelMeshRef.current.material.forEach(mat => mat.dispose());
        } else {
          modelMeshRef.current.material.dispose();
        }
      }

      // Create material
      const material = new THREE.MeshPhongMaterial({
        color: 0x888888,
        wireframe: settings.wireframe,
        transparent: settings.transparency > 0,
        opacity: 1 - settings.transparency
      });

      // Create mesh
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;
      mesh.receiveShadow = true;
      
      // Center the model
      const box = new THREE.Box3().setFromObject(mesh);
      const center = box.getCenter(new THREE.Vector3());
      mesh.position.sub(center);
      
      // Scale to fit in view
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);
      const scale = 5 / maxDim;
      mesh.scale.setScalar(scale);

      sceneRef.current.add(mesh);
      modelMeshRef.current = mesh;

      // Adjust camera position
      if (cameraRef.current && controlsRef.current) {
        const distance = maxDim * 2;
        cameraRef.current.position.set(distance, distance, distance);
        controlsRef.current.target.set(0, 0, 0);
        controlsRef.current.update();
      }

    } catch (err) {
      const error: ViewerError = {
        type: 'loading',
        message: 'Failed to load 3D model',
        details: err instanceof Error ? err.message : 'Unknown error'
      };
      setError(error);
      onError?.(error);
    } finally {
      setLoading(false);
    }
  }, [settings.wireframe, settings.transparency, onError]);

  // Update model appearance
  const updateModelAppearance = useCallback(() => {
    if (!modelMeshRef.current) return;

    const material = modelMeshRef.current.material as THREE.MeshPhongMaterial;
    material.wireframe = settings.wireframe;
    material.transparent = settings.transparency > 0;
    material.opacity = 1 - settings.transparency;
    material.needsUpdate = true;
  }, [settings.wireframe, settings.transparency]);

  // Update scene background
  const updateBackground = useCallback(() => {
    if (!sceneRef.current) return;
    sceneRef.current.background = new THREE.Color(settings.backgroundColor);
  }, [settings.backgroundColor]);

  // Toggle grid
  const updateGrid = useCallback(() => {
    if (!sceneRef.current) return;

    const existingGrid = sceneRef.current.getObjectByName('grid');
    if (existingGrid) {
      sceneRef.current.remove(existingGrid);
    }

    if (settings.showGrid) {
      const gridHelper = new THREE.GridHelper(20, 20);
      gridHelper.name = 'grid';
      sceneRef.current.add(gridHelper);
    }
  }, [settings.showGrid]);

  // Export model
  const exportModel = useCallback(async (format: 'stl' | 'screenshot') => {
    if (!modelMeshRef.current || !rendererRef.current) return;

    try {
      if (format === 'stl') {
        const exporter = new STLExporter();
        const stlString = exporter.parse(modelMeshRef.current);
        const blob = new Blob([stlString], { type: 'application/octet-stream' });
        onExport?.('stl', blob);
      } else if (format === 'screenshot') {
        rendererRef.current.render(sceneRef.current!, cameraRef.current!);
        const canvas = rendererRef.current.domElement;
        canvas.toBlob((blob) => {
          if (blob) {
            onExport?.('png', blob);
          }
        });
      }
    } catch (err) {
      const error: ViewerError = {
        type: 'export',
        message: `Failed to export ${format}`,
        details: err instanceof Error ? err.message : 'Unknown error'
      };
      setError(error);
      onError?.(error);
    }
  }, [onExport, onError]);

  // Handle window resize
  const handleResize = useCallback(() => {
    if (!cameraRef.current || !rendererRef.current || !mountRef.current) return;

    const container = mountRef.current;
    const newWidth = container.clientWidth;
    const newHeight = container.clientHeight;

    cameraRef.current.aspect = newWidth / newHeight;
    cameraRef.current.updateProjectionMatrix();
    rendererRef.current.setSize(newWidth, newHeight);
  }, []);

  // Reset camera position
  const resetCamera = useCallback(() => {
    if (!cameraRef.current || !controlsRef.current) return;

    cameraRef.current.position.set(5, 5, 5);
    controlsRef.current.target.set(0, 0, 0);
    controlsRef.current.update();
  }, []);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Initialize scene on mount
  useEffect(() => {
    initScene();

    // Cleanup
    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      if (rendererRef.current && mountRef.current) {
        mountRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }
    };
  }, [initScene]);

  // Load model when prop changes
  useEffect(() => {
    if (model?.modelFilePath) {
      loadModel(model.modelFilePath);
    }
  }, [model?.modelFilePath, loadModel]);

  // Update appearance when settings change
  useEffect(() => {
    updateModelAppearance();
  }, [updateModelAppearance]);

  useEffect(() => {
    updateBackground();
  }, [updateBackground]);

  useEffect(() => {
    updateGrid();
  }, [updateGrid]);

  // Handle resize
  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  return (
    <Paper 
      elevation={3} 
      sx={{ 
        position: 'relative',
        width: isFullscreen ? '100vw' : width,
        height: isFullscreen ? '100vh' : height,
        ...(isFullscreen && {
          position: 'fixed',
          top: 0,
          left: 0,
          zIndex: 9999
        })
      }}
    >
      {/* Toolbar */}
      <Toolbar variant="dense" sx={{ minHeight: 48, backgroundColor: 'rgba(0,0,0,0.05)' }}>
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          3D Model Viewer
        </Typography>
        
        <Tooltip title="Reset Camera">
          <IconButton onClick={resetCamera} size="small">
            <CameraAlt />
          </IconButton>
        </Tooltip>

        <Tooltip title={settings.showGrid ? "Hide Grid" : "Show Grid"}>
          <IconButton 
            onClick={() => setSettings(prev => ({ ...prev, showGrid: !prev.showGrid }))}
            size="small"
          >
            {settings.showGrid ? <GridOff /> : <GridOn />}
          </IconButton>
        </Tooltip>

        <Tooltip title="Settings">
          <IconButton 
            onClick={(e) => setSettingsAnchor(e.currentTarget)}
            size="small"
          >
            <Settings />
          </IconButton>
        </Tooltip>

        <Tooltip title="Export">
          <IconButton 
            onClick={(e) => setExportAnchor(e.currentTarget)}
            size="small"
          >
            <Download />
          </IconButton>
        </Tooltip>

        <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
          <IconButton onClick={toggleFullscreen} size="small">
            {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
          </IconButton>
        </Tooltip>
      </Toolbar>

      {/* 3D Viewer Container */}
      <Box 
        ref={mountRef} 
        sx={{ 
          width: '100%', 
          height: isFullscreen ? 'calc(100vh - 48px)' : height - 48,
          position: 'relative',
          overflow: 'hidden'
        }} 
      />

      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: 48,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255,255,255,0.8)',
            zIndex: 1000
          }}
        >
          <CircularProgress />
        </Box>
      )}

      {/* Error Display */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            position: 'absolute', 
            top: 60, 
            left: 10, 
            right: 10,
            zIndex: 1001
          }}
          onClose={() => setError(null)}
        >
          {error.message}
          {error.details && (
            <Typography variant="caption" display="block">
              {error.details}
            </Typography>
          )}
        </Alert>
      )}

      {/* Settings Menu */}
      <Menu
        anchorEl={settingsAnchor}
        open={Boolean(settingsAnchor)}
        onClose={() => setSettingsAnchor(null)}
        PaperProps={{ sx: { minWidth: 300, p: 2 } }}
      >
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={settings.wireframe}
                onChange={(e) => setSettings(prev => ({ ...prev, wireframe: e.target.checked }))}
              />
            }
            label="Wireframe Mode"
          />
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography gutterBottom>Transparency</Typography>
          <Slider
            value={settings.transparency}
            onChange={(_, value) => setSettings(prev => ({ ...prev, transparency: value as number }))}
            min={0}
            max={0.9}
            step={0.1}
            valueLabelDisplay="auto"
            valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
          />
        </Box>

        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={settings.showGrid}
                onChange={(e) => setSettings(prev => ({ ...prev, showGrid: e.target.checked }))}
              />
            }
            label="Show Grid"
          />
        </Box>
      </Menu>

      {/* Export Menu */}
      <Menu
        anchorEl={exportAnchor}
        open={Boolean(exportAnchor)}
        onClose={() => setExportAnchor(null)}
      >
        <MenuItem onClick={() => { exportModel('stl'); setExportAnchor(null); }}>
          Export STL
        </MenuItem>
        <MenuItem onClick={() => { exportModel('screenshot'); setExportAnchor(null); }}>
          Export Screenshot
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default Model3DViewer;