import React, { useState, useCallback, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  LinearProgress,
  Alert,
  IconButton,
  Chip,
  Stack
} from '@mui/material';
import {
  CloudUpload,
  Cancel,
  CheckCircle,
  Error as ErrorIcon,
  Refresh
} from '@mui/icons-material';
import { UploadedFile, FileValidationResult, SUPPORTED_FILE_TYPES, MAX_FILE_SIZE } from '../types/file';
import { FilePreview } from './FilePreview';
import { FileManagement } from './FileManagement';

interface FileUploadProps {
  onFileUpload: (files: UploadedFile[]) => void;
  onFileRemove: (fileId: string) => void;
  onFileRename: (fileId: string, newName: string) => void;
  onFileDuplicate: (fileId: string) => void;
  onRetryUpload: (fileId: string) => void;
  uploadedFiles: UploadedFile[];
  maxFiles?: number;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFileUpload,
  onFileRemove,
  onFileRename,
  onFileDuplicate,
  onRetryUpload,
  uploadedFiles,
  maxFiles = 10
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = useCallback((file: File): FileValidationResult => {
    const errors: string[] = [];

    // Check file type
    const supportedTypes = Object.keys(SUPPORTED_FILE_TYPES);
    if (!supportedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.dxf')) {
      errors.push(`Unsupported file type: ${file.type}. Supported formats: PDF, PNG, JPG, TIFF, DXF`);
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      errors.push(`File size too large: ${(file.size / 1024 / 1024).toFixed(1)}MB. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB`);
    }

    // Check if file already exists
    const existingFile = uploadedFiles.find(f => f.name === file.name && f.size === file.size);
    if (existingFile) {
      errors.push(`File "${file.name}" already uploaded`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, [uploadedFiles]);

  const processFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newFiles: UploadedFile[] = [];
    const allErrors: string[] = [];

    // Check total file limit
    if (uploadedFiles.length + fileArray.length > maxFiles) {
      allErrors.push(`Cannot upload more than ${maxFiles} files. Currently have ${uploadedFiles.length} files.`);
      setValidationErrors(allErrors);
      return;
    }

    fileArray.forEach((file) => {
      const validation = validateFile(file);
      
      if (validation.isValid) {
        const uploadedFile: UploadedFile = {
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          file,
          status: 'pending',
          progress: 0
        };

        // Create preview URL for images
        if (file.type.startsWith('image/')) {
          uploadedFile.previewUrl = URL.createObjectURL(file);
        }

        newFiles.push(uploadedFile);
      } else {
        allErrors.push(...validation.errors);
      }
    });

    if (allErrors.length > 0) {
      setValidationErrors(allErrors);
    } else {
      setValidationErrors([]);
    }

    if (newFiles.length > 0) {
      onFileUpload(newFiles);
    }
  }, [uploadedFiles, maxFiles, validateFile, onFileUpload]);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processFiles(files);
    }
  }, [processFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [processFiles]);

  const handleBrowseClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const clearErrors = useCallback(() => {
    setValidationErrors([]);
  }, []);

  const getSupportedFormatsText = () => {
    return Object.values(SUPPORTED_FILE_TYPES).join(', ');
  };

  return (
    <Box>
      {/* Drag and Drop Upload Area */}
      <Paper
        elevation={isDragOver ? 8 : 2}
        sx={{
          p: 4,
          textAlign: 'center',
          border: isDragOver ? '2px dashed #1976d2' : '2px dashed #ccc',
          backgroundColor: isDragOver ? 'action.hover' : 'background.paper',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            backgroundColor: 'action.hover',
            borderColor: 'primary.main'
          }
        }}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleBrowseClick}
      >
        <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        
        <Typography variant="h6" gutterBottom>
          Drag & Drop Files Here
        </Typography>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          or click to browse files
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<CloudUpload />}
          onClick={handleBrowseClick}
          sx={{ mb: 2 }}
        >
          Choose Files
        </Button>
        
        <Typography variant="caption" display="block" color="text.secondary">
          Supported formats: {getSupportedFormatsText()}
        </Typography>
        
        <Typography variant="caption" display="block" color="text.secondary">
          Maximum file size: {MAX_FILE_SIZE / 1024 / 1024}MB
        </Typography>
        
        <Typography variant="caption" display="block" color="text.secondary">
          Maximum files: {maxFiles}
        </Typography>
      </Paper>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={Object.keys(SUPPORTED_FILE_TYPES).join(',')}
        style={{ display: 'none' }}
        onChange={handleFileSelect}
      />

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert 
          severity="error" 
          sx={{ mt: 2 }}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={clearErrors}
            >
              <Cancel fontSize="inherit" />
            </IconButton>
          }
        >
          <Typography variant="subtitle2" gutterBottom>
            Upload Errors:
          </Typography>
          {validationErrors.map((error, index) => (
            <Typography key={index} variant="body2">
              • {error}
            </Typography>
          ))}
        </Alert>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <Box mt={3}>
          <Typography variant="h6" gutterBottom>
            Uploaded Files ({uploadedFiles.length}/{maxFiles})
          </Typography>
          
          <Stack spacing={2}>
            {uploadedFiles.map((file) => (
              <Paper key={file.id} elevation={1} sx={{ p: 2 }}>
                <Box display="flex" alignItems="center" gap={2}>
                  {/* File Status Icon */}
                  <Box>
                    {file.status === 'completed' && (
                      <CheckCircle color="success" />
                    )}
                    {file.status === 'error' && (
                      <ErrorIcon color="error" />
                    )}
                    {(file.status === 'uploading' || file.status === 'processing') && (
                      <Box sx={{ width: 24, height: 24 }}>
                        <LinearProgress 
                          variant="determinate" 
                          value={file.progress}
                          sx={{ height: 4, borderRadius: 2 }}
                        />
                      </Box>
                    )}
                    {file.status === 'pending' && (
                      <CloudUpload color="action" />
                    )}
                  </Box>

                  {/* File Info */}
                  <Box flex={1}>
                    <Typography variant="subtitle2" noWrap>
                      {file.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </Typography>
                    
                    {/* Status Chip */}
                    <Chip
                      label={file.status.toUpperCase()}
                      size="small"
                      color={
                        file.status === 'completed' ? 'success' :
                        file.status === 'error' ? 'error' :
                        file.status === 'processing' ? 'warning' : 'default'
                      }
                      sx={{ ml: 1 }}
                    />
                  </Box>

                  {/* Action Buttons */}
                  <Box>
                    {file.status === 'error' && (
                      <IconButton
                        size="small"
                        onClick={() => onRetryUpload(file.id)}
                        title="Retry Upload"
                      >
                        <Refresh />
                      </IconButton>
                    )}
                    <IconButton
                      size="small"
                      onClick={() => onFileRemove(file.id)}
                      title="Remove File"
                    >
                      <Cancel />
                    </IconButton>
                  </Box>
                </Box>

                {/* Progress Bar */}
                {(file.status === 'uploading' || file.status === 'processing') && (
                  <Box mt={1}>
                    <LinearProgress 
                      variant="determinate" 
                      value={file.progress}
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {file.progress}% - {file.status === 'uploading' ? 'Uploading' : 'Processing'}
                    </Typography>
                  </Box>
                )}

                {/* Error Message */}
                {file.status === 'error' && file.error && (
                  <Alert severity="error" sx={{ mt: 1 }}>
                    {file.error}
                  </Alert>
                )}

                {/* File Preview */}
                {file.previewUrl && (
                  <Box mt={2}>
                    <FilePreview file={file} />
                  </Box>
                )}
              </Paper>
            ))}
          </Stack>

          {/* File Management */}
          <Box mt={2}>
            <FileManagement
              files={uploadedFiles}
              onRename={onFileRename}
              onDuplicate={onFileDuplicate}
              onRemove={onFileRemove}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
};