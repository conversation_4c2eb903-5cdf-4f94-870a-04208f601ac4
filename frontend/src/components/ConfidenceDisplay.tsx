import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';

export interface ConfidenceMetric {
  component: string;
  score: number;
  level: 'very_high' | 'high' | 'medium' | 'low' | 'very_low';
  details: Record<string, any>;
  issues: string[];
  suggestions: string[];
}

export interface OverallConfidence {
  overall_score: number;
  overall_level: 'very_high' | 'high' | 'medium' | 'low' | 'very_low';
  component_scores: Record<string, ConfidenceMetric>;
  critical_issues: string[];
  warnings: string[];
  recommendations: string[];
}

interface ConfidenceDisplayProps {
  confidence: OverallConfidence;
  showDetails?: boolean;
  onComponentClick?: (component: string) => void;
}

const ConfidenceDisplay: React.FC<ConfidenceDisplayProps> = ({
  confidence,
  showDetails = true,
  onComponentClick
}) => {
  const getConfidenceLevelColor = (level: string) => {
    switch (level) {
      case 'very_high': return 'success';
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'error';
      case 'very_low': return 'error';
      default: return 'info';
    }
  };

  const getConfidenceLevelIcon = (level: string) => {
    switch (level) {
      case 'very_high':
      case 'high':
        return <CheckCircleIcon color="success" />;
      case 'medium':
        return <WarningIcon color="warning" />;
      case 'low':
      case 'very_low':
        return <ErrorIcon color="error" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  const formatComponentName = (component: string) => {
    return component.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatConfidenceLevel = (level: string) => {
    return level.replace(/_/g, ' ').toUpperCase();
  };

  const getScoreDescription = (score: number) => {
    if (score >= 0.9) return 'Excellent';
    if (score >= 0.75) return 'Good';
    if (score >= 0.5) return 'Fair';
    if (score >= 0.25) return 'Poor';
    return 'Very Poor';
  };

  return (
    <Card>
      <CardContent>
        {/* Overall Confidence Header */}
        <Box mb={3}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            {getConfidenceLevelIcon(confidence.overall_level)}
            <Typography variant="h6">
              Analysis Confidence: {getScoreDescription(confidence.overall_score)}
            </Typography>
            <Chip 
              label={formatConfidenceLevel(confidence.overall_level)}
              color={getConfidenceLevelColor(confidence.overall_level) as any}
              size="small"
            />
          </Box>

          {/* Overall Score Bar */}
          <Box mb={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="body2" color="text.secondary">
                Overall Score
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {Math.round(confidence.overall_score * 100)}%
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={confidence.overall_score * 100}
              color={getConfidenceLevelColor(confidence.overall_level) as any}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>

          {/* Critical Issues Alert */}
          {confidence.critical_issues.length > 0 && (
            <Alert severity="error" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Critical Issues Detected:
              </Typography>
              <List dense>
                {confidence.critical_issues.map((issue, index) => (
                  <ListItem key={index} sx={{ py: 0 }}>
                    <ListItemText 
                      primary={issue}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Alert>
          )}

          {/* Warnings Alert */}
          {confidence.warnings.length > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Warnings:
              </Typography>
              <List dense>
                {confidence.warnings.map((warning, index) => (
                  <ListItem key={index} sx={{ py: 0 }}>
                    <ListItemText 
                      primary={warning}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Alert>
          )}
        </Box>

        {/* Component Details */}
        {showDetails && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Component Analysis
            </Typography>
            
            {Object.entries(confidence.component_scores).map(([component, metric]) => (
              <Accordion key={component} sx={{ mb: 1 }}>
                <AccordionSummary 
                  expandIcon={<ExpandMoreIcon />}
                  onClick={() => onComponentClick?.(component)}
                >
                  <Box display="flex" alignItems="center" gap={2} width="100%">
                    {getConfidenceLevelIcon(metric.level)}
                    <Box flex={1}>
                      <Typography variant="subtitle1">
                        {formatComponentName(component)}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={2} mt={0.5}>
                        <LinearProgress 
                          variant="determinate" 
                          value={metric.score * 100}
                          color={getConfidenceLevelColor(metric.level) as any}
                          sx={{ width: 100, height: 4 }}
                        />
                        <Typography variant="body2" color="text.secondary">
                          {Math.round(metric.score * 100)}%
                        </Typography>
                        <Chip 
                          label={formatConfidenceLevel(metric.level)}
                          color={getConfidenceLevelColor(metric.level) as any}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                  </Box>
                </AccordionSummary>
                
                <AccordionDetails>
                  {/* Component Details */}
                  {Object.keys(metric.details).length > 0 && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Details:
                      </Typography>
                      <Box display="flex" gap={1} flexWrap="wrap">
                        {Object.entries(metric.details).map(([key, value]) => (
                          <Tooltip key={key} title={`${key}: ${value}`}>
                            <Chip 
                              label={`${key}: ${typeof value === 'number' ? Math.round(value * 100) / 100 : value}`}
                              size="small"
                              variant="outlined"
                            />
                          </Tooltip>
                        ))}
                      </Box>
                    </Box>
                  )}

                  {/* Issues */}
                  {metric.issues.length > 0 && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom color="error">
                        Issues:
                      </Typography>
                      <List dense>
                        {metric.issues.map((issue, index) => (
                          <ListItem key={index} sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <ErrorIcon fontSize="small" color="error" />
                            </ListItemIcon>
                            <ListItemText 
                              primary={issue}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}

                  {/* Suggestions */}
                  {metric.suggestions.length > 0 && (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom color="primary">
                        Suggestions:
                      </Typography>
                      <List dense>
                        {metric.suggestions.map((suggestion, index) => (
                          <ListItem key={index} sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <TrendingUpIcon fontSize="small" color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary={suggestion}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        )}

        {/* Recommendations */}
        {confidence.recommendations.length > 0 && (
          <Box mt={3}>
            <Typography variant="h6" gutterBottom>
              Recommendations
            </Typography>
            <Alert severity="info">
              <List dense>
                {confidence.recommendations.map((recommendation, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <TrendingUpIcon fontSize="small" color="info" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={recommendation}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Alert>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default ConfidenceDisplay;