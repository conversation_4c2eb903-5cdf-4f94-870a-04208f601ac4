import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  Snackbar,
  Paper
} from '@mui/material';
import { FileUpload } from './FileUpload';
import { UploadedFile, ProcessingStatus } from '../types/file';
import { fileService } from '../services/fileService';

export const UploadContainer: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info'>('info');

  // Load user's existing files on component mount
  useEffect(() => {
    loadUserFiles();
  }, []);

  const loadUserFiles = async () => {
    try {
      const files = await fileService.getUserFiles();
      setUploadedFiles(files);
    } catch (error) {
      console.error('Failed to load user files:', error);
      showSnackbar('Failed to load existing files', 'error');
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' = 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const handleFileUpload = useCallback(async (newFiles: UploadedFile[]) => {
    // Add files to state immediately
    setUploadedFiles(prev => [...prev, ...newFiles]);

    // Process each file
    for (const file of newFiles) {
      try {
        // Update status to uploading
        setUploadedFiles(prev => 
          prev.map(f => 
            f.id === file.id 
              ? { ...f, status: 'uploading', progress: 0 }
              : f
          )
        );

        // Upload file
        const uploadResponse = await fileService.uploadFile(
          file.file,
          (progress) => {
            setUploadedFiles(prev => 
              prev.map(f => 
                f.id === file.id 
                  ? { ...f, progress }
                  : f
              )
            );
          }
        );

        // Update with analysis ID and start processing
        setUploadedFiles(prev => 
          prev.map(f => 
            f.id === file.id 
              ? { 
                  ...f, 
                  status: 'processing', 
                  progress: 100,
                  analysisId: uploadResponse.analysisId 
                }
              : f
          )
        );

        // Start processing status simulation
        if (uploadResponse.analysisId) {
          const stopSimulation = fileService.simulateProcessing(
            uploadResponse.analysisId,
            (status: ProcessingStatus) => {
              setUploadedFiles(prev => 
                prev.map(f => 
                  f.id === file.id 
                    ? { 
                        ...f, 
                        status: status.stage === 'complete' ? 'completed' : 'processing',
                        progress: status.progress 
                      }
                    : f
                )
              );

              if (status.stage === 'complete') {
                showSnackbar(`Processing completed for ${file.name}`, 'success');
              }
            }
          );

          // Store cleanup function (in a real app, you'd want to manage this better)
          setTimeout(() => {
            stopSimulation();
          }, 15000);
        }

        showSnackbar(`Upload started for ${file.name}`, 'info');

      } catch (error) {
        console.error('Upload failed:', error);
        
        // Update file status to error
        setUploadedFiles(prev => 
          prev.map(f => 
            f.id === file.id 
              ? { 
                  ...f, 
                  status: 'error', 
                  error: error instanceof Error ? error.message : 'Upload failed'
                }
              : f
          )
        );

        showSnackbar(`Upload failed for ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
      }
    }
  }, []);

  const handleFileRemove = useCallback(async (fileId: string) => {
    const file = uploadedFiles.find(f => f.id === fileId);
    if (!file) return;

    try {
      // If file has been uploaded to server, delete it
      if (file.analysisId) {
        // In a real implementation, you'd call the delete API
        // await fileService.deleteFile(file.analysisId);
      }

      // Remove from local state
      setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
      
      // Clean up preview URL
      if (file.previewUrl) {
        URL.revokeObjectURL(file.previewUrl);
      }

      showSnackbar(`Removed ${file.name}`, 'info');
    } catch (error) {
      console.error('Failed to remove file:', error);
      showSnackbar(`Failed to remove ${file.name}`, 'error');
    }
  }, [uploadedFiles]);

  const handleFileRename = useCallback((fileId: string, newName: string) => {
    setUploadedFiles(prev => 
      prev.map(f => 
        f.id === fileId 
          ? { ...f, name: newName }
          : f
      )
    );
    showSnackbar('File renamed successfully', 'success');
  }, []);

  const handleFileDuplicate = useCallback((fileId: string) => {
    const originalFile = uploadedFiles.find(f => f.id === fileId);
    if (!originalFile) return;

    const duplicatedFile: UploadedFile = {
      ...originalFile,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: `Copy of ${originalFile.name}`,
      status: 'pending'
    };

    setUploadedFiles(prev => [...prev, duplicatedFile]);
    showSnackbar(`Duplicated ${originalFile.name}`, 'success');
  }, [uploadedFiles]);

  const handleRetryUpload = useCallback(async (fileId: string) => {
    const file = uploadedFiles.find(f => f.id === fileId);
    if (!file) return;

    // Reset file status and retry upload
    setUploadedFiles(prev => 
      prev.map(f => 
        f.id === fileId 
          ? { ...f, status: 'pending', progress: 0, error: undefined }
          : f
      )
    );

    // Retry the upload
    await handleFileUpload([file]);
  }, [uploadedFiles, handleFileUpload]);

  return (
    <Box>
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Upload Engineering Drawings
        </Typography>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          Upload your engineering drawings to automatically generate Bills of Materials, 
          calculate weights, and create 3D models. Supported formats include PDF, DXF, 
          PNG, JPG, and TIFF files.
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Tips for best results:</strong>
            <br />
            • Use high-resolution images (300 DPI or higher)
            • Ensure drawings have clear, dark lines
            • Include dimensions and material specifications
            • Make sure text is legible and not too small
          </Typography>
        </Alert>
      </Paper>

      <FileUpload
        onFileUpload={handleFileUpload}
        onFileRemove={handleFileRemove}
        onFileRename={handleFileRename}
        onFileDuplicate={handleFileDuplicate}
        onRetryUpload={handleRetryUpload}
        uploadedFiles={uploadedFiles}
        maxFiles={10}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};