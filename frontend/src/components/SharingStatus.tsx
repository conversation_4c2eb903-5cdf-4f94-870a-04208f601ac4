import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  Tooltip,
  IconButton,
  Popover,
  List,
  ListItem,
  ListItemText,
  Typography,
  Avatar,
  Badge,
} from '@mui/material';
import {
  Share as ShareIcon,
  People as PeopleIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import sharingService, { DesignShare } from '../services/sharingService';

interface SharingStatusProps {
  designId: number;
  isOwner: boolean;
  compact?: boolean;
}

const SharingStatus: React.FC<SharingStatusProps> = ({
  designId,
  isOwner,
  compact = false,
}) => {
  const [shares, setShares] = useState<DesignShare[]>([]);
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  // Load sharing information
  const loadShares = async () => {
    if (!isOwner) return; // Only owners can see sharing details
    
    try {
      setLoading(true);
      const response = await sharingService.getDesignShares(designId);
      setShares(response.shares);
    } catch (error) {
      console.error('Failed to load sharing information:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadShares();
  }, [designId, isOwner]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (shares.length > 0) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  if (!isOwner) {
    // For shared designs, show that it's shared with you
    return (
      <Tooltip title="This design is shared with you">
        <Chip
          size="small"
          icon={<ShareIcon />}
          label="Shared"
          color="secondary"
          variant="outlined"
        />
      </Tooltip>
    );
  }

  if (shares.length === 0) {
    return compact ? null : (
      <Tooltip title="Not shared">
        <Chip
          size="small"
          icon={<ShareIcon />}
          label="Private"
          variant="outlined"
        />
      </Tooltip>
    );
  }

  const shareCount = shares.length;
  const editCount = shares.filter(share => share.permission_level === 'edit').length;
  const viewCount = shares.filter(share => share.permission_level === 'view').length;

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <Tooltip title={`Shared with ${shareCount} user${shareCount !== 1 ? 's' : ''}`}>
        <IconButton
          size="small"
          onClick={handleClick}
          color="primary"
        >
          <Badge badgeContent={shareCount} color="primary">
            <PeopleIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      {!compact && (
        <Box display="flex" gap={0.5}>
          {editCount > 0 && (
            <Tooltip title={`${editCount} user${editCount !== 1 ? 's' : ''} can edit`}>
              <Chip
                size="small"
                icon={<EditIcon />}
                label={editCount}
                color="primary"
                variant="outlined"
              />
            </Tooltip>
          )}
          {viewCount > 0 && (
            <Tooltip title={`${viewCount} user${viewCount !== 1 ? 's' : ''} can view`}>
              <Chip
                size="small"
                icon={<ViewIcon />}
                label={viewCount}
                color="default"
                variant="outlined"
              />
            </Tooltip>
          )}
        </Box>
      )}

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Box sx={{ p: 2, minWidth: 300 }}>
          <Typography variant="h6" gutterBottom>
            Shared with ({shareCount})
          </Typography>
          
          <List dense>
            {shares.map((share) => (
              <ListItem key={share.share_id} divider>
                <Avatar sx={{ mr: 2, width: 32, height: 32 }}>
                  {share.username.substring(0, 2).toUpperCase()}
                </Avatar>
                <ListItemText
                  primary={share.username}
                  secondary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="caption" color="textSecondary">
                        {share.email}
                      </Typography>
                      <Chip
                        size="small"
                        icon={share.permission_level === 'edit' ? <EditIcon /> : <ViewIcon />}
                        label={share.permission_level}
                        color={share.permission_level === 'edit' ? 'primary' : 'default'}
                        variant="outlined"
                      />
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
          
          <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
            Click the share button to manage access
          </Typography>
        </Box>
      </Popover>
    </Box>
  );
};

export default SharingStatus;