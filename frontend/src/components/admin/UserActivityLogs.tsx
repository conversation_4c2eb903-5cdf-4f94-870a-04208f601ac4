/**
 * User Activity Logs Component
 * Displays user activity and audit logs for admin monitoring
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  CircularProgress,
  Button,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { adminService, UserActivityLog } from '../../services/adminService';

export const UserActivityLogs: React.FC = () => {
  const [activityLogs, setActivityLogs] = useState<UserActivityLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    user_id: '',
    action: '',
    days: 7,
  });

  useEffect(() => {
    loadActivityLogs();
  }, [filters]);

  const loadActivityLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = { days: filters.days };
      if (filters.user_id) params.user_id = parseInt(filters.user_id);
      if (filters.action) params.action = filters.action;
      
      const data = await adminService.getUserActivityLogs(params);
      setActivityLogs(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load activity logs');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadActivityLogs();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'login':
        return 'success';
      case 'logout':
        return 'info';
      case 'create':
        return 'primary';
      case 'update':
        return 'warning';
      case 'delete':
        return 'error';
      default:
        return 'default';
    }
  };

  const getUniqueActions = () => {
    const actions = [...new Set(activityLogs.map(log => log.action))];
    return actions.sort();
  };

  const getUniqueUsers = () => {
    const users = [...new Set(activityLogs.map(log => ({ id: log.user_id, username: log.username })))];
    return users.sort((a, b) => a.username.localeCompare(b.username));
  };

  if (loading && activityLogs.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">User Activity Logs</Typography>
        <Button
          variant="outlined"
          startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filters
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>User</InputLabel>
            <Select
              value={filters.user_id}
              label="User"
              onChange={(e) => setFilters({ ...filters, user_id: e.target.value })}
            >
              <MenuItem value="">All Users</MenuItem>
              {getUniqueUsers().map((user) => (
                <MenuItem key={user.id} value={user.id.toString()}>
                  {user.username}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Action</InputLabel>
            <Select
              value={filters.action}
              label="Action"
              onChange={(e) => setFilters({ ...filters, action: e.target.value })}
            >
              <MenuItem value="">All Actions</MenuItem>
              {getUniqueActions().map((action) => (
                <MenuItem key={action} value={action}>
                  {action}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={filters.days}
              label="Time Range"
              onChange={(e) => setFilters({ ...filters, days: Number(e.target.value) })}
            >
              <MenuItem value={1}>Last 24 hours</MenuItem>
              <MenuItem value={7}>Last 7 days</MenuItem>
              <MenuItem value={30}>Last 30 days</MenuItem>
              <MenuItem value={90}>Last 90 days</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>

      {/* Activity Summary */}
      <Box display="flex" gap={2} mb={3} flexWrap="wrap">
        <Chip
          label={`Total Activities: ${activityLogs.length}`}
          color="primary"
          variant="outlined"
        />
        {getUniqueActions().map((action) => (
          <Chip
            key={action}
            label={`${action}: ${activityLogs.filter(log => log.action === action).length}`}
            color={getActionColor(action) as any}
            variant="outlined"
          />
        ))}
      </Box>

      {/* Activity Logs Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell>Action</TableCell>
              <TableCell>Resource</TableCell>
              <TableCell>IP Address</TableCell>
              <TableCell>Timestamp</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {activityLogs.map((log) => (
              <TableRow key={log.id}>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {log.username}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ID: {log.user_id}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={log.action}
                    color={getActionColor(log.action) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {log.resource_type && (
                    <Box>
                      <Typography variant="body2">
                        {log.resource_type}
                      </Typography>
                      {log.resource_id && (
                        <Typography variant="caption" color="text.secondary">
                          ID: {log.resource_id}
                        </Typography>
                      )}
                    </Box>
                  )}
                  {!log.resource_type && (
                    <Typography variant="body2" color="text.secondary">
                      N/A
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {log.ip_address || 'Unknown'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(log.timestamp)}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {activityLogs.length === 0 && !loading && (
        <Box textAlign="center" py={4}>
          <Typography variant="body1" color="text.secondary">
            No activity logs found for the selected filters.
          </Typography>
        </Box>
      )}
    </Box>
  );
};