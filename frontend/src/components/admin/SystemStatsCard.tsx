/**
 * System Statistics Card Component
 * Displays system overview statistics for admin dashboard
 */
import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { adminService, SystemStats } from '../../services/adminService';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color = 'primary', subtitle }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box display="flex" alignItems="center" mb={2}>
        <Box
          sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: `${color}.light`,
            color: `${color}.contrastText`,
            mr: 2,
          }}
        >
          {icon}
        </Box>
        <Typography variant="h6" component="div" color="text.secondary">
          {title}
        </Typography>
      </Box>
      <Typography variant="h4" component="div" fontWeight="bold">
        {value}
      </Typography>
      {subtitle && (
        <Typography variant="body2" color="text.secondary" mt={1}>
          {subtitle}
        </Typography>
      )}
    </CardContent>
  </Card>
);

export const SystemStatsCard: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminService.getSystemStats();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load system statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No statistics available
      </Alert>
    );
  }

  const successRate = stats.total_analyses > 0 
    ? ((stats.successful_analyses / stats.total_analyses) * 100).toFixed(1)
    : '0';

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        System Overview
      </Typography>
      
      <Grid container spacing={3}>
        {/* User Statistics */}
        <Grid item xs={12} sm={6} md={4} component="div">
          <StatCard
            title="Total Users"
            value={stats.total_users}
            icon={<PeopleIcon />}
            color="primary"
            subtitle={`${stats.active_users} active, ${stats.admin_users} admins`}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} component="div">
          <StatCard
            title="Total Designs"
            value={stats.total_designs}
            icon={<AssignmentIcon />}
            color="info"
            subtitle={`${stats.designs_this_month} this month`}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} component="div">
          <StatCard
            title="Analysis Success Rate"
            value={`${successRate}%`}
            icon={<CheckCircleIcon />}
            color="success"
            subtitle={`${stats.successful_analyses}/${stats.total_analyses} successful`}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} component="div">
          <StatCard
            title="Failed Analyses"
            value={stats.failed_analyses}
            icon={<ErrorIcon />}
            color="error"
            subtitle="Require attention"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} component="div">
          <StatCard
            title="Storage Used"
            value={`${stats.total_storage_mb.toFixed(1)} MB`}
            icon={<StorageIcon />}
            color="warning"
            subtitle="File storage"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} component="div">
          <StatCard
            title="Avg Processing Time"
            value={`${stats.avg_processing_time_seconds.toFixed(1)}s`}
            icon={<SpeedIcon />}
            color="secondary"
            subtitle="Per analysis"
          />
        </Grid>
      </Grid>

      {/* Quick Status Indicators */}
      <Box mt={4}>
        <Typography variant="h6" gutterBottom>
          System Status
        </Typography>
        <Box display="flex" gap={1} flexWrap="wrap">
          <Chip
            label={`${stats.active_users} Active Users`}
            color={stats.active_users > 0 ? 'success' : 'default'}
            variant="outlined"
          />
          <Chip
            label={`${stats.designs_this_month} Designs This Month`}
            color={stats.designs_this_month > 0 ? 'primary' : 'default'}
            variant="outlined"
          />
          <Chip
            label={`${successRate}% Success Rate`}
            color={parseFloat(successRate) > 80 ? 'success' : parseFloat(successRate) > 60 ? 'warning' : 'error'}
            variant="outlined"
          />
        </Box>
      </Box>
    </Box>
  );
};