/**
 * Materials Management Component
 * Admin interface for managing material database
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { adminService, Material, MaterialCreate, MaterialUpdate } from '../../services/adminService';

export const MaterialsManagement: React.FC = () => {
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [formData, setFormData] = useState<MaterialCreate>({
    name: '',
    density: 0,
    category: '',
    description: '',
  });

  useEffect(() => {
    loadMaterials();
  }, [searchTerm, categoryFilter]);

  const loadMaterials = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (categoryFilter) params.category = categoryFilter;
      
      const data = await adminService.getMaterials(params);
      setMaterials(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load materials');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMaterial = async () => {
    try {
      await adminService.createMaterial(formData);
      setCreateDialogOpen(false);
      setFormData({ name: '', density: 0, category: '', description: '' });
      await loadMaterials();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create material');
    }
  };

  const handleEditMaterial = async () => {
    if (!selectedMaterial) return;
    
    try {
      const updateData: MaterialUpdate = {
        name: formData.name || undefined,
        density: formData.density || undefined,
        category: formData.category || undefined,
        description: formData.description || undefined,
      };
      
      await adminService.updateMaterial(selectedMaterial.id, updateData);
      setEditDialogOpen(false);
      setSelectedMaterial(null);
      setFormData({ name: '', density: 0, category: '', description: '' });
      await loadMaterials();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update material');
    }
  };

  const handleDeleteMaterial = async () => {
    if (!selectedMaterial) return;
    
    try {
      await adminService.deleteMaterial(selectedMaterial.id);
      setDeleteDialogOpen(false);
      setSelectedMaterial(null);
      await loadMaterials();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete material');
    }
  };

  const openEditDialog = (material: Material) => {
    setSelectedMaterial(material);
    setFormData({
      name: material.name,
      density: material.density,
      category: material.category,
      description: material.description || '',
    });
    setEditDialogOpen(true);
  };

  const openDeleteDialog = (material: Material) => {
    setSelectedMaterial(material);
    setDeleteDialogOpen(true);
  };

  const getUniqueCategories = () => {
    const categories = [...new Set(materials.map(m => m.category))];
    return categories.sort();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && materials.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Materials Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Add Material
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
          <TextField
            label="Search materials"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
            sx={{ minWidth: 250 }}
          />
          
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={categoryFilter}
              label="Category"
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <MenuItem value="">All Categories</MenuItem>
              {getUniqueCategories().map((category) => (
                <MenuItem key={category} value={category}>
                  {category}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Paper>

      {/* Materials Summary */}
      <Box display="flex" gap={2} mb={3}>
        <Chip
          label={`Total Materials: ${materials.length}`}
          color="primary"
          variant="outlined"
        />
        {getUniqueCategories().map((category) => (
          <Chip
            key={category}
            label={`${category}: ${materials.filter(m => m.category === category).length}`}
            variant="outlined"
          />
        ))}
      </Box>

      {/* Materials Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Density (kg/m³)</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {materials.map((material) => (
              <TableRow key={material.id}>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {material.name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip label={material.category} size="small" />
                </TableCell>
                <TableCell>{material.density}</TableCell>
                <TableCell>
                  <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                    {material.description || 'No description'}
                  </Typography>
                </TableCell>
                <TableCell>{formatDate(material.created_at)}</TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    <Tooltip title="Edit Material">
                      <IconButton
                        size="small"
                        onClick={() => openEditDialog(material)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Delete Material">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => openDeleteDialog(material)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {materials.length === 0 && !loading && (
        <Box textAlign="center" py={4}>
          <Typography variant="body1" color="text.secondary">
            No materials found. Add some materials to get started.
          </Typography>
        </Box>
      )}

      {/* Create Material Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Material</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              label="Material Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Density (kg/m³)"
              type="number"
              value={formData.density}
              onChange={(e) => setFormData({ ...formData, density: parseFloat(e.target.value) || 0 })}
              fullWidth
              required
            />
            <TextField
              label="Category"
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              fullWidth
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateMaterial} variant="contained">
            Add Material
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Material Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Material</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              label="Material Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Density (kg/m³)"
              type="number"
              value={formData.density}
              onChange={(e) => setFormData({ ...formData, density: parseFloat(e.target.value) || 0 })}
              fullWidth
              required
            />
            <TextField
              label="Category"
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              fullWidth
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleEditMaterial} variant="contained">
            Update Material
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Material Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Material</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the material "{selectedMaterial?.name}"? 
            This action cannot be undone and may affect existing designs that use this material.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteMaterial} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};