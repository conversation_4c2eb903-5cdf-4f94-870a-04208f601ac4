/**
 * System Health Component
 * Displays system health metrics and monitoring information
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  Alert,
  CircularProgress,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Storage as DatabaseIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { adminService, SystemHealthMetric } from '../../services/adminService';

interface HealthMetricCardProps {
  metric: SystemHealthMetric;
}

const HealthMetricCard: React.FC<HealthMetricCardProps> = ({ metric }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'critical':
        return <ErrorIcon color="error" />;
      default:
        return <CheckCircleIcon />;
    }
  };

  const getProgressValue = () => {
    if (metric.unit === '%') {
      return Math.min(metric.value, 100);
    }
    return 0;
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">{metric.metric_name}</Typography>
          <Chip
            label={metric.status}
            color={getStatusColor(metric.status) as any}
            size="small"
            icon={getStatusIcon(metric.status)}
          />
        </Box>
        
        <Typography variant="h4" component="div" gutterBottom>
          {metric.value} {metric.unit}
        </Typography>
        
        {metric.unit === '%' && (
          <LinearProgress
            variant="determinate"
            value={getProgressValue()}
            color={getStatusColor(metric.status) as any}
            sx={{ height: 8, borderRadius: 4, mb: 1 }}
          />
        )}
        
        <Typography variant="body2" color="text.secondary">
          Last updated: {new Date(metric.last_updated).toLocaleString()}
        </Typography>
      </CardContent>
    </Card>
  );
};

export const SystemHealth: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemHealthMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    loadHealthMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadHealthMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadHealthMetrics = async () => {
    try {
      setError(null);
      const data = await adminService.getSystemHealth();
      setMetrics(data);
      setLastRefresh(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load system health metrics');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    loadHealthMetrics();
  };

  const getOverallStatus = () => {
    if (metrics.length === 0) return 'unknown';
    
    const hasCritical = metrics.some(m => m.status === 'critical');
    const hasWarning = metrics.some(m => m.status === 'warning');
    
    if (hasCritical) return 'critical';
    if (hasWarning) return 'warning';
    return 'healthy';
  };

  const getOverallStatusColor = () => {
    const status = getOverallStatus();
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading && metrics.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">System Health</Typography>
        <Button
          variant="outlined"
          startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Overall Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6">Overall System Status:</Typography>
            <Chip
              label={getOverallStatus().toUpperCase()}
              color={getOverallStatusColor() as any}
              size="medium"
            />
            <Typography variant="body2" color="text.secondary" sx={{ ml: 'auto' }}>
              Last updated: {lastRefresh.toLocaleString()}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Health Metrics Grid */}
      <Grid container spacing={3}>
        {metrics.map((metric, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <HealthMetricCard metric={metric} />
          </Grid>
        ))}
      </Grid>

      {/* Health Summary */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Health Summary
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText
                primary={`${metrics.filter(m => m.status === 'healthy').length} Healthy Metrics`}
                secondary="All systems operating normally"
              />
            </ListItem>
            
            {metrics.filter(m => m.status === 'warning').length > 0 && (
              <ListItem>
                <ListItemIcon>
                  <WarningIcon color="warning" />
                </ListItemIcon>
                <ListItemText
                  primary={`${metrics.filter(m => m.status === 'warning').length} Warning Metrics`}
                  secondary="Systems require attention"
                />
              </ListItem>
            )}
            
            {metrics.filter(m => m.status === 'critical').length > 0 && (
              <ListItem>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary={`${metrics.filter(m => m.status === 'critical').length} Critical Metrics`}
                  secondary="Immediate attention required"
                />
              </ListItem>
            )}
          </List>
        </CardContent>
      </Card>
    </Box>
  );
};