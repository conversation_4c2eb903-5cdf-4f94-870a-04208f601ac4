/**
 * Error Logs Component
 * Displays and manages system error logs
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Collapse,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { adminService, ErrorLogEntry } from '../../services/adminService';

interface ErrorRowProps {
  error: ErrorLogEntry;
  onResolve: (errorId: number) => void;
}

const ErrorRow: React.FC<ErrorRowProps> = ({ error, onResolve }) => {
  const [expanded, setExpanded] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getErrorTypeColor = (errorType: string) => {
    switch (errorType.toLowerCase()) {
      case 'validationerror':
        return 'warning';
      case 'processingerror':
        return 'error';
      case 'authenticationerror':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <>
      <TableRow>
        <TableCell>
          <IconButton
            size="small"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </TableCell>
        <TableCell>
          <Chip
            label={error.error_type}
            color={getErrorTypeColor(error.error_type) as any}
            size="small"
          />
        </TableCell>
        <TableCell>
          <Typography variant="body2" noWrap>
            {error.error_message}
          </Typography>
        </TableCell>
        <TableCell>{error.username || 'System'}</TableCell>
        <TableCell>{error.endpoint || 'N/A'}</TableCell>
        <TableCell>{formatDate(error.timestamp)}</TableCell>
        <TableCell>
          <Chip
            label={error.resolved ? 'Resolved' : 'Open'}
            color={error.resolved ? 'success' : 'error'}
            size="small"
          />
        </TableCell>
        <TableCell>
          {!error.resolved && (
            <Tooltip title="Mark as Resolved">
              <IconButton
                size="small"
                color="success"
                onClick={() => onResolve(error.id)}
              >
                <CheckCircleIcon />
              </IconButton>
            </Tooltip>
          )}
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={8}>
          <Collapse in={expanded} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Error Details
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Message:</strong> {error.error_message}
              </Typography>
              {error.stack_trace && (
                <Box>
                  <Typography variant="body2" gutterBottom>
                    <strong>Stack Trace:</strong>
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.100', maxHeight: 200, overflow: 'auto' }}>
                    <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
                      {error.stack_trace}
                    </Typography>
                  </Paper>
                </Box>
              )}
              {error.request_data && (
                <Box mt={2}>
                  <Typography variant="body2" gutterBottom>
                    <strong>Request Data:</strong>
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.100', maxHeight: 200, overflow: 'auto' }}>
                    <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
                      {error.request_data}
                    </Typography>
                  </Paper>
                </Box>
              )}
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
};

export const ErrorLogs: React.FC = () => {
  const [errors, setErrors] = useState<ErrorLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    error_type: '',
    resolved: '',
    days: 7,
  });

  useEffect(() => {
    loadErrorLogs();
  }, [filters]);

  const loadErrorLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: any = { days: filters.days };
      if (filters.error_type) params.error_type = filters.error_type;
      if (filters.resolved !== '') params.resolved = filters.resolved === 'true';
      
      const data = await adminService.getErrorLogs(params);
      setErrors(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load error logs');
    } finally {
      setLoading(false);
    }
  };

  const handleResolveError = async (errorId: number) => {
    try {
      await adminService.resolveErrorLog(errorId);
      await loadErrorLogs(); // Reload to update the resolved status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resolve error');
    }
  };

  const handleRefresh = () => {
    loadErrorLogs();
  };

  const getUniqueErrorTypes = () => {
    const types = [...new Set(errors.map(e => e.error_type))];
    return types.sort();
  };

  if (loading && errors.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Error Logs</Typography>
        <Button
          variant="outlined"
          startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filters
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Error Type</InputLabel>
            <Select
              value={filters.error_type}
              label="Error Type"
              onChange={(e) => setFilters({ ...filters, error_type: e.target.value })}
            >
              <MenuItem value="">All Types</MenuItem>
              {getUniqueErrorTypes().map((type) => (
                <MenuItem key={type} value={type}>
                  {type}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.resolved}
              label="Status"
              onChange={(e) => setFilters({ ...filters, resolved: e.target.value })}
            >
              <MenuItem value="">All</MenuItem>
              <MenuItem value="false">Open</MenuItem>
              <MenuItem value="true">Resolved</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={filters.days}
              label="Time Range"
              onChange={(e) => setFilters({ ...filters, days: Number(e.target.value) })}
            >
              <MenuItem value={1}>Last 24 hours</MenuItem>
              <MenuItem value={7}>Last 7 days</MenuItem>
              <MenuItem value={30}>Last 30 days</MenuItem>
              <MenuItem value={90}>Last 90 days</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>

      {/* Error Summary */}
      <Box display="flex" gap={2} mb={3}>
        <Chip
          label={`Total: ${errors.length}`}
          color="default"
          variant="outlined"
        />
        <Chip
          label={`Open: ${errors.filter(e => !e.resolved).length}`}
          color="error"
          variant="outlined"
        />
        <Chip
          label={`Resolved: ${errors.filter(e => e.resolved).length}`}
          color="success"
          variant="outlined"
        />
      </Box>

      {/* Error Logs Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell width={50}></TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Message</TableCell>
              <TableCell>User</TableCell>
              <TableCell>Endpoint</TableCell>
              <TableCell>Timestamp</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {errors.map((errorLog) => (
              <ErrorRow
                key={errorLog.id}
                error={errorLog}
                onResolve={handleResolveError}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {errors.length === 0 && !loading && (
        <Box textAlign="center" py={4}>
          <Typography variant="body1" color="text.secondary">
            No error logs found for the selected filters.
          </Typography>
        </Box>
      )}
    </Box>
  );
};