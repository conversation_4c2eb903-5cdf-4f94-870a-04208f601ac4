import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  CircularProgress,
  Tooltip,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  MoreVert,
  Edit,
  Delete,
  FileCopy,
  Share,
  Download,
  Visibility,
  Schedule,
  CheckCircle,
  Error,
  Pending
} from '@mui/icons-material';
import { format } from 'date-fns';
import { Design, DesignAction } from '../types/design';
import { DesignService } from '../services/designService';

interface DesignGalleryProps {
  designs: Design[];
  onDesignAction: (action: DesignAction) => void;
  onDesignClick: (design: Design) => void;
  loading?: boolean;
}

export const DesignGallery: React.FC<DesignGalleryProps> = ({
  designs,
  onDesignAction,
  onDesignClick,
  loading = false
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDesign, setSelectedDesign] = useState<Design | null>(null);
  const [renameDialog, setRenameDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [duplicateDialog, setDuplicateDialog] = useState(false);
  const [shareDialog, setShareDialog] = useState(false);
  const [newName, setNewName] = useState('');
  const [shareEmail, setShareEmail] = useState('');
  const [sharePermission, setSharePermission] = useState<'view' | 'edit'>('view');

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, design: Design) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedDesign(design);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedDesign(null);
  };

  const handleDesignClick = (design: Design) => {
    // Mark as viewed for analytics
    DesignService.markAsViewed(design.id);
    onDesignClick(design);
  };

  const handleRename = () => {
    if (selectedDesign && newName.trim()) {
      onDesignAction({
        type: 'rename',
        designId: selectedDesign.id,
        payload: { newName: newName.trim() }
      });
      setRenameDialog(false);
      setNewName('');
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedDesign) {
      onDesignAction({
        type: 'delete',
        designId: selectedDesign.id
      });
      setDeleteDialog(false);
    }
    handleMenuClose();
  };

  const handleDuplicate = () => {
    if (selectedDesign && newName.trim()) {
      onDesignAction({
        type: 'duplicate',
        designId: selectedDesign.id,
        payload: { newName: newName.trim() }
      });
      setDuplicateDialog(false);
      setNewName('');
    }
    handleMenuClose();
  };

  const handleShare = () => {
    if (selectedDesign && shareEmail.trim()) {
      onDesignAction({
        type: 'share',
        designId: selectedDesign.id,
        payload: { userEmail: shareEmail.trim(), permissionLevel: sharePermission }
      });
      setShareDialog(false);
      setShareEmail('');
      setSharePermission('view');
    }
    handleMenuClose();
  };

  const handleDownload = (format: 'bom' | 'model' | 'all') => {
    if (selectedDesign) {
      onDesignAction({
        type: 'download',
        designId: selectedDesign.id,
        payload: { format }
      });
    }
    handleMenuClose();
  };

  const getStatusIcon = (status: Design['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'processing':
        return <Pending color="warning" />;
      case 'error':
        return <Error color="error" />;
      default:
        return <Schedule color="info" />;
    }
  };

  const getStatusColor = (status: Design['status']) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'info';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (designs.length === 0) {
    return (
      <Box textAlign="center" py={8}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No designs found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Upload your first engineering drawing to get started
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {designs.map((design) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={design.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 4
                }
              }}
              onClick={() => handleDesignClick(design)}
            >
              {/* Thumbnail */}
              <CardMedia
                sx={{
                  height: 160,
                  backgroundColor: 'grey.100',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative'
                }}
              >
                {design.thumbnailUrl ? (
                  <img
                    src={design.thumbnailUrl}
                    alt={design.name}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  <Avatar
                    sx={{
                      width: 64,
                      height: 64,
                      backgroundColor: 'primary.main',
                      fontSize: '1.5rem'
                    }}
                  >
                    {design.name.substring(0, 2).toUpperCase()}
                  </Avatar>
                )}
                
                {/* Status overlay */}
                <Box
                  position="absolute"
                  top={8}
                  left={8}
                  display="flex"
                  alignItems="center"
                  gap={1}
                >
                  <Tooltip title={`Status: ${design.status}`}>
                    <Box display="flex" alignItems="center">
                      {getStatusIcon(design.status)}
                    </Box>
                  </Tooltip>
                  {design.isShared && (
                    <Tooltip title="Shared design">
                      <Share fontSize="small" color="info" />
                    </Tooltip>
                  )}
                </Box>

                {/* Menu button */}
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.9)'
                    }
                  }}
                  onClick={(e) => handleMenuClick(e, design)}
                >
                  <MoreVert />
                </IconButton>
              </CardMedia>

              <CardContent sx={{ flexGrow: 1, pb: 1 }}>
                {/* Design name */}
                <Typography
                  variant="h6"
                  component="h3"
                  gutterBottom
                  sx={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {design.name}
                </Typography>

                {/* Status chip */}
                <Box mb={1}>
                  <Chip
                    label={design.status.charAt(0).toUpperCase() + design.status.slice(1)}
                    color={getStatusColor(design.status) as any}
                    size="small"
                  />
                </Box>

                {/* Processing progress */}
                {design.status === 'processing' && (
                  <Box mb={2}>
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      {design.processingProgress.message}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={design.processingProgress.progress}
                      sx={{ height: 4, borderRadius: 2 }}
                    />
                  </Box>
                )}

                {/* File info */}
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {formatFileSize(design.fileSize)} • {design.fileType.toUpperCase()}
                </Typography>

                {/* Dates */}
                <Typography variant="caption" color="text.secondary" display="block">
                  Created: {format(new Date(design.createdAt), 'MMM dd, yyyy')}
                </Typography>
                {design.updatedAt !== design.createdAt && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    Updated: {format(new Date(design.updatedAt), 'MMM dd, yyyy')}
                  </Typography>
                )}

                {/* View count */}
                <Box display="flex" alignItems="center" gap={0.5} mt={1}>
                  <Visibility fontSize="small" color="disabled" />
                  <Typography variant="caption" color="text.secondary">
                    {design.viewCount} views
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => setRenameDialog(true)}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Rename
        </MenuItem>
        <MenuItem onClick={() => setDuplicateDialog(true)}>
          <FileCopy fontSize="small" sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        <MenuItem onClick={() => setShareDialog(true)}>
          <Share fontSize="small" sx={{ mr: 1 }} />
          Share
        </MenuItem>
        <MenuItem onClick={() => handleDownload('all')}>
          <Download fontSize="small" sx={{ mr: 1 }} />
          Download All
        </MenuItem>
        <MenuItem onClick={() => setDeleteDialog(true)} sx={{ color: 'error.main' }}>
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Rename Dialog */}
      <Dialog open={renameDialog} onClose={() => setRenameDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Rename Design</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="New Name"
            fullWidth
            variant="outlined"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            placeholder={selectedDesign?.name}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRenameDialog(false)}>Cancel</Button>
          <Button onClick={handleRename} variant="contained" disabled={!newName.trim()}>
            Rename
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>Delete Design</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{selectedDesign?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>Cancel</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Duplicate Dialog */}
      <Dialog open={duplicateDialog} onClose={() => setDuplicateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Duplicate Design</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="New Name"
            fullWidth
            variant="outlined"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            placeholder={selectedDesign ? `${selectedDesign.name} (Copy)` : ''}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDuplicateDialog(false)}>Cancel</Button>
          <Button onClick={handleDuplicate} variant="contained" disabled={!newName.trim()}>
            Duplicate
          </Button>
        </DialogActions>
      </Dialog>

      {/* Share Dialog */}
      <Dialog open={shareDialog} onClose={() => setShareDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Share Design</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="User Email"
            fullWidth
            variant="outlined"
            value={shareEmail}
            onChange={(e) => setShareEmail(e.target.value)}
            placeholder="Enter email address"
            sx={{ mb: 2 }}
          />
          <TextField
            select
            label="Permission Level"
            fullWidth
            variant="outlined"
            value={sharePermission}
            onChange={(e) => setSharePermission(e.target.value as 'view' | 'edit')}
            SelectProps={{
              native: true,
            }}
          >
            <option value="view">View Only</option>
            <option value="edit">View & Edit</option>
          </TextField>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialog(false)}>Cancel</Button>
          <Button onClick={handleShare} variant="contained" disabled={!shareEmail.trim()}>
            Share
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};