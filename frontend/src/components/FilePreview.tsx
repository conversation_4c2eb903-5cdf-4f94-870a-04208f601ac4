import React, { useState } from 'react';
import {
  Box,
  Card,
  CardMedia,
  CardContent,
  Typography,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Button,
  Chip,
  Stack
} from '@mui/material';
import {
  ZoomIn,
  Download,
  PictureAsPdf,
  Image as ImageIcon,
  Description
} from '@mui/icons-material';
import { UploadedFile } from '../types/file';

interface FilePreviewProps {
  file: UploadedFile;
  maxHeight?: number;
  showDetails?: boolean;
}

export const FilePreview: React.FC<FilePreviewProps> = ({
  file,
  maxHeight = 200,
  showDetails = true
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleDownload = () => {
    const url = URL.createObjectURL(file.file);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getFileIcon = () => {
    if (file.type === 'application/pdf') {
      return <PictureAsPdf sx={{ fontSize: 48, color: 'error.main' }} />;
    }
    if (file.type.startsWith('image/')) {
      return <ImageIcon sx={{ fontSize: 48, color: 'primary.main' }} />;
    }
    if (file.name.toLowerCase().endsWith('.dxf')) {
      return <Description sx={{ fontSize: 48, color: 'warning.main' }} />;
    }
    return <Description sx={{ fontSize: 48, color: 'text.secondary' }} />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  return (
    <>
      <Card sx={{ maxWidth: '100%' }}>
        {/* Preview Image or Icon */}
        {file.previewUrl ? (
          <CardMedia
            component="img"
            height={maxHeight}
            image={file.previewUrl}
            alt={file.name}
            sx={{
              objectFit: 'contain',
              backgroundColor: 'grey.100',
              cursor: 'pointer'
            }}
            onClick={handleOpenDialog}
          />
        ) : (
          <Box
            sx={{
              height: maxHeight,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'grey.100',
              cursor: 'pointer'
            }}
            onClick={handleOpenDialog}
          >
            {getFileIcon()}
          </Box>
        )}

        {/* File Details */}
        {showDetails && (
          <CardContent>
            <Typography variant="subtitle2" noWrap title={file.name}>
              {file.name}
            </Typography>
            
            <Stack direction="row" spacing={1} alignItems="center" mt={1}>
              <Chip
                label={file.type || 'Unknown'}
                size="small"
                variant="outlined"
              />
              <Typography variant="caption" color="text.secondary">
                {formatFileSize(file.size)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatDate(file.lastModified)}
              </Typography>
            </Stack>

            {/* Action Buttons */}
            <Box mt={2} display="flex" gap={1}>
              <IconButton
                size="small"
                onClick={handleOpenDialog}
                title="View Full Size"
              >
                <ZoomIn />
              </IconButton>
              <IconButton
                size="small"
                onClick={handleDownload}
                title="Download File"
              >
                <Download />
              </IconButton>
            </Box>
          </CardContent>
        )}
      </Card>

      {/* Full Size Preview Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          <Typography variant="h6" noWrap>
            {file.name}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {formatFileSize(file.size)} • {file.type}
          </Typography>
        </DialogTitle>
        
        <DialogContent sx={{ p: 0, display: 'flex', justifyContent: 'center' }}>
          {file.previewUrl ? (
            <Box
              component="img"
              src={file.previewUrl}
              alt={file.name}
              sx={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                gap: 2
              }}
            >
              {getFileIcon()}
              <Typography variant="h6" color="text.secondary">
                Preview not available
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {file.type === 'application/pdf' 
                  ? 'PDF files cannot be previewed in browser'
                  : 'This file type does not support preview'
                }
              </Typography>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleDownload} startIcon={<Download />}>
            Download
          </Button>
          <Button onClick={handleCloseDialog}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};