/**
 * Fabrication Progress Dashboard Component
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Build as BuildIcon,
  Assignment as AssignmentIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import fabricationService, {
  DashboardOverview,
  ProjectSummary,
  ProgressReports,
} from '../services/fabricationService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`fabrication-tabpanel-${index}`}
      aria-labelledby={`fabrication-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const FabricationDashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [dashboardData, setDashboardData] = useState<DashboardOverview | null>(null);
  const [reportsData, setReportsData] = useState<ProgressReports | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [dashboard, reports] = await Promise.all([
        fabricationService.getDashboardOverview(),
        fabricationService.getProgressReports(),
      ]);
      
      setDashboardData(dashboard);
      setReportsData(reports);
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getCompletionColor = (percentage: number): 'error' | 'warning' | 'info' | 'success' => {
    if (percentage < 25) return 'error';
    if (percentage < 50) return 'warning';
    if (percentage < 75) return 'info';
    return 'success';
  };

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
        <Button onClick={loadDashboardData} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
        <BuildIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        Fabrication Dashboard
      </Typography>

      {/* Overall Statistics Cards */}
      {dashboardData && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Projects
                </Typography>
                <Typography variant="h4">
                  {dashboardData.overall_stats.total_projects}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Parts
                </Typography>
                <Typography variant="h4">
                  {dashboardData.overall_stats.total_parts}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Completed Parts
                </Typography>
                <Typography variant="h4">
                  {dashboardData.overall_stats.completed_parts}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Overall Progress
                </Typography>
                <Typography variant="h4">
                  {dashboardData.overall_stats.overall_completion}%
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={dashboardData.overall_stats.overall_completion}
                  color={getCompletionColor(dashboardData.overall_stats.overall_completion)}
                  sx={{ mt: 1 }}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="fabrication dashboard tabs">
          <Tab
            label="Projects"
            icon={<AssignmentIcon />}
            iconPosition="start"
            id="fabrication-tab-0"
            aria-controls="fabrication-tabpanel-0"
          />
          <Tab
            label="Reports"
            icon={<AssessmentIcon />}
            iconPosition="start"
            id="fabrication-tab-1"
            aria-controls="fabrication-tabpanel-1"
          />
        </Tabs>
      </Box>

      {/* Projects Tab */}
      <TabPanel value={tabValue} index={0}>
        {dashboardData && (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Project Name</TableCell>
                  <TableCell align="center">Total Parts</TableCell>
                  <TableCell align="center">Completed</TableCell>
                  <TableCell align="center">Progress</TableCell>
                  <TableCell align="center">Created</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {dashboardData.projects.map((project: ProjectSummary) => (
                  <TableRow key={project.design_id}>
                    <TableCell>
                      <Typography variant="subtitle1" fontWeight="medium">
                        {project.design_name}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">{project.total_parts}</TableCell>
                    <TableCell align="center">{project.completed_parts}</TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={project.completion_percentage}
                          color={getCompletionColor(project.completion_percentage)}
                          sx={{ width: 100 }}
                        />
                        <Typography variant="body2">
                          {project.completion_percentage}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      {formatDate(project.created_at)}
                    </TableCell>
                    <TableCell align="center">
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<TimelineIcon />}
                        href={`/fabrication/${project.design_id}`}
                      >
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {dashboardData.projects.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography color="textSecondary">
                        No projects with fabrication progress found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </TabPanel>

      {/* Reports Tab */}
      <TabPanel value={tabValue} index={1}>
        {reportsData && (
          <Grid container spacing={3}>
            {/* Stage Distribution */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Stage Distribution
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    {Object.entries(reportsData.stage_distribution).map(([stage, count]) => (
                      <Box key={stage} sx={{ mb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2">{stage}</Typography>
                          <Typography variant="body2">{count} parts</Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={(count / reportsData.total_parts) * 100}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Summary Stats */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Summary Statistics
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography>Total Parts:</Typography>
                      <Chip label={reportsData.total_parts} color="primary" />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography>Completed Parts:</Typography>
                      <Chip label={reportsData.completed_parts} color="success" />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography>Overdue Parts:</Typography>
                      <Chip 
                        label={reportsData.overdue_count} 
                        color={reportsData.overdue_count > 0 ? "error" : "default"}
                        icon={reportsData.overdue_count > 0 ? <WarningIcon /> : undefined}
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Overdue Parts */}
            {reportsData.overdue_parts.length > 0 && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="error">
                      <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Overdue Parts
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Part Number</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell>Due Date</TableCell>
                            <TableCell>Days Overdue</TableCell>
                            <TableCell>Assigned To</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {reportsData.overdue_parts.map((part, index) => (
                            <TableRow key={index}>
                              <TableCell>{part.part_number || 'N/A'}</TableCell>
                              <TableCell>{part.description || 'N/A'}</TableCell>
                              <TableCell>{formatDate(part.estimated_completion)}</TableCell>
                              <TableCell>
                                <Chip 
                                  label={`${part.days_overdue} days`} 
                                  color="error" 
                                  size="small" 
                                />
                              </TableCell>
                              <TableCell>{part.assigned_to || 'Unassigned'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        )}
      </TabPanel>
    </Box>
  );
};

export default FabricationDashboard;