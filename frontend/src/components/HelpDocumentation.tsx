import React from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tab,
  Tabs,
  Paper
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Help as HelpIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  Build as BuildIcon,
  Upload as UploadIcon,
  Visibility as VisibilityIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';

interface TroubleshootingItem {
  problem: string;
  symptoms: string[];
  solutions: string[];
  severity: 'low' | 'medium' | 'high';
  category: string;
}

interface HelpSection {
  title: string;
  icon: React.ReactElement;
  content: React.ReactNode;
}

interface HelpDocumentationProps {
  open: boolean;
  onClose: () => void;
  initialTab?: number;
}

const HelpDocumentation: React.FC<HelpDocumentationProps> = ({
  open,
  onClose,
  initialTab = 0
}) => {
  const [activeTab, setActiveTab] = React.useState(initialTab);

  const troubleshootingItems: TroubleshootingItem[] = [
    {
      problem: "Poor Image Quality Error",
      symptoms: [
        "Error message: 'The drawing quality is too poor for accurate analysis'",
        "Low confidence scores (below 50%)",
        "Missing or unclear text recognition"
      ],
      solutions: [
        "Upload a higher resolution image (minimum 300 DPI recommended)",
        "Ensure clear contrast between lines and background",
        "Remove shadows and distortions from the scan",
        "Try scanning in grayscale mode for better contrast",
        "Clean the drawing surface before scanning"
      ],
      severity: "medium",
      category: "Image Processing"
    },
    {
      problem: "OCR Text Recognition Failed",
      symptoms: [
        "No dimensions or text detected",
        "Incorrect text recognition",
        "Missing part numbers or material specifications"
      ],
      solutions: [
        "Ensure text is clearly readable and not blurred",
        "Check that text size is adequate (minimum 8pt font)",
        "Verify drawing orientation is correct",
        "Use manual input to add missing text information",
        "Try uploading in a different file format (PDF often works better)"
      ],
      severity: "medium",
      category: "Text Recognition"
    },
    {
      problem: "No Geometric Features Detected",
      symptoms: [
        "Error: 'Unable to detect geometric features'",
        "Empty or incomplete analysis results",
        "No shapes or lines identified"
      ],
      solutions: [
        "Ensure drawing lines are continuous and well-defined",
        "Check that the drawing follows standard engineering conventions",
        "Remove any background patterns or watermarks",
        "Try uploading a cleaner version of the drawing",
        "Verify the drawing contains actual technical content"
      ],
      severity: "high",
      category: "Feature Detection"
    },
    {
      problem: "BOM Extraction Failed",
      symptoms: [
        "No parts list generated",
        "Incomplete Bill of Materials",
        "Missing material or quantity information"
      ],
      solutions: [
        "Verify the drawing contains a parts list or BOM table",
        "Ensure part numbers are clearly visible and readable",
        "Check that material specifications are included in the drawing",
        "Use manual input to complete missing BOM information",
        "Make sure the parts list follows standard formatting"
      ],
      severity: "medium",
      category: "BOM Generation"
    },
    {
      problem: "3D Model Generation Failed",
      symptoms: [
        "No 3D model created",
        "Error in 3D generation process",
        "Incomplete or distorted 3D model"
      ],
      solutions: [
        "Ensure the drawing contains sufficient dimensional information",
        "Check that all views are properly aligned and scaled",
        "Verify that geometric features are clearly defined",
        "Try simplifying complex features for better 3D inference",
        "Make sure orthographic views are consistent"
      ],
      severity: "low",
      category: "3D Generation"
    },
    {
      problem: "File Upload Issues",
      symptoms: [
        "File upload fails or times out",
        "Unsupported file format error",
        "File too large error"
      ],
      solutions: [
        "Check file format - supported: PDF, DXF, PNG, JPG, TIFF",
        "Reduce file size to under 50MB",
        "Try compressing the image or reducing resolution",
        "Ensure stable internet connection",
        "Clear browser cache and try again"
      ],
      severity: "high",
      category: "File Handling"
    },
    {
      problem: "Analysis Takes Too Long",
      symptoms: [
        "Processing timeout errors",
        "Analysis stuck in progress",
        "System appears unresponsive"
      ],
      solutions: [
        "Try uploading a smaller or simpler drawing",
        "Reduce image resolution if very high",
        "Split complex drawings into separate files",
        "Wait for current analysis to complete before starting new one",
        "Contact support if the issue persists"
      ],
      severity: "medium",
      category: "Performance"
    }
  ];

  const bestPractices = [
    {
      title: "Drawing Preparation",
      tips: [
        "Scan at 300 DPI or higher for best results",
        "Use high contrast settings (black lines on white background)",
        "Ensure drawings are flat and properly aligned",
        "Remove any coffee stains, marks, or annotations",
        "Make sure text is legible and not hand-written"
      ]
    },
    {
      title: "File Format Selection",
      tips: [
        "PDF files often provide the best results",
        "DXF files preserve vector information",
        "PNG format is good for scanned images",
        "Avoid JPEG for technical drawings (compression artifacts)",
        "Keep file sizes reasonable (under 50MB)"
      ]
    },
    {
      title: "Drawing Standards",
      tips: [
        "Follow standard engineering drawing conventions",
        "Include clear title blocks with part information",
        "Provide complete dimensional information",
        "Use standard symbols and notation",
        "Include material specifications in parts list"
      ]
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'info';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <ErrorIcon color="error" />;
      case 'medium': return <WarningIcon color="warning" />;
      case 'low': return <InfoIcon color="info" />;
      default: return <InfoIcon color="info" />;
    }
  };

  const helpSections: HelpSection[] = [
    {
      title: "Troubleshooting",
      icon: <BuildIcon />,
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Common issues and their solutions. Click on any problem below to see detailed troubleshooting steps.
          </Typography>
          
          {troubleshootingItems.map((item, index) => (
            <Accordion key={index} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={2}>
                  {getSeverityIcon(item.severity)}
                  <Box>
                    <Typography variant="subtitle1">{item.problem}</Typography>
                    <Box display="flex" gap={1} mt={0.5}>
                      <Chip 
                        label={item.category} 
                        size="small" 
                        variant="outlined"
                      />
                      <Chip 
                        label={item.severity.toUpperCase()} 
                        size="small" 
                        color={getSeverityColor(item.severity) as any}
                      />
                    </Box>
                  </Box>
                </Box>
              </AccordionSummary>
              
              <AccordionDetails>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Symptoms:
                  </Typography>
                  <List dense>
                    {item.symptoms.map((symptom, idx) => (
                      <ListItem key={idx} sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <WarningIcon fontSize="small" color="warning" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={symptom}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                  
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    Solutions:
                  </Typography>
                  <List dense>
                    {item.solutions.map((solution, idx) => (
                      <ListItem key={idx} sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <CheckCircleIcon fontSize="small" color="success" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={solution}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )
    },
    {
      title: "Best Practices",
      icon: <CheckCircleIcon />,
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Follow these best practices to get the most accurate results from your drawing analysis.
          </Typography>
          
          {bestPractices.map((section, index) => (
            <Card key={index} sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {section.title}
                </Typography>
                <List>
                  {section.tips.map((tip, idx) => (
                    <ListItem key={idx}>
                      <ListItemIcon>
                        <CheckCircleIcon color="success" />
                      </ListItemIcon>
                      <ListItemText primary={tip} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          ))}
        </Box>
      )
    },
    {
      title: "Understanding Results",
      icon: <AssessmentIcon />,
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Learn how to interpret analysis results and confidence scores.
          </Typography>
          
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Confidence Scores
              </Typography>
              <Typography variant="body2" paragraph>
                Confidence scores indicate how reliable the analysis results are:
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="90-100%: Very High Confidence"
                    secondary="Results are highly reliable and accurate"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="75-89%: High Confidence"
                    secondary="Results are generally reliable with minor uncertainties"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <WarningIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="50-74%: Medium Confidence"
                    secondary="Results may need manual review and correction"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <ErrorIcon color="error" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Below 50%: Low Confidence"
                    secondary="Results require significant manual review and input"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
          
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Analysis Components
              </Typography>
              <Typography variant="body2" paragraph>
                Understanding what each analysis component does:
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <VisibilityIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Image Quality"
                    secondary="Assesses resolution, contrast, and clarity of the uploaded drawing"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <BuildIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Feature Detection"
                    secondary="Identifies geometric shapes, lines, and drawing elements"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Text Recognition (OCR)"
                    secondary="Extracts text, dimensions, and annotations from the drawing"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <AssessmentIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="BOM Extraction"
                    secondary="Generates Bill of Materials from parts list and drawing information"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Box>
      )
    },
    {
      title: "Getting Started",
      icon: <UploadIcon />,
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Quick start guide for using the 3D BOM Generator.
          </Typography>
          
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Step-by-Step Process
            </Typography>
            <Typography variant="body2">
              Follow these steps to analyze your engineering drawings and generate 3D models with BOMs.
            </Typography>
          </Alert>
          
          <List>
            <ListItem>
              <ListItemIcon>
                <Box 
                  sx={{ 
                    width: 24, 
                    height: 24, 
                    borderRadius: '50%', 
                    bgcolor: 'primary.main', 
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.875rem',
                    fontWeight: 'bold'
                  }}
                >
                  1
                </Box>
              </ListItemIcon>
              <ListItemText 
                primary="Upload Your Drawing"
                secondary="Select and upload your engineering drawing (PDF, DXF, or image file)"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <Box 
                  sx={{ 
                    width: 24, 
                    height: 24, 
                    borderRadius: '50%', 
                    bgcolor: 'primary.main', 
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.875rem',
                    fontWeight: 'bold'
                  }}
                >
                  2
                </Box>
              </ListItemIcon>
              <ListItemText 
                primary="Wait for Analysis"
                secondary="The system will automatically analyze your drawing and extract information"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <Box 
                  sx={{ 
                    width: 24, 
                    height: 24, 
                    borderRadius: '50%', 
                    bgcolor: 'primary.main', 
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.875rem',
                    fontWeight: 'bold'
                  }}
                >
                  3
                </Box>
              </ListItemIcon>
              <ListItemText 
                primary="Review Results"
                secondary="Check the generated BOM, weight calculations, and 3D model"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <Box 
                  sx={{ 
                    width: 24, 
                    height: 24, 
                    borderRadius: '50%', 
                    bgcolor: 'primary.main', 
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.875rem',
                    fontWeight: 'bold'
                  }}
                >
                  4
                </Box>
              </ListItemIcon>
              <ListItemText 
                primary="Make Corrections"
                secondary="Use manual correction tools to fix any inaccuracies or add missing information"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <Box 
                  sx={{ 
                    width: 24, 
                    height: 24, 
                    borderRadius: '50%', 
                    bgcolor: 'primary.main', 
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.875rem',
                    fontWeight: 'bold'
                  }}
                >
                  5
                </Box>
              </ListItemIcon>
              <ListItemText 
                primary="Export Results"
                secondary="Download your BOM as CSV and 3D model as STL or STEP file"
              />
            </ListItem>
          </List>
          
          <Alert severity="success" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Pro Tip:</strong> For best results, ensure your drawings are clear, 
              high-resolution, and follow standard engineering conventions.
            </Typography>
          </Alert>
        </Box>
      )
    }
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <HelpIcon color="primary" />
          <Typography variant="h6">Help & Documentation</Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Paper sx={{ width: '100%' }}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            {helpSections.map((section, index) => (
              <Tab
                key={index}
                icon={section.icon}
                label={section.title}
              />
            ))}
          </Tabs>
          
          <Box sx={{ p: 3, maxHeight: '60vh', overflow: 'auto' }}>
            {helpSections[activeTab]?.content}
          </Box>
        </Paper>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default HelpDocumentation;