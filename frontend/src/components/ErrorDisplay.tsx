import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>,
  <PERSON>lapse,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  LinearProgress
} from '@mui/material';
import {
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon,
  Build as BuildIcon,
  Help as HelpIcon
} from '@mui/icons-material';

export interface ErrorDetails {
  code: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  suggestions: string[];
  retry_possible: boolean;
  timestamp: string;
  confidence_score?: number;
  failed_sections?: string[];
  technical_details?: Record<string, any>;
}

export interface RetryOptions {
  can_retry: boolean;
  retry_suggestions: string[];
  estimated_wait_time: number;
  retry_with_changes: boolean;
  manual_alternatives: string[];
}

interface ErrorDisplayProps {
  error: <PERSON>rrorDetails;
  retryOptions?: RetryOptions;
  onRetry?: () => void;
  onManualCorrection?: () => void;
  onDismiss?: () => void;
  showTechnicalDetails?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  retryOptions,
  onRetry,
  onManualCorrection,
  onDismiss,
  showTechnicalDetails = false
}) => {
  const [expanded, setExpanded] = React.useState(false);
  const [showTechnical, setShowTechnical] = React.useState(false);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'info';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return <ErrorIcon />;
      case 'medium':
        return <WarningIcon />;
      case 'low':
      default:
        return <InfoIcon />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Card sx={{ mb: 2, border: `2px solid ${getSeverityColor(error.severity)}` }}>
      <CardContent>
        {/* Main Error Display */}
        <Box display="flex" alignItems="flex-start" gap={2}>
          <Box color={`${getSeverityColor(error.severity)}.main`}>
            {getSeverityIcon(error.severity)}
          </Box>
          
          <Box flex={1}>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <Typography variant="h6" color={`${getSeverityColor(error.severity)}.main`}>
                {error.message}
              </Typography>
              <Chip 
                label={error.severity.toUpperCase()} 
                color={getSeverityColor(error.severity) as any}
                size="small"
              />
              <Chip 
                label={error.category.replace('_', ' ').toUpperCase()} 
                variant="outlined"
                size="small"
              />
            </Box>

            {/* Confidence Score */}
            {error.confidence_score !== undefined && (
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Confidence Score: {Math.round(error.confidence_score * 100)}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={error.confidence_score * 100}
                  color={error.confidence_score > 0.7 ? 'success' : error.confidence_score > 0.4 ? 'warning' : 'error'}
                />
              </Box>
            )}

            {/* Failed Sections */}
            {error.failed_sections && error.failed_sections.length > 0 && (
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Affected Sections:
                </Typography>
                <Box display="flex" gap={1} flexWrap="wrap">
                  {error.failed_sections.map((section, index) => (
                    <Chip 
                      key={index}
                      label={section.replace('_', ' ')}
                      size="small"
                      variant="outlined"
                      color="error"
                    />
                  ))}
                </Box>
              </Box>
            )}

            {/* Action Buttons */}
            <Box display="flex" gap={1} mb={2}>
              {retryOptions?.can_retry && onRetry && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<RefreshIcon />}
                  onClick={onRetry}
                  size="small"
                >
                  Retry
                  {retryOptions.estimated_wait_time > 0 && (
                    <Typography variant="caption" sx={{ ml: 1 }}>
                      (~{retryOptions.estimated_wait_time}s)
                    </Typography>
                  )}
                </Button>
              )}
              
              {onManualCorrection && (
                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<BuildIcon />}
                  onClick={onManualCorrection}
                  size="small"
                >
                  Manual Correction
                </Button>
              )}
              
              <IconButton
                onClick={() => setExpanded(!expanded)}
                size="small"
                aria-label="expand"
              >
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>

            {/* Expandable Details */}
            <Collapse in={expanded}>
              <Box>
                {/* Suggestions */}
                {error.suggestions && error.suggestions.length > 0 && (
                  <Box mb={2}>
                    <Typography variant="subtitle2" gutterBottom>
                      Suggestions:
                    </Typography>
                    <List dense>
                      {error.suggestions.map((suggestion, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <HelpIcon fontSize="small" color="primary" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={suggestion}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}

                {/* Retry Suggestions */}
                {retryOptions?.retry_suggestions && retryOptions.retry_suggestions.length > 0 && (
                  <Box mb={2}>
                    <Typography variant="subtitle2" gutterBottom>
                      Before Retrying:
                    </Typography>
                    <List dense>
                      {retryOptions.retry_suggestions.map((suggestion, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <RefreshIcon fontSize="small" color="secondary" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={suggestion}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}

                {/* Manual Alternatives */}
                {retryOptions?.manual_alternatives && retryOptions.manual_alternatives.length > 0 && (
                  <Box mb={2}>
                    <Typography variant="subtitle2" gutterBottom>
                      Alternative Actions:
                    </Typography>
                    <List dense>
                      {retryOptions.manual_alternatives.map((alternative, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <BuildIcon fontSize="small" color="info" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={alternative}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}

                {/* Technical Details */}
                {showTechnicalDetails && error.technical_details && (
                  <Box mb={2}>
                    <Button
                      variant="text"
                      size="small"
                      onClick={() => setShowTechnical(!showTechnical)}
                      startIcon={showTechnical ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    >
                      Technical Details
                    </Button>
                    <Collapse in={showTechnical}>
                      <Box 
                        sx={{ 
                          mt: 1, 
                          p: 2, 
                          bgcolor: 'grey.100', 
                          borderRadius: 1,
                          fontFamily: 'monospace',
                          fontSize: '0.875rem'
                        }}
                      >
                        <pre>{JSON.stringify(error.technical_details, null, 2)}</pre>
                      </Box>
                    </Collapse>
                  </Box>
                )}

                {/* Error Metadata */}
                <Box display="flex" gap={2} flexWrap="wrap">
                  <Typography variant="caption" color="text.secondary">
                    Error Code: {error.code}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Time: {formatTimestamp(error.timestamp)}
                  </Typography>
                </Box>
              </Box>
            </Collapse>
          </Box>

          {/* Dismiss Button */}
          {onDismiss && (
            <IconButton onClick={onDismiss} size="small">
              <ExpandLessIcon />
            </IconButton>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default ErrorDisplay;