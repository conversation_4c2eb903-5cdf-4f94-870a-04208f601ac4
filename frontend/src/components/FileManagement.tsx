import React, { useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Divider,
  Stack
} from '@mui/material';
import {
  Edit,
  Delete,
  FileCopy,
  FolderOpen,
  Clear
} from '@mui/icons-material';
import { UploadedFile } from '../types/file';

interface FileManagementProps {
  files: UploadedFile[];
  onRename: (fileId: string, newName: string) => void;
  onDuplicate: (fileId: string) => void;
  onRemove: (fileId: string) => void;
}

export const FileManagement: React.FC<FileManagementProps> = ({
  files,
  onRename,
  onDuplicate,
  onRemove
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null);
  const [newFileName, setNewFileName] = useState('');
  const [renameError, setRenameError] = useState('');

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedFile(null);
  };

  const handleRenameClick = (file: UploadedFile) => {
    setSelectedFile(file);
    setNewFileName(file.name);
    setRenameError('');
    setRenameDialogOpen(true);
  };

  const handleRenameConfirm = () => {
    if (!selectedFile) return;

    // Validate new name
    if (!newFileName.trim()) {
      setRenameError('File name cannot be empty');
      return;
    }

    if (newFileName === selectedFile.name) {
      setRenameError('New name must be different from current name');
      return;
    }

    // Check if name already exists
    const existingFile = files.find(f => f.id !== selectedFile.id && f.name === newFileName.trim());
    if (existingFile) {
      setRenameError('A file with this name already exists');
      return;
    }

    onRename(selectedFile.id, newFileName.trim());
    setRenameDialogOpen(false);
    setSelectedFile(null);
    setNewFileName('');
    setRenameError('');
  };

  const handleRenameCancel = () => {
    setRenameDialogOpen(false);
    setSelectedFile(null);
    setNewFileName('');
    setRenameError('');
  };

  const handleDeleteClick = (file: UploadedFile) => {
    setSelectedFile(file);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (!selectedFile) return;
    onRemove(selectedFile.id);
    setDeleteDialogOpen(false);
    setSelectedFile(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSelectedFile(null);
  };

  const handleDuplicate = (file: UploadedFile) => {
    onDuplicate(file.id);
  };

  const clearAllFiles = () => {
    files.forEach(file => onRemove(file.id));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'error': return 'error';
      case 'processing': return 'warning';
      case 'uploading': return 'info';
      default: return 'default';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (files.length === 0) {
    return null;
  }

  return (
    <>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Button
          variant="outlined"
          startIcon={<FolderOpen />}
          onClick={handleOpenDialog}
        >
          Manage Files ({files.length})
        </Button>
        
        {files.length > 0 && (
          <Button
            variant="outlined"
            color="error"
            startIcon={<Clear />}
            onClick={clearAllFiles}
            size="small"
          >
            Clear All
          </Button>
        )}
      </Box>

      {/* File Management Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { height: '70vh' }
        }}
      >
        <DialogTitle>
          <Typography variant="h6">
            File Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage your uploaded files - rename, duplicate, or remove files
          </Typography>
        </DialogTitle>
        
        <DialogContent dividers>
          {files.length === 0 ? (
            <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
              No files uploaded yet
            </Typography>
          ) : (
            <List>
              {files.map((file, index) => (
                <React.Fragment key={file.id}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="subtitle2" noWrap>
                            {file.name}
                          </Typography>
                          <Chip
                            label={file.status.toUpperCase()}
                            size="small"
                            color={getStatusColor(file.status) as any}
                          />
                        </Box>
                      }
                      secondary={
                        <Stack direction="row" spacing={2} mt={0.5}>
                          <Typography variant="caption" color="text.secondary">
                            Size: {formatFileSize(file.size)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Type: {file.type || 'Unknown'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Modified: {new Date(file.lastModified).toLocaleDateString()}
                          </Typography>
                        </Stack>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <Stack direction="row" spacing={0.5}>
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={() => handleRenameClick(file)}
                          title="Rename file"
                          disabled={file.status === 'uploading'}
                        >
                          <Edit />
                        </IconButton>
                        
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={() => handleDuplicate(file)}
                          title="Duplicate file"
                          disabled={file.status !== 'completed'}
                        >
                          <FileCopy />
                        </IconButton>
                        
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={() => handleDeleteClick(file)}
                          title="Remove file"
                          color="error"
                        >
                          <Delete />
                        </IconButton>
                      </Stack>
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  {index < files.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Rename Dialog */}
      <Dialog
        open={renameDialogOpen}
        onClose={handleRenameCancel}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Rename File
        </DialogTitle>
        
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Enter a new name for "{selectedFile?.name}"
          </Typography>
          
          <TextField
            autoFocus
            fullWidth
            label="File Name"
            value={newFileName}
            onChange={(e) => {
              setNewFileName(e.target.value);
              setRenameError('');
            }}
            error={!!renameError}
            helperText={renameError}
            margin="normal"
          />
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleRenameCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleRenameConfirm}
            variant="contained"
            disabled={!newFileName.trim() || !!renameError}
          >
            Rename
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        maxWidth="sm"
      >
        <DialogTitle>
          Confirm Delete
        </DialogTitle>
        
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This action cannot be undone.
          </Alert>
          
          <Typography variant="body1">
            Are you sure you want to delete "{selectedFile?.name}"?
          </Typography>
          
          {selectedFile?.status === 'completed' && (
            <Typography variant="body2" color="text.secondary" mt={1}>
              This will also remove any analysis results and generated models.
            </Typography>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleDeleteCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm}
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};