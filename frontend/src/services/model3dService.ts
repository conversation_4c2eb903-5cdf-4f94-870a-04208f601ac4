import axios from 'axios';
import { Model3D, ExportOptions } from '../types/model3d';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export class Model3DService {
  private static instance: Model3DService;
  private baseURL: string;

  private constructor() {
    this.baseURL = `${API_BASE_URL}/api/models`;
  }

  public static getInstance(): Model3DService {
    if (!Model3DService.instance) {
      Model3DService.instance = new Model3DService();
    }
    return Model3DService.instance;
  }

  /**
   * Get 3D model by ID
   */
  async getModel(modelId: string): Promise<Model3D> {
    try {
      const response = await axios.get(`${this.baseURL}/${modelId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching 3D model:', error);
      throw new Error('Failed to fetch 3D model');
    }
  }

  /**
   * Get 3D model by design ID
   */
  async getModelByDesignId(designId: string): Promise<Model3D | null> {
    try {
      const response = await axios.get(`${this.baseURL}/design/${designId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return null; // No model found for this design
      }
      console.error('Error fetching 3D model by design ID:', error);
      throw new Error('Failed to fetch 3D model');
    }
  }

  /**
   * Generate 3D model from design
   */
  async generateModel(designId: string): Promise<{ taskId: string }> {
    try {
      const response = await axios.post(`${this.baseURL}/generate`, {
        design_id: designId
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error generating 3D model:', error);
      throw new Error('Failed to generate 3D model');
    }
  }

  /**
   * Get generation status
   */
  async getGenerationStatus(taskId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress?: number;
    error?: string;
    model?: Model3D;
  }> {
    try {
      const response = await axios.get(`${this.baseURL}/status/${taskId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching generation status:', error);
      throw new Error('Failed to fetch generation status');
    }
  }

  /**
   * Export 3D model
   */
  async exportModel(modelId: string, format: 'stl' | 'step'): Promise<Blob> {
    try {
      const response = await axios.get(`${this.baseURL}/${modelId}/export/${format}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting 3D model:', error);
      throw new Error(`Failed to export ${format.toUpperCase()} model`);
    }
  }

  /**
   * Get STL file URL for viewing
   */
  getModelFileUrl(modelFilePath: string): string {
    const token = localStorage.getItem('token');
    return `${API_BASE_URL}/api/files/download?path=${encodeURIComponent(modelFilePath)}&token=${token}`;
  }

  /**
   * Delete 3D model
   */
  async deleteModel(modelId: string): Promise<void> {
    try {
      await axios.delete(`${this.baseURL}/${modelId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
    } catch (error) {
      console.error('Error deleting 3D model:', error);
      throw new Error('Failed to delete 3D model');
    }
  }

  /**
   * Update model metadata
   */
  async updateModel(modelId: string, updates: Partial<Model3D>): Promise<Model3D> {
    try {
      const response = await axios.put(`${this.baseURL}/${modelId}`, updates, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating 3D model:', error);
      throw new Error('Failed to update 3D model');
    }
  }

  /**
   * Get model statistics
   */
  async getModelStats(modelId: string): Promise<{
    vertices: number;
    faces: number;
    volume: number;
    surfaceArea: number;
  }> {
    try {
      const response = await axios.get(`${this.baseURL}/${modelId}/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching model statistics:', error);
      throw new Error('Failed to fetch model statistics');
    }
  }
}

export const model3dService = Model3DService.getInstance();