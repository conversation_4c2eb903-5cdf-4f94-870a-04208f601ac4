/**
 * Admin service for system management operations
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export interface SystemStats {
  total_users: number;
  active_users: number;
  admin_users: number;
  total_designs: number;
  designs_this_month: number;
  total_analyses: number;
  successful_analyses: number;
  failed_analyses: number;
  total_storage_mb: number;
  avg_processing_time_seconds: number;
}

export interface UserActivityLog {
  id: number;
  user_id: number;
  username: string;
  action: string;
  resource_type?: string;
  resource_id?: number;
  ip_address?: string;
  user_agent?: string;
  timestamp: string;
}

export interface SystemHealthMetric {
  metric_name: string;
  value: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  last_updated: string;
}

export interface ErrorLogEntry {
  id: number;
  error_type: string;
  error_message: string;
  stack_trace?: string;
  user_id?: number;
  username?: string;
  endpoint?: string;
  request_data?: string;
  timestamp: string;
  resolved: boolean;
}

export interface Material {
  id: number;
  name: string;
  density: number;
  category: string;
  description?: string;
  created_at: string;
}

export interface MaterialCreate {
  name: string;
  density: number;
  category: string;
  description?: string;
}

export interface MaterialUpdate {
  name?: string;
  density?: number;
  category?: string;
  description?: string;
}

class AdminService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('access_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // System Statistics
  async getSystemStats(): Promise<SystemStats> {
    const response = await fetch(`${API_BASE_URL}/api/admin/dashboard/stats`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<SystemStats>(response);
  }

  // User Activity Logs
  async getUserActivityLogs(params: {
    skip?: number;
    limit?: number;
    user_id?: number;
    action?: string;
    days?: number;
  } = {}): Promise<UserActivityLog[]> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await fetch(
      `${API_BASE_URL}/api/admin/users/activity?${searchParams}`,
      {
        headers: this.getAuthHeaders(),
      }
    );
    return this.handleResponse<UserActivityLog[]>(response);
  }

  // System Health
  async getSystemHealth(): Promise<SystemHealthMetric[]> {
    const response = await fetch(`${API_BASE_URL}/api/admin/system/health`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<SystemHealthMetric[]>(response);
  }

  // Error Logs
  async getErrorLogs(params: {
    skip?: number;
    limit?: number;
    error_type?: string;
    resolved?: boolean;
    days?: number;
  } = {}): Promise<ErrorLogEntry[]> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await fetch(
      `${API_BASE_URL}/api/admin/system/errors?${searchParams}`,
      {
        headers: this.getAuthHeaders(),
      }
    );
    return this.handleResponse<ErrorLogEntry[]>(response);
  }

  async resolveErrorLog(errorId: number): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/api/admin/system/errors/${errorId}/resolve`,
      {
        method: 'PUT',
        headers: this.getAuthHeaders(),
      }
    );
    await this.handleResponse<void>(response);
  }

  // Materials Management
  async getMaterials(params: {
    skip?: number;
    limit?: number;
    category?: string;
    search?: string;
  } = {}): Promise<Material[]> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await fetch(
      `${API_BASE_URL}/api/admin/materials?${searchParams}`,
      {
        headers: this.getAuthHeaders(),
      }
    );
    return this.handleResponse<Material[]>(response);
  }

  async createMaterial(materialData: MaterialCreate): Promise<Material> {
    const response = await fetch(`${API_BASE_URL}/api/admin/materials`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(materialData),
    });
    return this.handleResponse<Material>(response);
  }

  async updateMaterial(materialId: number, materialData: MaterialUpdate): Promise<Material> {
    const response = await fetch(`${API_BASE_URL}/api/admin/materials/${materialId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(materialData),
    });
    return this.handleResponse<Material>(response);
  }

  async deleteMaterial(materialId: number): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/admin/materials/${materialId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    await this.handleResponse<void>(response);
  }

  // System Cleanup
  async cleanupSystemData(): Promise<{ results: Record<string, number> }> {
    const response = await fetch(`${API_BASE_URL}/api/admin/system/cleanup`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse<{ results: Record<string, number> }>(response);
  }
}

export const adminService = new AdminService();