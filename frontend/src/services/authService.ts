import axios, { AxiosResponse } from 'axios';
import {
  User,
  LoginCredentials,
  RegisterData,
  PasswordResetRequest,
  PasswordResetConfirm,
  AuthResponse,
  ApiError
} from '../types/auth';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken
          });
          
          const { accessToken } = response.data;
          localStorage.setItem('accessToken', accessToken);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

class AuthServiceClass {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/login', credentials);
      
      // Store tokens
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);
      
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/register', data);
      
      // Store tokens
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);
      
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    }
  }

  async refreshToken(): Promise<string> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response: AxiosResponse<{ accessToken: string }> = await apiClient.post('/auth/refresh', {
        refreshToken
      });

      const { accessToken } = response.data;
      localStorage.setItem('accessToken', accessToken);
      
      return accessToken;
    } catch (error: any) {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      throw this.handleError(error);
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      const response: AxiosResponse<User> = await apiClient.get('/users/profile');
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    try {
      const response: AxiosResponse<User> = await apiClient.put('/users/profile', data);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async requestPasswordReset(data: PasswordResetRequest): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset/request', data);
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async confirmPasswordReset(data: PasswordResetConfirm): Promise<void> {
    try {
      await apiClient.post('/auth/password-reset/confirm', data);
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('accessToken');
  }

  // Admin methods
  async getAllUsers(skip: number = 0, limit: number = 100): Promise<User[]> {
    try {
      const response: AxiosResponse<User[]> = await apiClient.get(`/auth/users?skip=${skip}&limit=${limit}`);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async createUserAdmin(userData: RegisterData, isAdmin: boolean = false): Promise<User> {
    try {
      const response: AxiosResponse<User> = await apiClient.post(`/auth/users?is_admin=${isAdmin}`, userData);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async updateUserStatus(userId: number, isActive: boolean): Promise<User> {
    try {
      const response: AxiosResponse<User> = await apiClient.put(`/auth/users/${userId}/status`, { is_active: isActive });
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async updateUserAdminStatus(userId: number, isAdmin: boolean): Promise<User> {
    try {
      const response: AxiosResponse<User> = await apiClient.put(`/auth/users/${userId}/admin`, { is_admin: isAdmin });
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  async deleteUser(userId: number): Promise<void> {
    try {
      await apiClient.delete(`/auth/users/${userId}`);
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  private handleError(error: any): ApiError {
    if (error.response?.data) {
      return error.response.data.error || {
        code: 'UNKNOWN_ERROR',
        message: error.response.data.message || 'An unknown error occurred'
      };
    }
    
    return {
      code: 'NETWORK_ERROR',
      message: error.message || 'Network error occurred'
    };
  }
}

export const authService = new AuthServiceClass();
export { AuthServiceClass as AuthService };