import axios from 'axios';
import {
  Design,
  DesignFilters,
  DesignStats,
  DuplicateDesignRequest,
  RenameDesignRequest,
  ShareDesignRequest,
  RecentActivity
} from '../types/design';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance with auth interceptor
const api = axios.create({
  baseURL: API_BASE_URL,
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export class DesignService {
  /**
   * Get all designs for the current user
   */
  static async getUserDesigns(filters?: Partial<DesignFilters>): Promise<Design[]> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.search) {
        params.append('search', filters.search);
      }
      
      if (filters?.status && filters.status.length > 0) {
        filters.status.forEach(status => params.append('status', status));
      }
      
      if (filters?.material && filters.material.length > 0) {
        filters.material.forEach(material => params.append('material', material));
      }
      
      if (filters?.dateRange?.start) {
        params.append('dateStart', filters.dateRange.start);
      }
      
      if (filters?.dateRange?.end) {
        params.append('dateEnd', filters.dateRange.end);
      }
      
      if (filters?.sortBy) {
        params.append('sortBy', filters.sortBy);
      }
      
      if (filters?.sortOrder) {
        params.append('sortOrder', filters.sortOrder);
      }

      const response = await api.get(`/api/designs?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user designs:', error);
      throw error;
    }
  }

  /**
   * Get designs shared with the current user
   */
  static async getSharedDesigns(): Promise<Design[]> {
    try {
      const response = await api.get('/api/sharing/shared-with-me');
      // Transform shared design data to Design format
      const sharedDesigns = response.data.shared_designs.map((shared: any) => ({
        id: shared.design_id,
        name: shared.design_name,
        status: shared.design_status,
        createdAt: shared.design_created_at,
        updatedAt: shared.design_updated_at,
        owner: {
          username: shared.owner_username,
          email: shared.owner_email
        },
        permissionLevel: shared.permission_level,
        sharedAt: shared.shared_at
      }));
      return sharedDesigns;
    } catch (error) {
      console.error('Error fetching shared designs:', error);
      throw error;
    }
  }

  /**
   * Get recent designs (last 10)
   */
  static async getRecentDesigns(): Promise<Design[]> {
    try {
      const response = await api.get('/api/designs/recent');
      return response.data;
    } catch (error) {
      console.error('Error fetching recent designs:', error);
      throw error;
    }
  }

  /**
   * Get design statistics
   */
  static async getDesignStats(): Promise<DesignStats> {
    try {
      const response = await api.get('/api/designs/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching design stats:', error);
      throw error;
    }
  }

  /**
   * Get recent activity
   */
  static async getRecentActivity(): Promise<RecentActivity[]> {
    try {
      const response = await api.get('/api/designs/activity');
      return response.data;
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      throw error;
    }
  }

  /**
   * Get a specific design by ID
   */
  static async getDesign(id: string): Promise<Design> {
    try {
      const response = await api.get(`/api/designs/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching design:', error);
      throw error;
    }
  }

  /**
   * Rename a design
   */
  static async renameDesign(request: RenameDesignRequest): Promise<Design> {
    try {
      const response = await api.put(`/api/designs/${request.id}/rename`, {
        name: request.newName
      });
      return response.data;
    } catch (error) {
      console.error('Error renaming design:', error);
      throw error;
    }
  }

  /**
   * Delete a design
   */
  static async deleteDesign(id: string): Promise<void> {
    try {
      await api.delete(`/api/designs/${id}`);
    } catch (error) {
      console.error('Error deleting design:', error);
      throw error;
    }
  }

  /**
   * Duplicate a design
   */
  static async duplicateDesign(request: DuplicateDesignRequest): Promise<Design> {
    try {
      const response = await api.post(`/api/designs/${request.originalId}/duplicate`, {
        name: request.newName
      });
      return response.data;
    } catch (error) {
      console.error('Error duplicating design:', error);
      throw error;
    }
  }

  /**
   * Share a design with another user
   */
  static async shareDesign(request: ShareDesignRequest): Promise<void> {
    try {
      await api.post(`/api/designs/${request.designId}/share`, {
        userEmail: request.userEmail,
        permissionLevel: request.permissionLevel
      });
    } catch (error) {
      console.error('Error sharing design:', error);
      throw error;
    }
  }

  /**
   * Remove sharing for a design
   */
  static async unshareDesign(designId: string, userId: number): Promise<void> {
    try {
      await api.delete(`/api/designs/${designId}/share/${userId}`);
    } catch (error) {
      console.error('Error unsharing design:', error);
      throw error;
    }
  }

  /**
   * Download design files
   */
  static async downloadDesign(id: string, format: 'bom' | 'model' | 'all'): Promise<Blob> {
    try {
      const response = await api.get(`/api/designs/${id}/download/${format}`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error downloading design:', error);
      throw error;
    }
  }

  /**
   * Get available materials for filtering
   */
  static async getAvailableMaterials(): Promise<string[]> {
    try {
      const response = await api.get('/api/materials');
      return response.data.map((material: any) => material.name);
    } catch (error) {
      console.error('Error fetching materials:', error);
      return [];
    }
  }

  /**
   * Mark design as viewed (for analytics)
   */
  static async markAsViewed(id: string): Promise<void> {
    try {
      await api.post(`/api/designs/${id}/view`);
    } catch (error) {
      console.error('Error marking design as viewed:', error);
      // Don't throw error for analytics
    }
  }
}