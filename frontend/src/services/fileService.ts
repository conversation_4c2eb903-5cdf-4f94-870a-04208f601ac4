import axios, { AxiosProgressEvent } from 'axios';
import { UploadedFile, ProcessingStatus } from '../types/file';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export interface UploadResponse {
  fileId: string;
  message: string;
  analysisId?: string;
}

export interface ProcessingStatusResponse {
  status: ProcessingStatus;
  error?: string;
}

class FileService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  async uploadFile(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/files/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            ...this.getAuthHeaders()
          },
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            if (progressEvent.total && onProgress) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              onProgress(progress);
            }
          }
        }
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          error.response?.data?.message || 
          error.response?.data?.detail || 
          'Upload failed'
        );
      }
      throw new Error('Upload failed');
    }
  }

  async getProcessingStatus(analysisId: string): Promise<ProcessingStatusResponse> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/analysis/${analysisId}/status`,
        {
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          error.response?.data?.message || 
          error.response?.data?.detail || 
          'Failed to get processing status'
        );
      }
      throw new Error('Failed to get processing status');
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      await axios.delete(
        `${API_BASE_URL}/api/files/${fileId}`,
        {
          headers: this.getAuthHeaders()
        }
      );
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          error.response?.data?.message || 
          error.response?.data?.detail || 
          'Failed to delete file'
        );
      }
      throw new Error('Failed to delete file');
    }
  }

  async getFile(fileId: string): Promise<Blob> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/files/${fileId}`,
        {
          headers: this.getAuthHeaders(),
          responseType: 'blob'
        }
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          error.response?.data?.message || 
          'Failed to download file'
        );
      }
      throw new Error('Failed to download file');
    }
  }

  async getUserFiles(): Promise<UploadedFile[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/files/user`,
        {
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          error.response?.data?.message || 
          error.response?.data?.detail || 
          'Failed to get user files'
        );
      }
      throw new Error('Failed to get user files');
    }
  }

  // Simulate processing status updates for demo purposes
  simulateProcessing(
    analysisId: string,
    onStatusUpdate: (status: ProcessingStatus) => void
  ): () => void {
    const stages: ProcessingStatus[] = [
      { stage: 'upload', message: 'File uploaded successfully', progress: 20 },
      { stage: 'analysis', message: 'Analyzing drawing structure...', progress: 40 },
      { stage: 'analysis', message: 'Extracting geometric features...', progress: 60 },
      { stage: 'bom', message: 'Generating Bill of Materials...', progress: 80 },
      { stage: 'model', message: 'Creating 3D model...', progress: 90 },
      { stage: 'complete', message: 'Processing complete!', progress: 100 }
    ];

    let currentStage = 0;
    const interval = setInterval(() => {
      if (currentStage < stages.length) {
        onStatusUpdate(stages[currentStage]);
        currentStage++;
      } else {
        clearInterval(interval);
      }
    }, 2000);

    return () => clearInterval(interval);
  }
}

export const fileService = new FileService();