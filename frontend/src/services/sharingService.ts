/**
 * Design sharing and collaboration service
 */

export interface ShareRequest {
  design_id: number;
  shared_with_email: string;
  permission_level: 'view' | 'edit';
}

export interface ShareResponse {
  success: boolean;
  message: string;
  share: {
    share_id: number;
    design_id: number;
    shared_with_user_id: number;
    shared_with_username: string;
    shared_with_email: string;
    permission_level: string;
    shared_at: string;
  };
}

export interface DesignShare {
  share_id: number;
  user_id: number;
  username: string;
  email: string;
  permission_level: string;
  shared_at: string;
}

export interface SharedDesign {
  share_id: number;
  design_id: number;
  design_name: string;
  owner_username: string;
  owner_email: string;
  permission_level: string;
  shared_at: string;
  design_status: string;
  design_created_at: string;
  design_updated_at: string;
}

export interface UserSearchResult {
  user_id: number;
  username: string;
  email: string;
}

export interface SharingHistoryEntry {
  action: string;
  user_id: number;
  username: string;
  email: string;
  permission_level: string;
  timestamp: string;
}

class SharingService {
  private baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /**
   * Grant access to a design for another user
   */
  async grantAccess(request: ShareRequest): Promise<ShareResponse> {
    return this.makeRequest<ShareResponse>('/api/sharing/grant', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Revoke access to a design from a user
   */
  async revokeAccess(designId: number, sharedWithId: number): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(
      `/api/sharing/revoke/${designId}/${sharedWithId}`,
      {
        method: 'DELETE',
      }
    );
  }

  /**
   * Get all users who have access to a design
   */
  async getDesignShares(designId: number): Promise<{ design_id: number; shares: DesignShare[] }> {
    return this.makeRequest<{ design_id: number; shares: DesignShare[] }>(
      `/api/sharing/design/${designId}/shares`
    );
  }

  /**
   * Get all designs shared with the current user
   */
  async getSharedWithMe(): Promise<{ shared_designs: SharedDesign[] }> {
    return this.makeRequest<{ shared_designs: SharedDesign[] }>('/api/sharing/shared-with-me');
  }

  /**
   * Update permission level for an existing share
   */
  async updatePermission(
    designId: number,
    sharedWithId: number,
    permissionLevel: 'view' | 'edit'
  ): Promise<ShareResponse> {
    return this.makeRequest<ShareResponse>(
      `/api/sharing/design/${designId}/user/${sharedWithId}/permission`,
      {
        method: 'PUT',
        body: JSON.stringify({ permission_level: permissionLevel }),
      }
    );
  }

  /**
   * Get sharing history for a design
   */
  async getSharingHistory(designId: number): Promise<{ design_id: number; history: SharingHistoryEntry[] }> {
    return this.makeRequest<{ design_id: number; history: SharingHistoryEntry[] }>(
      `/api/sharing/design/${designId}/history`
    );
  }

  /**
   * Search for users to share with
   */
  async searchUsers(query: string): Promise<{ query: string; users: UserSearchResult[] }> {
    const encodedQuery = encodeURIComponent(query);
    return this.makeRequest<{ query: string; users: UserSearchResult[] }>(
      `/api/sharing/users/search?q=${encodedQuery}`
    );
  }

  /**
   * Check current user's access level to a design
   */
  async checkDesignAccess(designId: number): Promise<{
    design_id: number;
    access_level: string | null;
    has_access: boolean;
  }> {
    return this.makeRequest<{
      design_id: number;
      access_level: string | null;
      has_access: boolean;
    }>(`/api/sharing/design/${designId}/access`);
  }
}

export const sharingService = new SharingService();
export default sharingService;