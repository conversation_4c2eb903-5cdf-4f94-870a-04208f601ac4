/**
 * Fabrication progress tracking service
 */
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Types
export interface FabricationStage {
  id: number;
  name: string;
  description?: string;
  order_index: number;
  is_default: boolean;
  created_by?: number;
  created_at?: string;
}

export interface FabricationTeam {
  id: number;
  name: string;
  description?: string;
  lead_user_id: number;
  lead_user_name?: string;
  is_active: boolean;
  member_count: number;
  members: TeamMember[];
  created_at?: string;
}

export interface TeamMember {
  user_id: number;
  username: string;
  role?: string;
  joined_at?: string;
}

export interface PartProgress {
  id: number;
  design_id: number;
  part_id: number;
  part_number?: string;
  part_description?: string;
  current_stage?: FabricationStage;
  assigned_to?: number;
  assigned_user_name?: string;
  assigned_team_id?: number;
  assigned_team_name?: string;
  started_at?: string;
  estimated_completion?: string;
  actual_completion?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface DesignProgress {
  design_id: number;
  design_name: string;
  total_parts: number;
  completed_parts: number;
  completion_percentage: number;
  parts_progress: PartProgress[];
}

export interface TimelineItem {
  part_id: number;
  part_number?: string;
  description?: string;
  current_stage: string;
  started_at?: string;
  estimated_completion?: string;
  actual_completion?: string;
  assigned_to?: string;
  assigned_team?: string;
  stage_history: StageHistoryItem[];
}

export interface StageHistoryItem {
  stage_name: string;
  started_at: string;
  completed_at?: string;
  duration_minutes?: number;
}

export interface TimelineData {
  design_id: number;
  timeline_data: TimelineItem[];
}

export interface ProgressReports {
  stage_distribution: Record<string, number>;
  overdue_parts: OverduePart[];
  completion_by_day: Record<string, number>;
  total_parts: number;
  completed_parts: number;
  overdue_count: number;
}

export interface OverduePart {
  part_number?: string;
  description?: string;
  estimated_completion: string;
  days_overdue: number;
  assigned_to?: string;
}

export interface DashboardOverview {
  projects: ProjectSummary[];
  overall_stats: OverallStats;
}

export interface ProjectSummary {
  design_id: number;
  design_name: string;
  completion_percentage: number;
  total_parts: number;
  completed_parts: number;
  created_at?: string;
}

export interface OverallStats {
  total_projects: number;
  total_parts: number;
  completed_parts: number;
  overall_completion: number;
}

// Request types
export interface StageCreateRequest {
  name: string;
  description?: string;
  order_index: number;
}

export interface StageUpdateRequest {
  name?: string;
  description?: string;
  order_index?: number;
}

export interface TeamCreateRequest {
  name: string;
  description?: string;
  lead_user_id: number;
}

export interface TeamMemberAddRequest {
  user_id: number;
  role?: string;
}

export interface PartStatusUpdateRequest {
  stage_id: number;
  notes?: string;
  estimated_completion?: string;
}

export interface PartAssignmentRequest {
  assigned_to?: number;
  assigned_team_id?: number;
}

class FabricationService {
  private getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  // Stage Management
  async getFabricationStages(includeCustom: boolean = true): Promise<FabricationStage[]> {
    const response = await axios.get(
      `${API_BASE_URL}/api/fabrication/stages?include_custom=${includeCustom}`,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async createFabricationStage(stageData: StageCreateRequest): Promise<FabricationStage> {
    const response = await axios.post(
      `${API_BASE_URL}/api/fabrication/stages`,
      stageData,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async updateFabricationStage(stageId: number, stageData: StageUpdateRequest): Promise<FabricationStage> {
    const response = await axios.put(
      `${API_BASE_URL}/api/fabrication/stages/${stageId}`,
      stageData,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async deleteFabricationStage(stageId: number): Promise<void> {
    await axios.delete(
      `${API_BASE_URL}/api/fabrication/stages/${stageId}`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Team Management
  async getFabricationTeams(activeOnly: boolean = true): Promise<FabricationTeam[]> {
    const response = await axios.get(
      `${API_BASE_URL}/api/fabrication/teams?active_only=${activeOnly}`,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async createFabricationTeam(teamData: TeamCreateRequest): Promise<FabricationTeam> {
    const response = await axios.post(
      `${API_BASE_URL}/api/fabrication/teams`,
      teamData,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async addTeamMember(teamId: number, memberData: TeamMemberAddRequest): Promise<void> {
    await axios.post(
      `${API_BASE_URL}/api/fabrication/teams/${teamId}/members`,
      memberData,
      { headers: this.getAuthHeaders() }
    );
  }

  async removeTeamMember(teamId: number, userId: number): Promise<void> {
    await axios.delete(
      `${API_BASE_URL}/api/fabrication/teams/${teamId}/members/${userId}`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Progress Tracking
  async getDesignProgress(designId: number): Promise<DesignProgress> {
    const response = await axios.get(
      `${API_BASE_URL}/api/fabrication/${designId}/progress`,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async updatePartStatus(
    designId: number,
    partId: number,
    statusData: PartStatusUpdateRequest
  ): Promise<PartProgress> {
    const response = await axios.put(
      `${API_BASE_URL}/api/fabrication/${designId}/parts/${partId}/status`,
      statusData,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async assignPart(
    designId: number,
    partId: number,
    assignmentData: PartAssignmentRequest
  ): Promise<PartProgress> {
    const response = await axios.put(
      `${API_BASE_URL}/api/fabrication/${designId}/parts/${partId}/assignment`,
      assignmentData,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async getTimelineData(designId: number): Promise<TimelineData> {
    const response = await axios.get(
      `${API_BASE_URL}/api/fabrication/${designId}/timeline`,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }

  async getProgressReports(designId?: number): Promise<ProgressReports> {
    const url = designId 
      ? `${API_BASE_URL}/api/fabrication/${designId}/reports`
      : `${API_BASE_URL}/api/fabrication/reports`;
    
    const response = await axios.get(url, { headers: this.getAuthHeaders() });
    return response.data;
  }

  async getDashboardOverview(): Promise<DashboardOverview> {
    const response = await axios.get(
      `${API_BASE_URL}/api/fabrication/dashboard`,
      { headers: this.getAuthHeaders() }
    );
    return response.data;
  }
}

export default new FabricationService();