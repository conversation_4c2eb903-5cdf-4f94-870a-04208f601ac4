import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './styles/print.css';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, Typography, Button } from '@mui/material';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoginForm } from './components/auth/LoginForm';
import { RegisterForm } from './components/auth/RegisterForm';
import { PasswordResetForm } from './components/auth/PasswordResetForm';
import { UserProfile } from './components/auth/UserProfile';
import { Dashboard } from './components/Dashboard';
import { AdminDashboard } from './components/AdminDashboard';
import ResultsDisplay from './components/ResultsDisplay';
import { useParams, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Design } from './types/design';
import { DesignService } from './services/designService';

// Create Material-UI theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Results Display Wrapper Component
const ResultsDisplayWrapper: React.FC = () => {
  const { designId } = useParams<{ designId: string }>();
  const navigate = useNavigate();
  const [design, setDesign] = useState<Design | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDesign = async () => {
      if (!designId) {
        setError('Design ID not provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const designData = await DesignService.getDesign(designId);
        setDesign(designData);
        
        // Mark as viewed for analytics
        await DesignService.markAsViewed(designId);
      } catch (err) {
        console.error('Error loading design:', err);
        setError('Failed to load design');
      } finally {
        setLoading(false);
      }
    };

    loadDesign();
  }, [designId]);

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <Typography>Loading design...</Typography>
      </Box>
    );
  }

  if (error || !design) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        textAlign="center"
        p={3}
      >
        <Typography variant="h5" gutterBottom>
          {error || 'Design not found'}
        </Typography>
        <Button variant="contained" onClick={() => navigate('/dashboard')}>
          Back to Dashboard
        </Button>
      </Box>
    );
  }

  return (
    <ResultsDisplay
      design={design}
      onBack={() => navigate('/dashboard')}
    />
  );
};

// Account disabled page component
const AccountDisabledPage: React.FC = () => {
  const { logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      textAlign="center"
      p={3}
    >
      <Typography variant="h4" gutterBottom>
        Account Disabled
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Your account has been disabled. Please contact an administrator for assistance.
      </Typography>
      <Button variant="contained" onClick={handleLogout}>
        Sign Out
      </Button>
    </Box>
  );
};

// Main App component with routing
const AppRoutes: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        Loading...
      </Box>
    );
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route 
        path="/login" 
        element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginForm />
        } 
      />
      <Route 
        path="/register" 
        element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterForm />
        } 
      />
      <Route path="/forgot-password" element={<PasswordResetForm />} />
      <Route path="/account-disabled" element={<AccountDisabledPage />} />

      {/* Protected routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <UserProfile />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin"
        element={
          <ProtectedRoute>
            <AdminDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/design/:designId"
        element={
          <ProtectedRoute>
            <ResultsDisplayWrapper />
          </ProtectedRoute>
        }
      />

      {/* Default redirects */}
      <Route
        path="/"
        element={
          <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
        }
      />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <AppRoutes />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
