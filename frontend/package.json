{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^5.15.10", "@mui/lab": "^5.0.0-alpha.166", "@mui/material": "^5.15.10", "@mui/x-data-grid": "^6.19.4", "@mui/x-date-pickers": "^6.19.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.178.1", "axios": "^1.10.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "three": "^0.178.0", "three-stdlib": "^2.36.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx,.js,.jsx --max-warnings 0", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit"}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(axios)/)"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"jest-axe": "^10.0.0"}}