#!/usr/bin/env python3
"""
Test BOM service imports and basic functionality
"""

import sys
import os
sys.path.append('backend')

try:
    from services.bom_service import BOMService, BOMExtractor, BOMValidator, BOMExporter
    from services.bom_service import BOMValidationStatus, BOMValidationIssue, BOMValidationResult, BOMExportData
    print("✅ All BOM service imports successful")
    
    # Test instantiation
    service = BOMService()
    extractor = BOMExtractor()
    validator = BOMValidator()
    exporter = BOMExporter()
    print("✅ All BOM service classes instantiated successfully")
    
    # Test enum values
    print(f"✅ BOM validation statuses: {[status.value for status in BOMValidationStatus]}")
    
    print("\n🎉 BOM Service is ready for use!")
    
except Exception as e:
    print(f"❌ Import or instantiation failed: {e}")
    import traceback
    traceback.print_exc()