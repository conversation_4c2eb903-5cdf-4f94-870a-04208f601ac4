# Security Hardening Guide

## Overview
This document outlines security hardening measures implemented for the BOM Generator production environment.

## Network Security

### Firewall Configuration
```bash
# Allow only necessary ports
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 5432/tcp   # PostgreSQL (internal only)
ufw deny 6379/tcp   # Redis (internal only)
ufw deny 9000/tcp   # MinIO (internal only)
ufw enable
```

### SSL/TLS Configuration
- Use TLS 1.2 or higher
- Strong cipher suites only
- HSTS headers enabled
- Certificate pinning recommended

## Container Security

### Docker Security Best Practices
1. **Non-root users**: All containers run as non-root users
2. **Read-only filesystems**: Where possible
3. **Resource limits**: CPU and memory limits set
4. **Security scanning**: Regular vulnerability scans
5. **Minimal base images**: Alpine Linux used where possible

### Container Hardening
```yaml
# Example security context
security_opt:
  - no-new-privileges:true
cap_drop:
  - ALL
cap_add:
  - CHOWN
  - SETGID
  - SETUID
read_only: true
tmpfs:
  - /tmp
  - /var/tmp
```

## Database Security

### PostgreSQL Hardening
1. **Authentication**: SCRAM-SHA-256 password encryption
2. **Row-level security**: Enabled for sensitive tables
3. **SSL connections**: Required for external connections
4. **Audit logging**: All connections and queries logged
5. **Regular backups**: Encrypted and stored securely

### Database Access Control
- Principle of least privilege
- Separate users for different services
- Connection limits enforced
- IP-based access restrictions

## Application Security

### Authentication & Authorization
1. **JWT tokens**: Secure token generation and validation
2. **Password policies**: Strong password requirements
3. **Session management**: Secure session handling
4. **Rate limiting**: API endpoint protection
5. **RBAC**: Role-based access control

### Input Validation
1. **File upload validation**: Type, size, and content checks
2. **SQL injection prevention**: Parameterized queries
3. **XSS protection**: Input sanitization and CSP headers
4. **CSRF protection**: Token-based protection

### API Security
```python
# Rate limiting configuration
RATE_LIMITS = {
    "auth": "5/minute",
    "api": "100/minute",
    "upload": "10/minute"
}

# Security headers
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'"
}
```

## File Storage Security

### MinIO Security
1. **Access keys**: Strong, randomly generated
2. **Bucket policies**: Restrictive access policies
3. **Encryption**: Server-side encryption enabled
4. **Audit logging**: All access logged
5. **Network isolation**: Internal network only

### File Upload Security
1. **Virus scanning**: All uploads scanned
2. **File type validation**: Whitelist approach
3. **Size limits**: Prevent DoS attacks
4. **Quarantine**: Suspicious files isolated

## Monitoring & Logging

### Security Monitoring
1. **Failed login attempts**: Monitored and alerted
2. **Unusual access patterns**: Detected and investigated
3. **File access**: All file operations logged
4. **System resources**: CPU, memory, disk monitored

### Log Management
1. **Centralized logging**: All logs collected centrally
2. **Log retention**: Appropriate retention policies
3. **Log integrity**: Tamper-proof logging
4. **SIEM integration**: Security information and event management

## Backup Security

### Backup Encryption
1. **At-rest encryption**: All backups encrypted
2. **In-transit encryption**: Secure transfer protocols
3. **Key management**: Secure key storage and rotation
4. **Access control**: Restricted backup access

### Disaster Recovery
1. **Regular testing**: Recovery procedures tested
2. **Offsite storage**: Geographically distributed backups
3. **RTO/RPO**: Defined recovery objectives
4. **Documentation**: Detailed recovery procedures

## Compliance & Auditing

### Security Auditing
1. **Regular assessments**: Quarterly security reviews
2. **Penetration testing**: Annual pen tests
3. **Vulnerability scanning**: Continuous scanning
4. **Compliance checks**: Regular compliance audits

### Documentation
1. **Security policies**: Documented and maintained
2. **Incident response**: Defined procedures
3. **Change management**: Controlled changes
4. **Training**: Security awareness training

## Security Checklist

### Pre-deployment
- [ ] Security scan completed
- [ ] Penetration test passed
- [ ] SSL certificates configured
- [ ] Firewall rules applied
- [ ] Database hardened
- [ ] Secrets properly managed
- [ ] Monitoring configured
- [ ] Backup encryption verified

### Post-deployment
- [ ] Security monitoring active
- [ ] Log analysis configured
- [ ] Incident response tested
- [ ] Backup restoration tested
- [ ] Performance impact assessed
- [ ] Documentation updated
- [ ] Team training completed

### Ongoing
- [ ] Regular security updates
- [ ] Vulnerability assessments
- [ ] Log review and analysis
- [ ] Backup verification
- [ ] Access review
- [ ] Policy updates
- [ ] Training refreshers

## Incident Response

### Response Team
1. **Security lead**: Overall incident coordination
2. **System admin**: Technical response and recovery
3. **Developer**: Application-specific issues
4. **Management**: Business impact assessment

### Response Procedures
1. **Detection**: Automated alerts and monitoring
2. **Assessment**: Impact and scope evaluation
3. **Containment**: Immediate threat mitigation
4. **Eradication**: Root cause elimination
5. **Recovery**: System restoration
6. **Lessons learned**: Post-incident review

## Contact Information

### Security Team
- Security Lead: <EMAIL>
- On-call: +1-XXX-XXX-XXXX
- Emergency: <EMAIL>

### External Resources
- CERT: https://www.cert.org/
- CVE Database: https://cve.mitre.org/
- Security advisories: vendor-specific channels