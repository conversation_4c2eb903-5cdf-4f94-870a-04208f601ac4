#!/bin/bash

# Security Testing Script for Production Environment
set -e

echo "🔒 Running security tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

PASSED=0
FAILED=0
WARNINGS=0

# Function to test endpoint security
test_endpoint_security() {
    local endpoint=$1
    local description=$2
    
    echo -n "Testing $description... "
    
    # Test for common security headers
    response=$(curl -s -I "$endpoint" 2>/dev/null || echo "")
    
    if echo "$response" | grep -q "X-Content-Type-Options: nosniff"; then
        if echo "$response" | grep -q "X-Frame-Options: DENY"; then
            if echo "$response" | grep -q "X-XSS-Protection"; then
                echo -e "${GREEN}✅ PASS${NC}"
                ((PASSED++))
            else
                echo -e "${YELLOW}⚠️  WARNING${NC} (Missing XSS protection)"
                ((WARNINGS++))
            fi
        else
            echo -e "${YELLOW}⚠️  WARNING${NC} (Missing frame options)"
            ((WARNINGS++))
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Missing security headers)"
        ((FAILED++))
    fi
}

# Function to test rate limiting
test_rate_limiting() {
    local endpoint=$1
    local description=$2
    
    echo -n "Testing rate limiting on $description... "
    
    # Send multiple requests quickly
    success_count=0
    for i in {1..10}; do
        if curl -s -o /dev/null -w "%{http_code}" "$endpoint" 2>/dev/null | grep -q "200"; then
            ((success_count++))
        fi
    done
    
    if [ $success_count -lt 10 ]; then
        echo -e "${GREEN}✅ PASS${NC} (Rate limiting active)"
        ((PASSED++))
    else
        echo -e "${YELLOW}⚠️  WARNING${NC} (Rate limiting may not be active)"
        ((WARNINGS++))
    fi
}

# Function to test authentication
test_authentication() {
    echo -n "Testing authentication requirement... "
    
    # Try to access protected endpoint without auth
    response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8000/api/users/profile" 2>/dev/null)
    
    if [ "$response" -eq 401 ] || [ "$response" -eq 403 ]; then
        echo -e "${GREEN}✅ PASS${NC} (Authentication required)"
        ((PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC} (Authentication not enforced)"
        ((FAILED++))
    fi
}

# Function to test SQL injection
test_sql_injection() {
    echo -n "Testing SQL injection protection... "
    
    # Try basic SQL injection
    response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8000/api/designs?search='; DROP TABLE users; --" 2>/dev/null)
    
    if [ "$response" -eq 400 ] || [ "$response" -eq 422 ]; then
        echo -e "${GREEN}✅ PASS${NC} (SQL injection blocked)"
        ((PASSED++))
    elif [ "$response" -eq 500 ]; then
        echo -e "${RED}❌ FAIL${NC} (Possible SQL injection vulnerability)"
        ((FAILED++))
    else
        echo -e "${YELLOW}⚠️  WARNING${NC} (Unexpected response: $response)"
        ((WARNINGS++))
    fi
}

# Function to test file upload security
test_file_upload_security() {
    echo -n "Testing file upload security... "
    
    # Try to upload a potentially malicious file
    temp_file=$(mktemp)
    echo "<?php system(\$_GET['cmd']); ?>" > "$temp_file"
    
    response=$(curl -s -o /dev/null -w "%{http_code}" -F "file=@$temp_file" "http://localhost:8000/api/files/upload" 2>/dev/null)
    
    rm "$temp_file"
    
    if [ "$response" -eq 400 ] || [ "$response" -eq 422 ] || [ "$response" -eq 415 ]; then
        echo -e "${GREEN}✅ PASS${NC} (Malicious file rejected)"
        ((PASSED++))
    elif [ "$response" -eq 401 ] || [ "$response" -eq 403 ]; then
        echo -e "${GREEN}✅ PASS${NC} (Authentication required)"
        ((PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC} (File upload may be vulnerable)"
        ((FAILED++))
    fi
}

# Function to test HTTPS redirect
test_https_redirect() {
    echo -n "Testing HTTPS redirect... "
    
    if command -v openssl >/dev/null 2>&1; then
        response=$(curl -s -o /dev/null -w "%{http_code}" -L "http://localhost" 2>/dev/null)
        
        if [ "$response" -eq 200 ]; then
            # Check if we were redirected to HTTPS
            final_url=$(curl -s -o /dev/null -w "%{url_effective}" -L "http://localhost" 2>/dev/null)
            if echo "$final_url" | grep -q "https://"; then
                echo -e "${GREEN}✅ PASS${NC} (HTTPS redirect active)"
                ((PASSED++))
            else
                echo -e "${YELLOW}⚠️  WARNING${NC} (HTTPS redirect not configured)"
                ((WARNINGS++))
            fi
        else
            echo -e "${YELLOW}⚠️  WARNING${NC} (HTTPS not configured)"
            ((WARNINGS++))
        fi
    else
        echo -e "${YELLOW}⚠️  SKIP${NC} (OpenSSL not available)"
    fi
}

# Function to test container security
test_container_security() {
    echo -n "Testing container security... "
    
    # Check if containers are running as non-root
    non_root_count=0
    total_containers=0
    
    for container in $(docker-compose -f docker-compose.prod.yml ps -q); do
        if [ -n "$container" ]; then
            user=$(docker exec "$container" whoami 2>/dev/null || echo "unknown")
            if [ "$user" != "root" ] && [ "$user" != "unknown" ]; then
                ((non_root_count++))
            fi
            ((total_containers++))
        fi
    done
    
    if [ $total_containers -gt 0 ] && [ $non_root_count -eq $total_containers ]; then
        echo -e "${GREEN}✅ PASS${NC} (All containers run as non-root)"
        ((PASSED++))
    elif [ $non_root_count -gt 0 ]; then
        echo -e "${YELLOW}⚠️  WARNING${NC} ($non_root_count/$total_containers containers run as non-root)"
        ((WARNINGS++))
    else
        echo -e "${RED}❌ FAIL${NC} (Containers running as root)"
        ((FAILED++))
    fi
}

# Function to test database security
test_database_security() {
    echo -n "Testing database security... "
    
    # Try to connect to database from outside
    if command -v psql >/dev/null 2>&1; then
        if timeout 5 psql -h localhost -U bomgen -d bomgen_prod -c "SELECT 1;" 2>/dev/null; then
            echo -e "${RED}❌ FAIL${NC} (Database accessible from outside)"
            ((FAILED++))
        else
            echo -e "${GREEN}✅ PASS${NC} (Database properly isolated)"
            ((PASSED++))
        fi
    else
        echo -e "${YELLOW}⚠️  SKIP${NC} (psql not available)"
    fi
}

# Function to test backup security
test_backup_security() {
    echo -n "Testing backup security... "
    
    # Check if backup files are properly protected
    if [ -d "./backups" ]; then
        permissions=$(stat -c "%a" ./backups 2>/dev/null || echo "000")
        if [ "$permissions" = "755" ] || [ "$permissions" = "750" ]; then
            echo -e "${GREEN}✅ PASS${NC} (Backup directory properly secured)"
            ((PASSED++))
        else
            echo -e "${YELLOW}⚠️  WARNING${NC} (Backup directory permissions: $permissions)"
            ((WARNINGS++))
        fi
    else
        echo -e "${YELLOW}⚠️  WARNING${NC} (Backup directory not found)"
        ((WARNINGS++))
    fi
}

echo "🔍 Security Test Suite"
echo "======================"

echo ""
echo "🌐 Web Security Tests"
echo "===================="

test_endpoint_security "http://localhost" "Frontend security headers"
test_endpoint_security "http://localhost:8000/health" "Backend security headers"
test_https_redirect

echo ""
echo "🔐 Authentication & Authorization Tests"
echo "======================================"

test_authentication
test_rate_limiting "http://localhost:8000/api/auth/login" "login endpoint"

echo ""
echo "💉 Injection Attack Tests"
echo "========================="

test_sql_injection
test_file_upload_security

echo ""
echo "🐳 Container Security Tests"
echo "==========================="

test_container_security

echo ""
echo "🗄️  Database Security Tests"
echo "==========================="

test_database_security

echo ""
echo "💾 Backup Security Tests"
echo "========================"

test_backup_security

echo ""
echo "📊 Security Test Summary"
echo "========================"

total=$((PASSED + FAILED + WARNINGS))

echo -e "Total tests: $total"
echo -e "${GREEN}Passed: $PASSED${NC}"
echo -e "${YELLOW}Warnings: $WARNINGS${NC}"
echo -e "${RED}Failed: $FAILED${NC}"

if [ $FAILED -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All security tests passed!${NC}"
        exit 0
    else
        echo -e "\n${YELLOW}⚠️  Security tests completed with warnings${NC}"
        echo "Please review the warnings and consider implementing additional security measures."
        exit 1
    fi
else
    echo -e "\n${RED}❌ Security tests failed - immediate attention required${NC}"
    echo "Please address the failed tests before deploying to production."
    exit 2
fi