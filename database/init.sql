-- Initial database setup for BOM Generator
-- This script creates the basic database structure

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- User sessions
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Design projects
CREATE TABLE designs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    name <PERSON><PERSON><PERSON><PERSON>(200) NOT NULL,
    original_filename VA<PERSON>HA<PERSON>(255),
    file_path VARCHAR(500),
    status VARCHAR(50) DEFAULT 'uploaded',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Analysis results
CREATE TABLE analysis_results (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    analysis_data JSONB,
    confidence_score DECIMAL(3,2),
    processing_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bill of Materials
CREATE TABLE bom_items (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    part_number VARCHAR(100),
    description TEXT,
    quantity INTEGER,
    material VARCHAR(100),
    volume DECIMAL(10,4),
    weight DECIMAL(10,4),
    unit_weight DECIMAL(10,4)
);

-- 3D Models
CREATE TABLE models_3d (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    model_file_path VARCHAR(500),
    openscad_script TEXT,
    generation_time INTEGER,
    file_size INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Design sharing
CREATE TABLE design_shares (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    owner_id INTEGER REFERENCES users(id),
    shared_with_id INTEGER REFERENCES users(id),
    permission_level VARCHAR(20) DEFAULT 'view',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(design_id, shared_with_id)
);

-- Material database
CREATE TABLE materials (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    density DECIMAL(8,4) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fabrication stages (customizable per project type)
CREATE TABLE fabrication_stages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Part fabrication progress
CREATE TABLE part_fabrication_progress (
    id SERIAL PRIMARY KEY,
    design_id INTEGER REFERENCES designs(id),
    part_id INTEGER REFERENCES bom_items(id),
    current_stage_id INTEGER REFERENCES fabrication_stages(id),
    assigned_to INTEGER REFERENCES users(id),
    started_at TIMESTAMP,
    estimated_completion TIMESTAMP,
    actual_completion TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Stage history for tracking progress over time
CREATE TABLE fabrication_stage_history (
    id SERIAL PRIMARY KEY,
    part_progress_id INTEGER REFERENCES part_fabrication_progress(id),
    stage_id INTEGER REFERENCES fabrication_stages(id),
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    duration_minutes INTEGER,
    notes TEXT,
    updated_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Team/fabricator assignments
CREATE TABLE fabrication_teams (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    lead_user_id INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Team member assignments
CREATE TABLE team_members (
    id SERIAL PRIMARY KEY,
    team_id INTEGER REFERENCES fabrication_teams(id),
    user_id INTEGER REFERENCES users(id),
    role VARCHAR(50),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(team_id, user_id)
);

-- Insert comprehensive default materials with metal fabrication grades
INSERT INTO materials (name, density, category, description) VALUES
-- Steel grades
('Carbon Steel A36', 7850.0, 'Steel', 'Structural steel for construction and fabrication'),
('Carbon Steel 1018', 7870.0, 'Steel', 'Low carbon steel for machining and welding'),
('Carbon Steel 1045', 7870.0, 'Steel', 'Medium carbon steel for shafts and gears'),
('Stainless Steel 304', 8000.0, 'Steel', 'Austenitic stainless steel, corrosion resistant'),
('Stainless Steel 316', 8000.0, 'Steel', 'Marine grade stainless steel'),
('Stainless Steel 410', 7750.0, 'Steel', 'Martensitic stainless steel, hardenable'),
('Tool Steel O1', 7850.0, 'Steel', 'Oil hardening tool steel'),
('Tool Steel A2', 7860.0, 'Steel', 'Air hardening tool steel'),
('Alloy Steel 4140', 7850.0, 'Steel', 'Chromium-molybdenum alloy steel'),
('Alloy Steel 4340', 7850.0, 'Steel', 'Nickel-chromium-molybdenum alloy steel'),

-- Iron grades
('Cast Iron Gray', 7200.0, 'Iron', 'Gray cast iron for engine blocks and housings'),
('Cast Iron Ductile', 7100.0, 'Iron', 'Ductile iron with improved toughness'),
('Wrought Iron', 7700.0, 'Iron', 'Traditional wrought iron for decorative work'),

-- Aluminum grades
('Aluminum 1100', 2710.0, 'Aluminum', 'Pure aluminum, excellent corrosion resistance'),
('Aluminum 2024', 2780.0, 'Aluminum', 'High strength aluminum for aerospace'),
('Aluminum 3003', 2730.0, 'Aluminum', 'General purpose aluminum with manganese'),
('Aluminum 5052', 2680.0, 'Aluminum', 'Marine grade aluminum with magnesium'),
('Aluminum 6061', 2700.0, 'Aluminum', 'Structural aluminum, heat treatable'),
('Aluminum 6063', 2700.0, 'Aluminum', 'Architectural aluminum for extrusions'),
('Aluminum 7075', 2810.0, 'Aluminum', 'High strength aluminum for aircraft'),

-- Other common fabrication metals
('Brass 360', 8500.0, 'Brass', 'Free machining brass'),
('Brass 260', 8530.0, 'Brass', 'Cartridge brass, 70% copper'),
('Bronze 932', 8800.0, 'Bronze', 'Bearing bronze with tin and lead'),
('Copper 110', 8960.0, 'Copper', 'Electrolytic tough pitch copper'),
('Titanium Grade 2', 4510.0, 'Titanium', 'Commercially pure titanium'),
('Inconel 625', 8440.0, 'Superalloy', 'Nickel-chromium superalloy'),

-- Plastics for comparison
('ABS Plastic', 1050.0, 'Plastic', 'General purpose thermoplastic'),
('PLA Plastic', 1240.0, 'Plastic', '3D printing filament'),
('HDPE', 950.0, 'Plastic', 'High density polyethylene'),
('Nylon 6', 1140.0, 'Plastic', 'Engineering thermoplastic');

-- Insert default fabrication stages
INSERT INTO fabrication_stages (name, description, order_index, is_default) VALUES
('Not Started', 'Part has not begun fabrication', 1, TRUE),
('Material Prep', 'Material cutting and preparation', 2, TRUE),
('Machining', 'CNC machining or manual machining operations', 3, TRUE),
('Welding/Assembly', 'Welding and assembly operations', 4, TRUE),
('Quality Check', 'Quality inspection and testing', 5, TRUE),
('Finishing', 'Surface treatment, painting, or coating', 6, TRUE),
('Completed', 'Part fabrication completed', 7, TRUE);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_designs_user_id ON designs(user_id);
CREATE INDEX idx_designs_status ON designs(status);
CREATE INDEX idx_bom_items_design_id ON bom_items(design_id);
CREATE INDEX idx_analysis_results_design_id ON analysis_results(design_id);
CREATE INDEX idx_design_shares_design_id ON design_shares(design_id);
CREATE INDEX idx_design_shares_shared_with_id ON design_shares(shared_with_id);