-- Production Database Initialization Script

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for performance optimization
-- Users table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at);

-- User sessions indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);

-- Designs table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_designs_user_id ON designs(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_designs_status ON designs(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_designs_created_at ON designs(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_designs_name_trgm ON designs USING gin(name gin_trgm_ops);

-- Analysis results indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_results_design_id ON analysis_results(design_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_results_confidence ON analysis_results(confidence_score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_results_created_at ON analysis_results(created_at);

-- BOM items indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bom_items_design_id ON bom_items(design_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bom_items_part_number ON bom_items(part_number);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bom_items_material ON bom_items(material);

-- 3D models indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_models_3d_design_id ON models_3d(design_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_models_3d_created_at ON models_3d(created_at);

-- Design shares indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_design_shares_design_id ON design_shares(design_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_design_shares_shared_with ON design_shares(shared_with_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_design_shares_owner ON design_shares(owner_id);

-- Fabrication progress indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_part_fabrication_progress_design_id ON part_fabrication_progress(design_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_part_fabrication_progress_part_id ON part_fabrication_progress(part_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_part_fabrication_progress_stage ON part_fabrication_progress(current_stage_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_part_fabrication_progress_assigned ON part_fabrication_progress(assigned_to);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_part_fabrication_progress_completion ON part_fabrication_progress(estimated_completion);

-- Fabrication stage history indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fabrication_stage_history_part_progress ON fabrication_stage_history(part_progress_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fabrication_stage_history_stage ON fabrication_stage_history(stage_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fabrication_stage_history_started ON fabrication_stage_history(started_at);

-- Materials indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_materials_name ON materials(name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_materials_category ON materials(category);

-- Create composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_designs_user_status ON designs(user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_designs_user_created ON designs(user_id, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bom_items_design_material ON bom_items(design_id, material);

-- Create partial indexes for active records
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_email ON users(email) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_valid ON user_sessions(user_id, session_token) WHERE expires_at > NOW();

-- Create database statistics
ANALYZE;

-- Set up row-level security policies
ALTER TABLE designs ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE bom_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE models_3d ENABLE ROW LEVEL SECURITY;

-- Policy for designs - users can only see their own designs or shared designs
CREATE POLICY designs_user_policy ON designs
    FOR ALL TO bomgen
    USING (
        user_id = current_setting('app.current_user_id')::integer
        OR id IN (
            SELECT design_id FROM design_shares 
            WHERE shared_with_id = current_setting('app.current_user_id')::integer
        )
    );

-- Policy for analysis results - follow design access
CREATE POLICY analysis_results_user_policy ON analysis_results
    FOR ALL TO bomgen
    USING (
        design_id IN (
            SELECT id FROM designs WHERE 
            user_id = current_setting('app.current_user_id')::integer
            OR id IN (
                SELECT design_id FROM design_shares 
                WHERE shared_with_id = current_setting('app.current_user_id')::integer
            )
        )
    );

-- Policy for BOM items - follow design access
CREATE POLICY bom_items_user_policy ON bom_items
    FOR ALL TO bomgen
    USING (
        design_id IN (
            SELECT id FROM designs WHERE 
            user_id = current_setting('app.current_user_id')::integer
            OR id IN (
                SELECT design_id FROM design_shares 
                WHERE shared_with_id = current_setting('app.current_user_id')::integer
            )
        )
    );

-- Policy for 3D models - follow design access
CREATE POLICY models_3d_user_policy ON models_3d
    FOR ALL TO bomgen
    USING (
        design_id IN (
            SELECT id FROM designs WHERE 
            user_id = current_setting('app.current_user_id')::integer
            OR id IN (
                SELECT design_id FROM design_shares 
                WHERE shared_with_id = current_setting('app.current_user_id')::integer
            )
        )
    );

-- Create monitoring views for database health
CREATE OR REPLACE VIEW db_health_stats AS
SELECT 
    'connections' as metric,
    count(*) as value,
    max_connections as max_value,
    round(100.0 * count(*) / max_connections, 2) as percentage
FROM pg_stat_activity, (SELECT setting::int as max_connections FROM pg_settings WHERE name = 'max_connections') s
UNION ALL
SELECT 
    'database_size' as metric,
    pg_database_size(current_database()) as value,
    NULL as max_value,
    NULL as percentage
UNION ALL
SELECT 
    'cache_hit_ratio' as metric,
    round(100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)), 2) as value,
    100 as max_value,
    round(100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)), 2) as percentage
FROM pg_stat_database
WHERE datname = current_database();

-- Create function to get table sizes
CREATE OR REPLACE FUNCTION get_table_sizes()
RETURNS TABLE(
    table_name text,
    row_count bigint,
    total_size text,
    index_size text,
    table_size text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename as table_name,
        n_tup_ins - n_tup_del as row_count,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
        pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size,
        pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size
    FROM pg_stat_user_tables
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO bomgen;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO bomgen;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO bomgen;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO bomgen;