# Production Environment Variables
# Copy this file to .env.prod and fill in the values

# Database Configuration
DB_PASSWORD=your_secure_database_password_here

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here_minimum_32_characters

# MinIO/S3 Configuration
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key

# Monitoring Configuration
GRAFANA_PASSWORD=your_grafana_admin_password

# Backup Configuration (Optional - for remote backups)
BACKUP_S3_BUCKET=your-backup-bucket-name
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
BACKUP_RETENTION_DAYS=30

# Admin User (Optional - created during deployment)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_admin_password

# SSL Configuration (Optional)
# Place SSL certificates in nginx/ssl/ directory
# SSL_CERT_PATH=nginx/ssl/cert.pem
# SSL_KEY_PATH=nginx/ssl/key.pem

# Domain Configuration (Optional)
# DOMAIN_NAME=yourdomain.com

# Email Configuration (Optional - for notifications)
# SMTP_HOST=smtp.yourprovider.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your_email_password
# SMTP_FROM=<EMAIL>