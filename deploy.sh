#!/bin/bash

# Production Deployment Script for BOM Generator
set -e

echo "🚀 Starting production deployment..."

# Check if .env.prod exists
if [ ! -f .env.prod ]; then
    echo "❌ Error: .env.prod file not found"
    echo "Please create .env.prod with production environment variables"
    exit 1
fi

# Load environment variables
source .env.prod

# Validate required environment variables
required_vars=(
    "DB_PASSWORD"
    "JWT_SECRET_KEY"
    "MINIO_ACCESS_KEY"
    "MINIO_SECRET_KEY"
    "GRAFANA_PASSWORD"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Error: Required environment variable $var is not set"
        exit 1
    fi
done

echo "✅ Environment variables validated"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs backups nginx/ssl monitoring/grafana/{dashboards,datasources}

# Set proper permissions
chmod 755 logs backups
chmod 600 .env.prod

# Pull latest images
echo "📦 Pulling Docker images..."
docker-compose -f docker-compose.prod.yml pull

# Build custom images
echo "🔨 Building application images..."
docker-compose -f docker-compose.prod.yml build --no-cache

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Start database first and wait for it to be ready
echo "🗄️ Starting database..."
docker-compose -f docker-compose.prod.yml up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
timeout=60
while ! docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U bomgen -d bomgen_prod; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        echo "❌ Database failed to start within 60 seconds"
        exit 1
    fi
done

echo "✅ Database is ready"

# Run database migrations
echo "🔄 Running database migrations..."
docker-compose -f docker-compose.prod.yml run --rm backend alembic upgrade head

# Start all services
echo "🚀 Starting all services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
services=("frontend" "backend" "celery-worker")

for service in "${services[@]}"; do
    echo "Checking $service..."
    timeout=120
    while ! docker-compose -f docker-compose.prod.yml ps | grep $service | grep -q "healthy"; do
        sleep 5
        timeout=$((timeout - 5))
        if [ $timeout -le 0 ]; then
            echo "❌ $service failed to become healthy within 2 minutes"
            docker-compose -f docker-compose.prod.yml logs $service
            exit 1
        fi
    done
    echo "✅ $service is healthy"
done

# Run initial data seeding
echo "🌱 Seeding initial data..."
docker-compose -f docker-compose.prod.yml exec -T backend python -c "
from database.seed_data import seed_database
from database.seed_materials import seed_materials
seed_database()
seed_materials()
print('Database seeded successfully')
"

# Create initial admin user if specified
if [ ! -z "$ADMIN_EMAIL" ] && [ ! -z "$ADMIN_PASSWORD" ]; then
    echo "👤 Creating admin user..."
    docker-compose -f docker-compose.prod.yml exec -T backend python -c "
from services.auth_service import AuthService
from database.connection import get_db
auth_service = AuthService()
db = next(get_db())
try:
    auth_service.create_user(
        db=db,
        username='admin',
        email='$ADMIN_EMAIL',
        password='$ADMIN_PASSWORD',
        is_admin=True
    )
    print('Admin user created successfully')
except Exception as e:
    print(f'Admin user creation failed or already exists: {e}')
"
fi

# Setup SSL certificates if provided
if [ -f "nginx/ssl/cert.pem" ] && [ -f "nginx/ssl/key.pem" ]; then
    echo "🔒 SSL certificates found, enabling HTTPS..."
    # Update nginx configuration to enable HTTPS
    sed -i 's/# server {/server {/g' frontend/nginx.conf
    sed -i 's/# }/}/g' frontend/nginx.conf
    docker-compose -f docker-compose.prod.yml restart frontend
fi

# Run backup test
echo "💾 Testing backup system..."
docker-compose -f docker-compose.prod.yml exec -T backup curl -f http://localhost:8080/health

# Display deployment status
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📊 Service Status:"
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "🌐 Access URLs:"
echo "  Frontend: http://localhost"
echo "  Backend API: http://localhost:8000"
echo "  Grafana: http://localhost:3000 (admin / $GRAFANA_PASSWORD)"
echo "  Prometheus: http://localhost:9090"
echo "  MinIO Console: http://localhost:9001"

echo ""
echo "📝 Next Steps:"
echo "  1. Configure SSL certificates for HTTPS"
echo "  2. Set up domain name and DNS"
echo "  3. Configure external monitoring alerts"
echo "  4. Review security settings"
echo "  5. Set up automated backups to external storage"

echo ""
echo "📋 Useful Commands:"
echo "  View logs: docker-compose -f docker-compose.prod.yml logs -f [service]"
echo "  Scale services: docker-compose -f docker-compose.prod.yml up -d --scale backend=3"
echo "  Update: ./update.sh"
echo "  Backup: docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/full"