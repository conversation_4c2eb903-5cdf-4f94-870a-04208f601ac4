#!/bin/bash

# Production Health Check Script
set -e

echo "🏥 Running production health checks..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counters
PASSED=0
FAILED=0
WARNINGS=0

# Function to check service health
check_service() {
    local service=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "Checking $service... "
    
    if response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null); then
        if [ "$response" -eq "$expected_status" ]; then
            echo -e "${GREEN}✅ PASS${NC} (HTTP $response)"
            ((PASSED++))
        else
            echo -e "${RED}❌ FAIL${NC} (HTTP $response, expected $expected_status)"
            ((FAILED++))
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Connection failed)"
        ((FAILED++))
    fi
}

# Function to check Docker container health
check_container() {
    local container=$1
    echo -n "Checking container $container... "
    
    if docker-compose -f docker-compose.prod.yml ps | grep "$container" | grep -q "healthy"; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((PASSED++))
    elif docker-compose -f docker-compose.prod.yml ps | grep "$container" | grep -q "Up"; then
        echo -e "${YELLOW}⚠️  WARNING${NC} (Running but no health check)"
        ((WARNINGS++))
    else
        echo -e "${RED}❌ FAIL${NC} (Not running or unhealthy)"
        ((FAILED++))
    fi
}

# Function to check database connectivity
check_database() {
    echo -n "Checking database connectivity... "
    
    if docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U bomgen -d bomgen_prod >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}"
        ((FAILED++))
    fi
}

# Function to check disk space
check_disk_space() {
    echo -n "Checking disk space... "
    
    usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -lt 80 ]; then
        echo -e "${GREEN}✅ PASS${NC} (${usage}% used)"
        ((PASSED++))
    elif [ "$usage" -lt 90 ]; then
        echo -e "${YELLOW}⚠️  WARNING${NC} (${usage}% used)"
        ((WARNINGS++))
    else
        echo -e "${RED}❌ FAIL${NC} (${usage}% used - critically low)"
        ((FAILED++))
    fi
}

# Function to check memory usage
check_memory() {
    echo -n "Checking memory usage... "
    
    usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$usage" -lt 80 ]; then
        echo -e "${GREEN}✅ PASS${NC} (${usage}% used)"
        ((PASSED++))
    elif [ "$usage" -lt 90 ]; then
        echo -e "${YELLOW}⚠️  WARNING${NC} (${usage}% used)"
        ((WARNINGS++))
    else
        echo -e "${RED}❌ FAIL${NC} (${usage}% used - critically high)"
        ((FAILED++))
    fi
}

# Function to check SSL certificate
check_ssl() {
    local domain=${1:-localhost}
    echo -n "Checking SSL certificate for $domain... "
    
    if command -v openssl >/dev/null 2>&1; then
        if expiry=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null | grep notAfter | cut -d= -f2); then
            expiry_epoch=$(date -d "$expiry" +%s 2>/dev/null || echo "0")
            current_epoch=$(date +%s)
            days_left=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [ "$days_left" -gt 30 ]; then
                echo -e "${GREEN}✅ PASS${NC} ($days_left days remaining)"
                ((PASSED++))
            elif [ "$days_left" -gt 7 ]; then
                echo -e "${YELLOW}⚠️  WARNING${NC} ($days_left days remaining)"
                ((WARNINGS++))
            else
                echo -e "${RED}❌ FAIL${NC} ($days_left days remaining - renewal needed)"
                ((FAILED++))
            fi
        else
            echo -e "${YELLOW}⚠️  SKIP${NC} (HTTPS not configured)"
        fi
    else
        echo -e "${YELLOW}⚠️  SKIP${NC} (openssl not available)"
    fi
}

# Function to check backup status
check_backups() {
    echo -n "Checking backup status... "
    
    if response=$(curl -s http://localhost:8080/backup/status 2>/dev/null); then
        if echo "$response" | grep -q '"status":"success"'; then
            backup_count=$(echo "$response" | grep -o '"filename"' | wc -l)
            echo -e "${GREEN}✅ PASS${NC} ($backup_count recent backups found)"
            ((PASSED++))
        else
            echo -e "${RED}❌ FAIL${NC} (Backup service error)"
            ((FAILED++))
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Cannot connect to backup service)"
        ((FAILED++))
    fi
}

echo "🔍 System Health Checks"
echo "======================="

# System checks
check_disk_space
check_memory

echo ""
echo "🐳 Container Health Checks"
echo "=========================="

# Container health checks
containers=("frontend" "backend" "postgres" "redis" "minio" "celery-worker" "prometheus" "grafana")
for container in "${containers[@]}"; do
    check_container "$container"
done

echo ""
echo "🌐 Service Health Checks"
echo "========================"

# Service health checks
check_service "Frontend" "http://localhost/health"
check_service "Backend API" "http://localhost:8000/health"
check_service "Grafana" "http://localhost:3000/api/health"
check_service "Prometheus" "http://localhost:9090/-/healthy"
check_service "MinIO" "http://localhost:9000/minio/health/live"

echo ""
echo "🗄️  Database Health Checks"
echo "=========================="

check_database

echo ""
echo "🔒 Security Checks"
echo "=================="

check_ssl

echo ""
echo "💾 Backup Checks"
echo "================"

check_backups

echo ""
echo "📊 Summary"
echo "=========="

total=$((PASSED + FAILED + WARNINGS))

echo -e "Total checks: $total"
echo -e "${GREEN}Passed: $PASSED${NC}"
echo -e "${YELLOW}Warnings: $WARNINGS${NC}"
echo -e "${RED}Failed: $FAILED${NC}"

if [ $FAILED -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All health checks passed!${NC}"
        exit 0
    else
        echo -e "\n${YELLOW}⚠️  Health checks completed with warnings${NC}"
        exit 1
    fi
else
    echo -e "\n${RED}❌ Health checks failed - immediate attention required${NC}"
    exit 2
fi