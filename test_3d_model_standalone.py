#!/usr/bin/env python3
"""
Standalone test script for 3D model generation functionality.

This script tests the 3D model generation system with sample geometric data
to verify that OpenSCAD scripts are generated correctly and the system
handles various scenarios properly.
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from services.model_3d_service import OpenSCADGenerator, Model3DService


def test_openscad_generator():
    """Test OpenSCAD script generation with sample data."""
    print("Testing OpenSCAD Generator...")
    
    try:
        generator = OpenSCADGenerator()
        print(f"✓ OpenSCAD executable found: {generator.openscad_executable}")
    except RuntimeError as e:
        print(f"⚠ OpenSCAD not found: {e}")
        print("  Note: OpenSCAD is required for STL generation but script generation will still work")
        # Create generator without calling __init__ to avoid the executable check
        generator = OpenSCADGenerator.__new__(OpenSCADGenerator)
        generator.openscad_executable = "openscad"  # Mock for testing
    
    # Test single part with complex geometry
    print("\n1. Testing single part script generation...")
    
    part_data = {
        'part_number': 'BRACKET-001',
        'description': 'L-shaped mounting bracket',
        'material': 'Steel A36',
        'features': [
            {
                'type': 'contour',
                'closed': True,
                'area': 3200,
                'points': [
                    [0, 0], [80, 0], [80, 20], [20, 20], 
                    [20, 60], [0, 60]
                ]
            },
            {
                'type': 'circle',
                'is_hole': True,
                'center': [10, 10],
                'radius': 4.0
            },
            {
                'type': 'circle',
                'is_hole': True,
                'center': [70, 10],
                'radius': 4.0
            },
            {
                'type': 'circle',
                'is_hole': True,
                'center': [10, 50],
                'radius': 4.0
            }
        ],
        'dimensions': {
            'width': 80.0,
            'height': 60.0,
            'thickness': 8.0
        }
    }
    
    script = generator.generate_part_script(part_data)
    print("Generated OpenSCAD script:")
    print("-" * 50)
    print(script)
    print("-" * 50)
    
    # Verify script content
    assert "BRACKET-001" in script
    assert "linear_extrude(height=8.0)" in script
    assert "polygon([[0, 0], [80, 0], [80, 20], [20, 20], [20, 60], [0, 60]])" in script
    assert "cylinder(h=8.2, r=4.0)" in script
    assert script.count("translate([") >= 3  # Three holes
    print("✓ Single part script generation successful")
    
    # Test assembly with multiple parts
    print("\n2. Testing assembly script generation...")
    
    parts_data = [
        {
            'part_number': 'BASE-PLATE',
            'features': [
                {
                    'type': 'contour',
                    'closed': True,
                    'area': 6000,
                    'points': [[0, 0], [100, 0], [100, 60], [0, 60]]
                }
            ],
            'dimensions': {'thickness': 12.0}
        },
        {
            'part_number': 'SIDE-BRACKET',
            'features': [
                {
                    'type': 'contour',
                    'closed': True,
                    'area': 1800,
                    'points': [[0, 0], [30, 0], [30, 60], [0, 60]]
                }
            ],
            'dimensions': {'thickness': 8.0}
        },
        {
            'part_number': 'TOP-COVER',
            'features': [
                {
                    'type': 'contour',
                    'closed': True,
                    'area': 4500,
                    'points': [[0, 0], [90, 0], [90, 50], [0, 50]]
                }
            ],
            'dimensions': {'thickness': 5.0}
        }
    ]
    
    assembly_script = generator.generate_assembly_script(parts_data)
    print("Generated Assembly OpenSCAD script:")
    print("-" * 50)
    print(assembly_script)
    print("-" * 50)
    
    # Verify assembly script
    assert "// Generated OpenSCAD assembly script" in assembly_script
    assert "// Part: BASE-PLATE" in assembly_script
    assert "// Part: SIDE-BRACKET" in assembly_script
    assert "// Part: TOP-COVER" in assembly_script
    assert "translate([0.0, 0.0, 0.0])" in assembly_script
    assert "translate([100.0, 0.0, 0.0])" in assembly_script
    assert "translate([0.0, 100.0, 0.0])" in assembly_script
    print("✓ Assembly script generation successful")
    
    return True


def test_model_3d_service():
    """Test 3D Model Service functionality."""
    print("\n3. Testing Model3D Service...")
    
    service = Model3DService()
    
    # Test parts extraction from analysis data
    print("\n3.1 Testing parts extraction from analysis data...")
    
    analysis_data = {
        'bom': {
            'items': [
                {
                    'part_number': 'HOUSING-001',
                    'description': 'Main housing',
                    'material': 'Aluminum 6061',
                    'quantity': 1
                },
                {
                    'part_number': 'COVER-001',
                    'description': 'Access cover',
                    'material': 'Steel',
                    'quantity': 1
                }
            ]
        },
        'computer_vision': {
            'features': [
                {
                    'type': 'contour',
                    'closed': True,
                    'area': 5000,
                    'points': [[0, 0], [100, 0], [100, 50], [0, 50]]
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [20, 25],
                    'radius': 6.0
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [80, 25],
                    'radius': 6.0
                }
            ],
            'dimensions': {
                'width': 100.0,
                'height': 50.0,
                'thickness': 15.0
            }
        }
    }
    
    parts = service._extract_parts_from_analysis(analysis_data)
    
    print(f"Extracted {len(parts)} parts:")
    for i, part in enumerate(parts):
        print(f"  Part {i+1}: {part['part_number']} - {part['description']} ({part['material']})")
        print(f"    Features: {len(part['features'])}")
        print(f"    Dimensions: {part['dimensions']}")
    
    assert len(parts) == 2
    assert parts[0]['part_number'] == 'HOUSING-001'
    assert parts[0]['material'] == 'Aluminum 6061'
    assert len(parts[0]['features']) == 3  # 1 contour + 2 holes
    print("✓ Parts extraction successful")
    
    # Test file validation
    print("\n3.2 Testing file validation...")
    
    # Test with non-existent file
    validation = service.validate_model_file("/tmp/nonexistent.stl")
    assert not validation['is_valid']
    assert not validation['file_exists']
    print("✓ Non-existent file validation correct")
    
    # Test with valid STL file
    with tempfile.NamedTemporaryFile(suffix='.stl', delete=False) as temp_file:
        # Write minimal STL header
        temp_file.write(b'0' * 80)  # 80-byte header
        temp_file.write(b'test stl data')
        temp_path = temp_file.name
    
    try:
        validation = service.validate_model_file(temp_path)
        assert validation['is_valid']
        assert validation['file_exists']
        assert validation['file_size'] > 80
        print("✓ Valid STL file validation correct")
    finally:
        os.unlink(temp_path)
    
    return True


def test_integration_workflow():
    """Test complete integration workflow."""
    print("\n4. Testing complete integration workflow...")
    
    # Sample engineering drawing analysis data
    sample_analysis = {
        'computer_vision': {
            'features': [
                {
                    'type': 'contour',
                    'closed': True,
                    'area': 7500,
                    'points': [
                        [0, 0], [150, 0], [150, 30], [120, 30],
                        [120, 50], [30, 50], [30, 30], [0, 30]
                    ]
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [15, 15],
                    'radius': 5.0
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [135, 15],
                    'radius': 5.0
                },
                {
                    'type': 'circle',
                    'is_hole': True,
                    'center': [75, 40],
                    'radius': 8.0
                }
            ],
            'dimensions': {
                'width': 150.0,
                'height': 50.0,
                'thickness': 10.0
            }
        },
        'bom': {
            'items': [
                {
                    'part_number': 'ADAPTER-PLATE-001',
                    'description': 'Motor adapter plate',
                    'material': 'Steel A36',
                    'quantity': 1
                }
            ]
        },
        'ocr': {
            'dimensions': [
                {'value': '150', 'unit': 'mm', 'type': 'length'},
                {'value': '50', 'unit': 'mm', 'type': 'width'},
                {'value': '10', 'unit': 'mm', 'type': 'thickness'}
            ],
            'annotations': [
                'M10 x 1.5 - 6H',
                '2X ⌀10 +0.1/-0.0',
                '⌀16 +0.2/-0.0'
            ]
        }
    }
    
    print("Sample analysis data:")
    print(f"  - Features detected: {len(sample_analysis['computer_vision']['features'])}")
    print(f"  - BOM items: {len(sample_analysis['bom']['items'])}")
    print(f"  - Dimensions: {sample_analysis['computer_vision']['dimensions']}")
    
    # Extract parts and generate scripts
    service = Model3DService()
    try:
        generator = OpenSCADGenerator()
    except RuntimeError:
        # OpenSCAD not found, create mock generator
        generator = OpenSCADGenerator.__new__(OpenSCADGenerator)
        generator.openscad_executable = "openscad"
    
    parts = service._extract_parts_from_analysis(sample_analysis)
    print(f"\nExtracted parts: {len(parts)}")
    
    for part in parts:
        print(f"\nGenerating script for {part['part_number']}...")
        script = generator.generate_part_script(part)
        
        # Verify script quality
        lines = script.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        print(f"  Script length: {len(lines)} lines ({len(non_empty_lines)} non-empty)")
        print(f"  Contains extrusion: {'linear_extrude' in script}")
        print(f"  Contains holes: {'cylinder' in script}")
        print(f"  Contains polygon: {'polygon' in script}")
        
        # Show first few lines
        print("  First few lines:")
        for line in lines[:8]:
            if line.strip():
                print(f"    {line}")
    
    print("✓ Integration workflow successful")
    return True


def test_error_handling():
    """Test error handling scenarios."""
    print("\n5. Testing error handling...")
    
    try:
        generator = OpenSCADGenerator()
    except RuntimeError:
        # OpenSCAD not found, create mock generator
        generator = OpenSCADGenerator.__new__(OpenSCADGenerator)
        generator.openscad_executable = "openscad"
    service = Model3DService()
    
    # Test with empty part data
    print("\n5.1 Testing empty part data...")
    empty_part = {'part_number': 'EMPTY', 'features': [], 'dimensions': {}}
    script = generator.generate_part_script(empty_part)
    assert "cube([50.0, 50.0, 10.0])" in script  # Should use fallback
    print("✓ Empty part data handled correctly")
    
    # Test with malformed features
    print("\n5.2 Testing malformed features...")
    malformed_part = {
        'part_number': 'MALFORMED',
        'features': [
            {'type': 'unknown_type'},
            {'type': 'contour', 'closed': False},  # Not closed
            {'type': 'circle', 'is_hole': True}  # Missing center/radius
        ],
        'dimensions': {'width': 30, 'height': 20, 'thickness': 5}
    }
    script = generator.generate_part_script(malformed_part)
    assert "cube([30, 20, 5])" in script  # Should fallback to dimensions
    print("✓ Malformed features handled correctly")
    
    # Test empty analysis data
    print("\n5.3 Testing empty analysis data...")
    empty_analysis = {}
    parts = service._extract_parts_from_analysis(empty_analysis)
    assert len(parts) == 0
    print("✓ Empty analysis data handled correctly")
    
    print("✓ Error handling tests successful")
    return True


def main():
    """Run all tests."""
    print("=" * 60)
    print("3D Model Generation System Test Suite")
    print("=" * 60)
    
    try:
        # Run all test suites
        test_openscad_generator()
        test_model_3d_service()
        test_integration_workflow()
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("The 3D model generation system is working correctly.")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)