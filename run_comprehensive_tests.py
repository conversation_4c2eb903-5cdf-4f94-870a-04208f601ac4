#!/usr/bin/env python3
"""
Comprehensive test runner for the entire application
"""
import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description, cwd=None):
    """Run a command and return success status"""
    print(f"\n🔍 {description}")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True,
            cwd=cwd,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} - FAILED")
            if result.stderr:
                print("Error:", result.stderr)
            if result.stdout:
                print("Output:", result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"💥 {description} - ERROR: {e}")
        return False

def main():
    """Run comprehensive test suite"""
    print("🚀 COMPREHENSIVE APPLICATION TEST SUITE")
    print("=" * 60)
    
    tests = [
        # Core functionality tests
        ("python test_setup.py", "Database Models & Setup", "backend"),
        ("python run_ocr_tests.py", "OCR System Tests", "."),
        ("python test_ocr_standalone.py", "OCR Standalone Tests", "."),
        ("python test_3d_model_standalone.py", "3D Model Generation", "."),
        ("python test_bom_standalone.py", "BOM Generation System", "."),
        ("python test_weight_api_standalone.py", "Weight Calculation API", "."),
        ("python test_analysis_core.py", "Drawing Analysis Core", "."),
        
        # Backend tests
        ("python -m pytest tests/test_integration_simple.py -v", "Simple Integration Tests", "backend"),
        ("python -m pytest tests/test_bom_service.py -v", "BOM Service Tests", "backend"),
        
        # System health checks
        ("./health-check.sh", "System Health Check", "."),
        
        # Security tests
        ("./security/security-test.sh", "Security Test Suite", "."),
        
        # Frontend tests (if available)
        ("npm test -- --watchAll=false --coverage", "Frontend Tests", "frontend"),
        ("npm run lint", "Frontend Linting", "frontend"),
        ("npm run type-check", "TypeScript Check", "frontend"),
    ]
    
    passed = 0
    failed = 0
    skipped = 0
    
    for cmd, description, cwd in tests:
        # Check if the test file/script exists
        if cwd == ".":
            test_path = Path(cmd.split()[1] if len(cmd.split()) > 1 else cmd.split()[0])
        else:
            test_path = Path(cwd) / (cmd.split()[1] if len(cmd.split()) > 1 else cmd.split()[0])
        
        # Skip if test file doesn't exist (for some optional tests)
        if not test_path.exists() and not cmd.startswith("./") and not cmd.startswith("npm"):
            print(f"⏭️  {description} - SKIPPED (file not found)")
            skipped += 1
            continue
        
        if run_command(cmd, description, cwd):
            passed += 1
        else:
            failed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 60)
    total = passed + failed + skipped
    print(f"Total Tests: {total}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"⏭️  Skipped: {skipped}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! System is healthy.")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the failures above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())