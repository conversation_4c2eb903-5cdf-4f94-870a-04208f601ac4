#!/usr/bin/env python3
"""
Weight Calculation Functionality Demo

This script demonstrates the weight calculation engine capabilities
including volume calculations, material matching, and weight summaries.
"""

import sys
import os
import json
from decimal import Decimal

# Add backend to path
sys.path.append('backend')

from services.weight_service import (
    WeightCalculationService, VolumeCalculator, MaterialMatcher,
    GeometricDimensions, VolumeCalculationMethod
)
from models.material import Material
from models.design import BOMItem, AnalysisResult


def demo_volume_calculations():
    """Demonstrate volume calculation methods"""
    print("=" * 60)
    print("VOLUME CALCULATION DEMONSTRATIONS")
    print("=" * 60)
    
    calculator = VolumeCalculator()
    
    # Test 1: Rectangular part
    print("\n1. Rectangular Part Volume Calculation")
    print("-" * 40)
    dimensions = GeometricDimensions(
        length=100.0,  # mm
        width=50.0,    # mm
        height=10.0    # mm
    )
    
    result = calculator._calculate_rectangular_volume(dimensions)
    print(f"Dimensions: {dimensions.length} x {dimensions.width} x {dimensions.height} mm")
    print(f"Volume: {result.volume_cm3:.2f} cm³")
    print(f"Method: {result.calculation_method.value}")
    print(f"Confidence: {result.confidence:.1%}")
    if result.assumptions:
        print(f"Assumptions: {', '.join(result.assumptions)}")
    
    # Test 2: Cylindrical part
    print("\n2. Cylindrical Part Volume Calculation")
    print("-" * 40)
    dimensions = GeometricDimensions(
        diameter=25.0,  # mm
        height=100.0    # mm
    )
    
    result = calculator._calculate_cylindrical_volume(dimensions)
    print(f"Diameter: {dimensions.diameter} mm, Height: {dimensions.height} mm")
    print(f"Volume: {result.volume_cm3:.2f} cm³")
    print(f"Method: {result.calculation_method.value}")
    print(f"Confidence: {result.confidence:.1%}")
    
    # Test 3: Part with missing dimensions
    print("\n3. Part with Missing Dimensions (Default Thickness)")
    print("-" * 40)
    dimensions = GeometricDimensions(
        length=80.0,   # mm
        width=40.0     # mm
        # No height - will use default
    )
    
    result = calculator._calculate_rectangular_volume(dimensions)
    print(f"Dimensions: {dimensions.length} x {dimensions.width} x ? mm")
    print(f"Volume: {result.volume_cm3:.2f} cm³")
    print(f"Method: {result.calculation_method.value}")
    print(f"Confidence: {result.confidence:.1%}")
    print(f"Assumptions: {', '.join(result.assumptions)}")


def demo_dimension_parsing():
    """Demonstrate dimension parsing from text"""
    print("\n\n" + "=" * 60)
    print("DIMENSION PARSING DEMONSTRATIONS")
    print("=" * 60)
    
    calculator = VolumeCalculator()
    
    test_texts = [
        "25.4 mm",
        "2.54 cm", 
        "1.0 in",
        "1.5\"",
        "100",
        "Ø50 mm",
        "Length: 125.5 mm",
        "Invalid text"
    ]
    
    print("\nParsing dimensions from text:")
    print("-" * 30)
    for text in test_texts:
        result = calculator._parse_dimension_text(text)
        if result:
            print(f"'{text}' → {result:.1f} mm")
        else:
            print(f"'{text}' → No dimension found")


def demo_material_matching():
    """Demonstrate material matching capabilities"""
    print("\n\n" + "=" * 60)
    print("MATERIAL MATCHING DEMONSTRATIONS")
    print("=" * 60)
    
    # Create sample materials
    materials = [
        Material(id=1, name="Carbon Steel A36", density=7850.0, category="Steel"),
        Material(id=2, name="Stainless Steel 304", density=8000.0, category="Steel"),
        Material(id=3, name="Aluminum 6061", density=2700.0, category="Aluminum"),
        Material(id=4, name="Brass 360", density=8500.0, category="Brass"),
        Material(id=5, name="ABS Plastic", density=1050.0, category="Plastic"),
    ]
    
    matcher = MaterialMatcher()
    
    # Mock database for testing
    class MockDB:
        def query(self, model):
            return self
        
        def filter(self, condition):
            return self
        
        def first(self):
            return None
        
        def all(self):
            return materials
    
    mock_db = MockDB()
    
    test_materials = [
        "steel",
        "stainless",
        "aluminum",
        "plastic",
        "unknown_material"
    ]
    
    print("\nMaterial matching results:")
    print("-" * 30)
    for material_name in test_materials:
        result = matcher._fuzzy_match_material(material_name, mock_db)
        if result:
            print(f"'{material_name}' → {result.name} ({result.density} kg/m³)")
        else:
            print(f"'{material_name}' → No match found")


def demo_weight_calculations():
    """Demonstrate complete weight calculations"""
    print("\n\n" + "=" * 60)
    print("WEIGHT CALCULATION DEMONSTRATIONS")
    print("=" * 60)
    
    # Sample analysis data
    analysis_data = {
        'cv_results': {
            'features': [
                {
                    'feature_type': 'rectangle',
                    'width': 100.0,
                    'height': 50.0
                }
            ]
        },
        'ocr_results': {
            'extracted_texts': [
                {
                    'text': '10 mm',
                    'text_type': 'dimension',
                    'confidence': 85
                },
                {
                    'text': 'STEEL',
                    'text_type': 'material',
                    'confidence': 90
                }
            ]
        }
    }
    
    # Sample BOM items
    bom_items = [
        {
            'part_number': 'PLATE-001',
            'description': 'Base plate',
            'quantity': 1,
            'material': 'Carbon Steel A36',
            'volume_cm3': 50.0  # 100mm x 50mm x 10mm
        },
        {
            'part_number': 'ROD-002', 
            'description': 'Support rod',
            'quantity': 4,
            'material': 'Aluminum 6061',
            'volume_cm3': 19.63  # π * (12.5mm)² * 100mm / 1000
        },
        {
            'part_number': 'BRACKET-003',
            'description': 'Mounting bracket',
            'quantity': 2,
            'material': 'Stainless Steel 304',
            'volume_cm3': 25.0
        }
    ]
    
    # Material properties
    materials = {
        'Carbon Steel A36': {'density': 7850.0, 'category': 'Steel'},
        'Aluminum 6061': {'density': 2700.0, 'category': 'Aluminum'},
        'Stainless Steel 304': {'density': 8000.0, 'category': 'Steel'}
    }
    
    print("\nWeight calculations for sample assembly:")
    print("-" * 50)
    
    total_weight = 0.0
    weight_breakdown = []
    
    for item in bom_items:
        material_props = materials[item['material']]
        
        # Calculate weight
        volume_m3 = item['volume_cm3'] / 1_000_000  # Convert cm³ to m³
        unit_weight_kg = volume_m3 * material_props['density']
        total_weight_kg = unit_weight_kg * item['quantity']
        total_weight += total_weight_kg
        
        weight_breakdown.append({
            'part': item['part_number'],
            'material': item['material'],
            'volume_cm3': item['volume_cm3'],
            'unit_weight_kg': unit_weight_kg,
            'quantity': item['quantity'],
            'total_weight_kg': total_weight_kg
        })
        
        print(f"Part: {item['part_number']}")
        print(f"  Material: {item['material']}")
        print(f"  Volume: {item['volume_cm3']:.2f} cm³")
        print(f"  Unit Weight: {unit_weight_kg:.4f} kg")
        print(f"  Quantity: {item['quantity']}")
        print(f"  Total Weight: {total_weight_kg:.4f} kg")
        print()
    
    print(f"TOTAL ASSEMBLY WEIGHT: {total_weight:.4f} kg")
    
    # Material distribution
    print("\nMaterial distribution:")
    print("-" * 25)
    material_weights = {}
    for item in weight_breakdown:
        material = item['material']
        if material not in material_weights:
            material_weights[material] = 0
        material_weights[material] += item['total_weight_kg']
    
    for material, weight in material_weights.items():
        percentage = (weight / total_weight) * 100
        print(f"{material}: {weight:.4f} kg ({percentage:.1f}%)")


def demo_validation_scenarios():
    """Demonstrate validation scenarios and edge cases"""
    print("\n\n" + "=" * 60)
    print("VALIDATION SCENARIOS")
    print("=" * 60)
    
    calculator = VolumeCalculator()
    
    scenarios = [
        {
            'name': 'Very Small Part',
            'dimensions': GeometricDimensions(length=0.5, width=0.3, height=0.1),
            'expected_issues': ['Very small dimensions']
        },
        {
            'name': 'Missing Height',
            'dimensions': GeometricDimensions(length=100.0, width=50.0),
            'expected_issues': ['Default thickness assumption']
        },
        {
            'name': 'Large Part',
            'dimensions': GeometricDimensions(length=2000.0, width=1000.0, height=50.0),
            'expected_issues': ['Large part - verify accuracy']
        },
        {
            'name': 'Circular Part',
            'dimensions': GeometricDimensions(diameter=100.0, height=25.0),
            'expected_issues': []
        }
    ]
    
    print("\nValidation scenarios:")
    print("-" * 30)
    
    for scenario in scenarios:
        print(f"\nScenario: {scenario['name']}")
        
        if scenario['dimensions'].diameter:
            result = calculator._calculate_cylindrical_volume(scenario['dimensions'])
        else:
            result = calculator._calculate_rectangular_volume(scenario['dimensions'])
        
        print(f"  Volume: {result.volume_cm3:.2f} cm³")
        print(f"  Confidence: {result.confidence:.1%}")
        
        if result.notes:
            print(f"  Notes: {', '.join(result.notes)}")
        if result.assumptions:
            print(f"  Assumptions: {', '.join(result.assumptions)}")
        
        # Validation flags
        validation_flags = []
        if result.volume_cm3 < 1.0:
            validation_flags.append("Very small volume")
        if result.confidence < 0.5:
            validation_flags.append("Low confidence")
        if result.volume_cm3 > 1000.0:
            validation_flags.append("Large volume")
        
        if validation_flags:
            print(f"  Validation Flags: {', '.join(validation_flags)}")


def demo_export_format():
    """Demonstrate weight calculation export format"""
    print("\n\n" + "=" * 60)
    print("EXPORT FORMAT DEMONSTRATION")
    print("=" * 60)
    
    # Sample weight calculation results
    weight_results = [
        {
            'part_number': 'PLATE-001',
            'description': 'Base plate',
            'material': 'Carbon Steel A36',
            'volume_cm3': 50.0,
            'unit_weight_kg': 0.3925,
            'quantity': 1,
            'total_weight_kg': 0.3925,
            'confidence': 85.0
        },
        {
            'part_number': 'ROD-002',
            'description': 'Support rod',
            'material': 'Aluminum 6061',
            'volume_cm3': 19.63,
            'unit_weight_kg': 0.0530,
            'quantity': 4,
            'total_weight_kg': 0.2120,
            'confidence': 80.0
        }
    ]
    
    print("\nWeight calculation export (CSV format):")
    print("-" * 45)
    
    # CSV header
    headers = [
        'Part Number', 'Description', 'Material', 'Volume (cm³)',
        'Unit Weight (kg)', 'Quantity', 'Total Weight (kg)', 'Confidence (%)'
    ]
    
    print(','.join(headers))
    
    # CSV data
    for result in weight_results:
        row = [
            result['part_number'],
            result['description'],
            result['material'],
            f"{result['volume_cm3']:.2f}",
            f"{result['unit_weight_kg']:.4f}",
            str(result['quantity']),
            f"{result['total_weight_kg']:.4f}",
            f"{result['confidence']:.1f}"
        ]
        print(','.join(row))
    
    # Summary
    total_weight = sum(r['total_weight_kg'] for r in weight_results)
    avg_confidence = sum(r['confidence'] for r in weight_results) / len(weight_results)
    
    print(f"\nSummary:")
    print(f"Total Weight: {total_weight:.4f} kg")
    print(f"Average Confidence: {avg_confidence:.1f}%")
    print(f"Part Count: {len(weight_results)}")


def main():
    """Run all weight calculation demonstrations"""
    print("WEIGHT CALCULATION ENGINE DEMONSTRATION")
    print("=" * 60)
    print("This demo shows the capabilities of the weight calculation system")
    print("including volume calculations, material matching, and weight summaries.")
    
    try:
        demo_volume_calculations()
        demo_dimension_parsing()
        demo_material_matching()
        demo_weight_calculations()
        demo_validation_scenarios()
        demo_export_format()
        
        print("\n\n" + "=" * 60)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("=" * 60)
        print("\nKey Features Demonstrated:")
        print("✓ Volume calculation from 2D dimensions")
        print("✓ Multiple calculation methods (rectangular, cylindrical)")
        print("✓ Dimension parsing from text")
        print("✓ Material matching and database lookup")
        print("✓ Weight calculations with material density")
        print("✓ Confidence scoring and validation")
        print("✓ Export formatting for BOM integration")
        print("✓ Error handling and edge cases")
        
        print("\nNext Steps:")
        print("- Integrate with drawing analysis pipeline")
        print("- Add material database seeding")
        print("- Implement API endpoints")
        print("- Add frontend weight display components")
        
    except Exception as e:
        print(f"\nERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())