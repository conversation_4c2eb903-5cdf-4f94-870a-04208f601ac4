#!/usr/bin/env python3
"""
Demo of OCR functionality for Task 8 - Engineering Drawing Text Extraction
"""

import cv2
import numpy as np
from processing.ocr.text_extractor import OCR<PERSON><PERSON>tExtractor, <PERSON><PERSON>Matcher, TextType

def create_engineering_drawing():
    """Create a realistic engineering drawing with various text types"""
    # Create white background
    image = np.ones((500, 800, 3), dtype=np.uint8) * 255
    
    # Title block
    cv2.putText(image, "MECHANICAL PART DRAWING", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(image, "DWG NO: MP-001", (50, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    # Dimensions
    cv2.putText(image, "2.50±0.01", (200, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(image, "1.25", (300, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(image, "R0.125", (400, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(image, "3/4", (500, 300), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    # Part numbers
    cv2.putText(image, "PART NO: ABC-123456", (50, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "P/N: XYZ-789", (50, 380), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    # Materials
    cv2.putText(image, "MATERIAL: STEEL A36", (50, 420), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "ALUMINUM 6061", (300, 420), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    # Notes and annotations
    cv2.putText(image, "NOTE: ALL DIMENSIONS IN INCHES", (50, 460), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(image, "SCALE: 1:1", (400, 460), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    return image

def demonstrate_ocr_capabilities():
    """Demonstrate OCR text extraction capabilities"""
    print("🔧 OCR Engineering Drawing Analysis Demo")
    print("=" * 60)
    
    # Create test drawing
    print("📐 Creating synthetic engineering drawing...")
    drawing = create_engineering_drawing()
    
    # Initialize OCR system
    print("🤖 Initializing OCR text extraction system...")
    extractor = OCRTextExtractor()
    
    # Extract all text
    print("📖 Extracting text from drawing...")
    result = extractor.extract_text_from_image(drawing)
    
    print(f"\n📊 OCR Processing Results:")
    print(f"  ⏱️  Processing time: {result.processing_time:.2f} seconds")
    print(f"  📈 Overall confidence: {result.overall_confidence:.1f}%")
    print(f"  📝 Total text elements: {len(result.extracted_texts)}")
    
    if result.errors:
        print(f"  ❌ Errors: {result.errors}")
    if result.warnings:
        print(f"  ⚠️  Warnings: {result.warnings}")
    
    # Categorize extracted text
    print(f"\n📋 Extracted Text by Category:")
    categories = {
        TextType.DIMENSION: [],
        TextType.PART_NUMBER: [],
        TextType.MATERIAL: [],
        TextType.ANNOTATION: [],
        TextType.GENERAL: []
    }
    
    for text_elem in result.extracted_texts:
        categories[text_elem.text_type].append({
            'text': text_elem.text,
            'confidence': text_elem.confidence,
            'bbox': text_elem.bbox
        })
    
    for text_type, items in categories.items():
        if items:
            print(f"\n  🏷️  {text_type.value.upper()}:")
            for item in items:
                print(f"    • '{item['text']}' (confidence: {item['confidence']:.1f}%)")
    
    # Extract specific patterns
    print(f"\n🎯 Pattern-Specific Extraction:")
    patterns = extractor.extract_specific_patterns(drawing)
    
    for pattern_type, items in patterns.items():
        if items:
            print(f"  📌 {pattern_type.title()}: {items}")
    
    # Demonstrate pattern matching
    print(f"\n🔍 Pattern Classification Examples:")
    matcher = PatternMatcher()
    
    test_texts = [
        "2.50±0.01",
        "ABC-123456", 
        "STEEL A36",
        "NOTE: ALL DIMENSIONS IN INCHES",
        "Random text"
    ]
    
    for text in test_texts:
        classification = matcher.classify_text(text)
        print(f"  '{text}' → {classification.value}")
    
    print(f"\n✅ OCR Demo Complete!")
    print("🎉 Task 8 OCR system successfully extracts and classifies engineering drawing text!")

if __name__ == "__main__":
    demonstrate_ocr_capabilities()