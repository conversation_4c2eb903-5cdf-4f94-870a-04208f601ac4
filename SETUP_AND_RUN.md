# BOM Generator - Setup and Run Guide

## 🚀 Quick Start

Get the BOM Generator up and running in minutes with this comprehensive setup guide.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Ubuntu 20.04+ / macOS 10.15+ / Windows 10+ (with WSL2)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 20GB free space minimum
- **CPU**: 4+ cores recommended

### Required Software
- **Docker**: Version 20.0+
- **Docker Compose**: Version 2.0+
- **Git**: Latest version
- **Node.js**: Version 18+ (for development)
- **Python**: Version 3.11+ (for development)

## 🛠️ Installation

### Step 1: Install Docker

#### Ubuntu/Debian
```bash
# Update package index
sudo apt update

# Install Docker
sudo apt install -y docker.io docker-compose

# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker

# Add your user to docker group
sudo usermod -aG docker $USER

# Log out and back in for group changes to take effect
```

#### macOS
```bash
# Install Docker Desktop from https://docker.com/products/docker-desktop
# Or using Homebrew:
brew install --cask docker
```

#### Windows
1. Install Docker Desktop from https://docker.com/products/docker-desktop
2. Enable WSL2 integration
3. Restart your computer

### Step 2: Clone the Repository

```bash
git clone <repository-url>
cd bom-generator
```

### Step 3: Choose Your Setup

## 🏃‍♂️ Development Setup (Recommended for Testing)

Perfect for trying out the application or development work.

### Quick Development Start

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to start (about 2-3 minutes)
docker-compose -f docker-compose.dev.yml logs -f

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Development Setup Details

1. **Start the development environment**:
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. **Check service status**:
   ```bash
   docker-compose -f docker-compose.dev.yml ps
   ```

3. **View logs**:
   ```bash
   # All services
   docker-compose -f docker-compose.dev.yml logs -f
   
   # Specific service
   docker-compose -f docker-compose.dev.yml logs -f backend
   ```

4. **Run database migrations**:
   ```bash
   docker-compose -f docker-compose.dev.yml exec backend alembic upgrade head
   ```

5. **Seed the database with sample data**:
   ```bash
   docker-compose -f docker-compose.dev.yml exec backend python -c "
   from database.seed_data import seed_database
   from database.seed_materials import seed_materials
   seed_database()
   seed_materials()
   print('Database seeded successfully')
   "
   ```

### Development URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: localhost:5432 (bomgen/bomgen/bomgen_dev)
- **Redis**: localhost:6379
- **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin)

## 🏭 Production Setup

For production deployments with monitoring, security, and scalability.

### Step 1: Environment Setup

```bash
# Run the automated setup script
./setup_env.sh
```

This script will:
- Install system dependencies
- Configure firewall
- Generate secure passwords
- Create directory structure
- Set up monitoring

### Step 2: Configure Environment

```bash
# Copy and edit the production environment file
cp .env.prod.example .env.prod
nano .env.prod  # or use your preferred editor
```

**Required Configuration**:
```bash
# Database password (auto-generated by setup script)
DB_PASSWORD=your_secure_password

# JWT secret key (auto-generated)
JWT_SECRET_KEY=your_jwt_secret_key

# MinIO credentials (auto-generated)
MINIO_ACCESS_KEY=your_access_key
MINIO_SECRET_KEY=your_secret_key

# Grafana admin password (auto-generated)
GRAFANA_PASSWORD=your_grafana_password

# Optional: Admin user for initial setup
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_admin_password
```

### Step 3: SSL Configuration (Recommended)

```bash
# Create SSL directory
mkdir -p nginx/ssl

# Copy your SSL certificates
cp your-certificate.pem nginx/ssl/cert.pem
cp your-private-key.pem nginx/ssl/key.pem

# Set proper permissions
chmod 600 nginx/ssl/*
```

### Step 4: Deploy to Production

```bash
# Deploy the application
./deploy.sh
```

The deployment script will:
- Build Docker images
- Start all services
- Run database migrations
- Seed initial data
- Create admin user (if configured)
- Verify deployment health

### Step 5: Verify Production Deployment

```bash
# Run comprehensive health checks
./health-check.sh

# Run security tests
./security/security-test.sh
```

### Production URLs
- **Frontend**: http://localhost (or your domain)
- **Backend API**: http://localhost:8000
- **Grafana Monitoring**: http://localhost:3000
- **Prometheus Metrics**: http://localhost:9090
- **MinIO Console**: http://localhost:9001

## 🔧 Common Operations

### Starting Services

```bash
# Development
docker-compose -f docker-compose.dev.yml up -d

# Production
docker-compose -f docker-compose.prod.yml up -d
```

### Stopping Services

```bash
# Development
docker-compose -f docker-compose.dev.yml down

# Production
docker-compose -f docker-compose.prod.yml down
```

### Viewing Logs

```bash
# All services
docker-compose -f docker-compose.dev.yml logs -f

# Specific service
docker-compose -f docker-compose.dev.yml logs -f backend

# Last 100 lines
docker-compose -f docker-compose.dev.yml logs --tail=100 backend
```

### Database Operations

```bash
# Connect to database
docker-compose -f docker-compose.dev.yml exec postgres psql -U bomgen -d bomgen_dev

# Run migrations
docker-compose -f docker-compose.dev.yml exec backend alembic upgrade head

# Create new migration
docker-compose -f docker-compose.dev.yml exec backend alembic revision --autogenerate -m "Description"

# Reset database (development only)
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d
```

### File Storage Operations

```bash
# Access MinIO console
# Navigate to http://localhost:9001
# Login: minioadmin / minioadmin (dev) or your configured credentials (prod)

# List buckets via CLI
docker-compose -f docker-compose.dev.yml exec backend python -c "
from services.file_service import FileService
fs = FileService()
print('Buckets:', fs.list_buckets())
"
```

## 🧪 Testing the Application

### 1. Create a Test User

```bash
# Via API (development)
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpassword123"
  }'
```

### 2. Login and Get Token

```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpassword123"
  }'
```

### 3. Upload a Test Drawing

```bash
# Save the token from login response
TOKEN="your_jwt_token_here"

# Upload a test file
curl -X POST "http://localhost:8000/api/files/upload" \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@path/to/your/drawing.pdf"
```

### 4. Test OCR Processing

```bash
# Process the uploaded file
curl -X POST "http://localhost:8000/api/ocr/process" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "file_id": "file_id_from_upload_response"
  }'
```

## 🔍 Monitoring and Maintenance

### Health Monitoring

```bash
# Check application health
curl http://localhost:8000/health

# Run comprehensive health check
./health-check.sh

# Check individual service health
docker-compose -f docker-compose.prod.yml ps
```

### Performance Monitoring

Access Grafana dashboards:
1. Navigate to http://localhost:3000
2. Login with admin / your_grafana_password
3. View pre-configured dashboards for:
   - System metrics (CPU, memory, disk)
   - Application metrics (response times, error rates)
   - Database performance
   - Storage usage

### Log Monitoring

```bash
# View application logs
docker-compose logs -f backend

# View error logs
tail -f logs/error_reports.log

# View system monitoring logs
tail -f logs/system_monitor.log
```

### Backup Operations

```bash
# Manual backup (production)
docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/full

# Check backup status
docker-compose -f docker-compose.prod.yml exec backup curl http://localhost:8080/backup/status

# List backup files
ls -la backups/
```

## 🔄 Updates and Maintenance

### Updating the Application

```bash
# Production update with zero downtime
./update.sh

# Development update
git pull origin main
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml build --no-cache
docker-compose -f docker-compose.dev.yml up -d
```

### Scaling Services

```bash
# Scale backend services (production)
docker-compose -f docker-compose.prod.yml up -d --scale backend=3 --scale celery-worker=2

# Check scaled services
docker-compose -f docker-compose.prod.yml ps
```

## 🐛 Troubleshooting

### Common Issues

#### Services Won't Start
```bash
# Check Docker daemon
sudo systemctl status docker

# Check logs for errors
docker-compose logs backend

# Check available resources
docker system df
free -h
df -h
```

#### Database Connection Issues
```bash
# Check database status
docker-compose exec postgres pg_isready -U bomgen -d bomgen_dev

# Reset database (development only)
docker-compose down -v
docker-compose up -d postgres
# Wait for database to start, then:
docker-compose up -d
```

#### Port Already in Use
```bash
# Find process using port
sudo lsof -i :8000

# Kill process if needed
sudo kill -9 <PID>

# Or change ports in docker-compose.yml
```

#### Out of Disk Space
```bash
# Clean up Docker
docker system prune -a

# Remove old images
docker image prune -a

# Remove unused volumes
docker volume prune
```

### Getting Help

1. **Check logs first**:
   ```bash
   docker-compose logs [service_name]
   ```

2. **Verify configuration**:
   ```bash
   # Check environment variables
   docker-compose config
   
   # Validate docker-compose file
   docker-compose -f docker-compose.dev.yml config
   ```

3. **Run health checks**:
   ```bash
   ./health-check.sh
   ```

4. **Check system resources**:
   ```bash
   docker stats
   free -h
   df -h
   ```

## 📚 Additional Resources

### Documentation
- **API Documentation**: http://localhost:8000/docs (when running)
- **Production Guide**: [PRODUCTION.md](PRODUCTION.md)
- **Security Guide**: [security/security-hardening.md](security/security-hardening.md)

### Development
- **Frontend Development**: See `frontend/README.md`
- **Backend Development**: See `backend/README.md`
- **Database Schema**: See `backend/models/`

### Support
- **Issues**: Create an issue in the repository
- **Discussions**: Use repository discussions
- **Security Issues**: Email <EMAIL>

## 🎉 You're Ready!

Your BOM Generator is now set up and running! Here's what you can do next:

1. **Access the application** at http://localhost:3000 (dev) or http://localhost (prod)
2. **Create an account** and start uploading technical drawings
3. **Explore the API** at http://localhost:8000/docs
4. **Monitor performance** at http://localhost:3000 (Grafana)
5. **Check system health** with `./health-check.sh`

Happy generating! 🚀