#!/bin/bash

# Production Update Script for BOM Generator
set -e

echo "🔄 Starting production update..."

# Check if .env.prod exists
if [ ! -f .env.prod ]; then
    echo "❌ Error: .env.prod file not found"
    exit 1
fi

# Load environment variables
source .env.prod

# Create backup before update
echo "💾 Creating backup before update..."
docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/full

# Pull latest code (if using git)
if [ -d ".git" ]; then
    echo "📥 Pulling latest code..."
    git pull origin main
fi

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker-compose -f docker-compose.prod.yml pull

# Build updated images
echo "🔨 Building updated images..."
docker-compose -f docker-compose.prod.yml build --no-cache

# Run database migrations
echo "🔄 Running database migrations..."
docker-compose -f docker-compose.prod.yml run --rm backend alembic upgrade head

# Rolling update - update services one by one to minimize downtime
echo "🔄 Performing rolling update..."

# Update backend services
echo "Updating backend..."
docker-compose -f docker-compose.prod.yml up -d --no-deps backend
sleep 30

echo "Updating celery workers..."
docker-compose -f docker-compose.prod.yml up -d --no-deps celery-worker celery-beat
sleep 15

echo "Updating frontend..."
docker-compose -f docker-compose.prod.yml up -d --no-deps frontend
sleep 15

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
services=("frontend" "backend" "celery-worker")

for service in "${services[@]}"; do
    echo "Checking $service..."
    timeout=60
    while ! docker-compose -f docker-compose.prod.yml ps | grep $service | grep -q "healthy"; do
        sleep 5
        timeout=$((timeout - 5))
        if [ $timeout -le 0 ]; then
            echo "❌ $service failed to become healthy, rolling back..."
            # Rollback logic could be implemented here
            exit 1
        fi
    done
    echo "✅ $service is healthy"
done

# Clean up old images
echo "🧹 Cleaning up old Docker images..."
docker image prune -f

echo ""
echo "✅ Update completed successfully!"
echo ""
echo "📊 Service Status:"
docker-compose -f docker-compose.prod.yml ps