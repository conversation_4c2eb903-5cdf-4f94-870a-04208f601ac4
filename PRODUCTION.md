# Production Deployment Guide

## Overview

This guide covers the production deployment and management of the Automated 3D Model & Bill of Materials Generator application.

## Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04 LTS or newer / CentOS 8 or newer
- **CPU**: 4+ cores recommended
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 100GB minimum, SSD recommended
- **Network**: Stable internet connection

### Software Requirements
- Docker 24.0+
- Docker Compose 2.0+
- Git
- OpenSSL (for SSL certificates)

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bom-generator
   ```

2. **Configure environment**
   ```bash
   cp .env.prod.example .env.prod
   # Edit .env.prod with your production values
   ```

3. **Deploy**
   ```bash
   ./deploy.sh
   ```

4. **Verify deployment**
   ```bash
   ./health-check.sh
   ```

## Detailed Setup

### 1. Environment Configuration

Copy the example environment file and configure it:

```bash
cp .env.prod.example .env.prod
```

**Required Variables:**
- `DB_PASSWORD`: Strong database password
- `JWT_SECRET_KEY`: 32+ character secret key
- `MINIO_ACCESS_KEY`: MinIO access key
- `MINIO_SECRET_KEY`: MinIO secret key
- `GRAFANA_PASSWORD`: Grafana admin password

**Optional Variables:**
- `BACKUP_S3_BUCKET`: S3 bucket for remote backups
- `AWS_ACCESS_KEY_ID`: AWS access key for backups
- `AWS_SECRET_ACCESS_KEY`: AWS secret key for backups
- `ADMIN_EMAIL`: Initial admin user email
- `ADMIN_PASSWORD`: Initial admin user password

### 2. SSL Configuration (Recommended)

For HTTPS support, place your SSL certificates in the `nginx/ssl/` directory:

```bash
mkdir -p nginx/ssl
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem
chmod 600 nginx/ssl/*
```

### 3. Firewall Configuration

Configure your firewall to allow only necessary ports:

```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

## Services Overview

### Core Application Services
- **Frontend**: React application served by Nginx
- **Backend**: FastAPI application
- **Celery Worker**: Background task processing
- **Celery Beat**: Scheduled task management

### Data Services
- **PostgreSQL**: Primary database
- **Redis**: Caching and task queue
- **MinIO**: File storage

### Monitoring Services
- **Prometheus**: Metrics collection
- **Grafana**: Monitoring dashboards
- **Node Exporter**: System metrics

### Support Services
- **Backup**: Automated backup service

## Management Commands

### Deployment
```bash
./deploy.sh          # Initial deployment
./update.sh          # Update existing deployment
./health-check.sh    # Health check all services
```

### Service Management
```bash
# View all services
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f [service]

# Restart service
docker-compose -f docker-compose.prod.yml restart [service]

# Scale service
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

### Database Management
```bash
# Run migrations
docker-compose -f docker-compose.prod.yml run --rm backend alembic upgrade head

# Database backup
docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/database

# Database shell
docker-compose -f docker-compose.prod.yml exec postgres psql -U bomgen -d bomgen_prod
```

### File Storage Management
```bash
# MinIO console access
# Navigate to http://localhost:9001

# File backup
docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/files
```

## Monitoring & Alerting

### Access Monitoring Dashboards
- **Grafana**: http://localhost:3000 (admin / your_grafana_password)
- **Prometheus**: http://localhost:9090
- **MinIO Console**: http://localhost:9001

### Key Metrics to Monitor
- **System**: CPU, memory, disk usage
- **Application**: Response times, error rates, throughput
- **Database**: Connection count, query performance
- **Storage**: Disk usage, file operations

### Alert Configuration
Alerts are configured in `monitoring/alert_rules.yml`. Key alerts include:
- High CPU/memory usage
- Service downtime
- Database connection issues
- Storage space low
- SSL certificate expiration

## Backup & Recovery

### Automated Backups
- **Database**: Daily at 2:00 AM
- **Files**: Daily at 2:00 AM
- **Retention**: 30 days (configurable)
- **Remote Storage**: Optional S3 backup

### Manual Backup
```bash
# Full backup
docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/full

# Database only
docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/database

# Files only
docker-compose -f docker-compose.prod.yml exec backup curl -X POST http://localhost:8080/backup/files
```

### Recovery Procedures
1. **Database Recovery**:
   ```bash
   # Stop application
   docker-compose -f docker-compose.prod.yml stop backend celery-worker
   
   # Restore database
   docker-compose -f docker-compose.prod.yml exec postgres pg_restore -U bomgen -d bomgen_prod /backups/database_backup_YYYYMMDD_HHMMSS.sql
   
   # Start application
   docker-compose -f docker-compose.prod.yml start backend celery-worker
   ```

2. **File Recovery**:
   ```bash
   # Extract backup
   tar -xzf /backups/files_backup_YYYYMMDD_HHMMSS.tar.gz -C /tmp/restore
   
   # Restore to MinIO (manual process through console)
   ```

## Security

### Security Measures Implemented
- Non-root container users
- Network isolation
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection
- CSRF protection
- Secure headers
- Password hashing
- JWT token security

### Security Checklist
- [ ] SSL certificates configured
- [ ] Firewall rules applied
- [ ] Strong passwords used
- [ ] Regular security updates
- [ ] Backup encryption enabled
- [ ] Monitoring alerts configured
- [ ] Access logs reviewed

### Security Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker images
docker-compose -f docker-compose.prod.yml pull
./update.sh
```

## Performance Optimization

### Scaling Options
```bash
# Scale backend services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3 --scale celery-worker=2

# Database optimization
# Edit database/postgresql.conf for your hardware
```

### Performance Monitoring
- Monitor response times in Grafana
- Check database query performance
- Monitor resource usage
- Review application logs

## Troubleshooting

### Common Issues

1. **Service Won't Start**
   ```bash
   # Check logs
   docker-compose -f docker-compose.prod.yml logs [service]
   
   # Check resource usage
   docker stats
   ```

2. **Database Connection Issues**
   ```bash
   # Check database status
   docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U bomgen -d bomgen_prod
   
   # Check connection limits
   docker-compose -f docker-compose.prod.yml exec postgres psql -U bomgen -d bomgen_prod -c "SELECT count(*) FROM pg_stat_activity;"
   ```

3. **High Memory Usage**
   ```bash
   # Check container memory usage
   docker stats
   
   # Restart services if needed
   docker-compose -f docker-compose.prod.yml restart
   ```

4. **Storage Issues**
   ```bash
   # Check disk usage
   df -h
   
   # Clean up old Docker images
   docker system prune -a
   ```

### Log Locations
- Application logs: `./logs/`
- Container logs: `docker-compose logs`
- System logs: `/var/log/`

## Maintenance

### Regular Maintenance Tasks
- **Daily**: Monitor dashboards, check alerts
- **Weekly**: Review logs, check backups
- **Monthly**: Security updates, performance review
- **Quarterly**: Full security audit, disaster recovery test

### Update Procedure
1. Create backup
2. Test updates in staging
3. Schedule maintenance window
4. Run `./update.sh`
5. Verify functionality
6. Monitor for issues

## Support

### Getting Help
- Check logs first
- Review monitoring dashboards
- Run health checks
- Consult troubleshooting section

### Contact Information
- Technical Support: <EMAIL>
- Security Issues: <EMAIL>
- Emergency: +1-XXX-XXX-XXXX

## Appendix

### Port Reference
- 80: HTTP (Frontend)
- 443: HTTPS (Frontend)
- 3000: Grafana
- 5432: PostgreSQL (internal)
- 6379: Redis (internal)
- 8000: Backend API
- 9000: MinIO API (internal)
- 9001: MinIO Console
- 9090: Prometheus

### File Locations
- Configuration: `.env.prod`
- SSL Certificates: `nginx/ssl/`
- Logs: `./logs/`
- Backups: `./backups/`
- Database Data: Docker volume `postgres_data`
- File Storage: Docker volume `minio_data`