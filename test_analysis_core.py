#!/usr/bin/env python3
"""
Core functionality test for drawing analysis orchestration service
"""

import cv2
import numpy as np
import tempfile
import os
import asyncio
from unittest.mock import Mock
import sys
import json

# Add backend to path
sys.path.append('backend')
sys.path.append('.')

from processing.computer_vision.feature_extractor import ComputerVisionEngine
from processing.ocr.text_extractor import OCRTextExtractor
from backend.services.drawing_analysis_service import (
    ConfidenceAssessor, ErrorAggregator, ResultCacheManager
)


def create_test_drawing():
    """Create a test engineering drawing"""
    print("Creating test drawing...")
    
    # Create a simple test drawing
    image = np.ones((800, 1200, 3), dtype=np.uint8) * 255
    
    # Add geometric features
    cv2.rectangle(image, (100, 100), (300, 200), (0, 0, 0), 2)
    cv2.circle(image, (500, 150), 50, (0, 0, 0), 2)
    cv2.line(image, (100, 300), (600, 300), (0, 0, 0), 2)
    cv2.line(image, (100, 350), (600, 350), (0, 0, 0), 1)  # Dimension line
    
    # Add text annotations
    cv2.putText(image, "PART-001", (100, 400), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "STEEL A36", (100, 450), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "100.0 +/- 0.1", (100, 500), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(image, "QTY: 2", (100, 550), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Add title block
    cv2.rectangle(image, (800, 600), (1150, 750), (0, 0, 0), 2)
    cv2.putText(image, "TITLE BLOCK", (820, 650), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(image, "DWG-001", (820, 680), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(image, "REV A", (820, 710), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    return image


def test_computer_vision_engine():
    """Test computer vision engine"""
    print("\n=== Testing Computer Vision Engine ===")
    
    try:
        # Create test image
        test_image = create_test_drawing()
        
        # Initialize CV engine
        cv_engine = ComputerVisionEngine()
        
        # Analyze drawing
        print("Running CV analysis...")
        cv_result = cv_engine.analyze_drawing(test_image)
        
        # Display results
        print(f"✓ CV Analysis completed in {cv_result.processing_time:.2f} seconds")
        print(f"✓ Image quality score: {cv_result.image_quality_score:.1f}/100")
        print(f"✓ Sections identified: {len(cv_result.sections)}")
        print(f"✓ Features detected: {len(cv_result.features)}")
        print(f"✓ Overall confidence: {cv_result.overall_confidence:.1f}%")
        
        if cv_result.errors:
            print(f"⚠ Errors: {len(cv_result.errors)}")
            for error in cv_result.errors:
                print(f"  - {error}")
        
        if cv_result.warnings:
            print(f"⚠ Warnings: {len(cv_result.warnings)}")
            for warning in cv_result.warnings:
                print(f"  - {warning}")
        
        # Display section details
        print("\nSection Details:")
        for i, section in enumerate(cv_result.sections):
            print(f"  {i+1}. {section.section_type}: {len(section.features)} features, confidence {section.confidence:.1f}")
        
        # Display feature details
        print("\nFeature Summary:")
        feature_types = {}
        for feature in cv_result.features:
            ftype = feature.feature_type.value
            feature_types[ftype] = feature_types.get(ftype, 0) + 1
        
        for ftype, count in feature_types.items():
            print(f"  {ftype}: {count}")
        
        return cv_result
        
    except Exception as e:
        print(f"✗ CV Engine test failed: {str(e)}")
        return None


def test_ocr_engine():
    """Test OCR engine"""
    print("\n=== Testing OCR Engine ===")
    
    try:
        # Create test image
        test_image = create_test_drawing()
        
        # Initialize OCR engine
        ocr_extractor = OCRTextExtractor()
        
        # Extract text
        print("Running OCR analysis...")
        ocr_result = ocr_extractor.extract_text_from_image(test_image)
        
        # Display results
        print(f"✓ OCR Analysis completed in {ocr_result.processing_time:.2f} seconds")
        print(f"✓ Texts extracted: {len(ocr_result.extracted_texts)}")
        print(f"✓ Overall confidence: {ocr_result.overall_confidence:.1f}%")
        
        if ocr_result.errors:
            print(f"⚠ Errors: {len(ocr_result.errors)}")
            for error in ocr_result.errors:
                print(f"  - {error}")
        
        if ocr_result.warnings:
            print(f"⚠ Warnings: {len(ocr_result.warnings)}")
            for warning in ocr_result.warnings:
                print(f"  - {warning}")
        
        # Display extracted texts
        print("\nExtracted Texts:")
        for i, text in enumerate(ocr_result.extracted_texts):
            print(f"  {i+1}. '{text.text}' ({text.text_type.value}, confidence: {text.confidence:.1f}%)")
        
        # Test pattern extraction
        print("\nTesting pattern extraction...")
        patterns = ocr_extractor.extract_specific_patterns(test_image)
        
        for pattern_type, items in patterns.items():
            if items:
                print(f"  {pattern_type}: {items}")
        
        return ocr_result
        
    except Exception as e:
        print(f"✗ OCR Engine test failed: {str(e)}")
        return None


def test_confidence_assessor(cv_result, ocr_result):
    """Test confidence assessment"""
    print("\n=== Testing Confidence Assessor ===")
    
    try:
        assessor = ConfidenceAssessor()
        
        # Assess confidence
        print("Assessing overall confidence...")
        confidence_assessment = assessor.assess_overall_confidence(cv_result, ocr_result)
        
        print(f"✓ Overall confidence: {confidence_assessment['overall_confidence']:.1f}%")
        print(f"✓ CV confidence: {confidence_assessment['cv_confidence']:.1f}%")
        print(f"✓ OCR confidence: {confidence_assessment['ocr_confidence']:.1f}%")
        print(f"✓ Image quality: {confidence_assessment['image_quality_score']:.1f}/100")
        
        # Display reliability factors
        print("\nReliability Factors:")
        for factor, status in confidence_assessment['reliability_factors'].items():
            status_icon = "✓" if status else "✗"
            print(f"  {status_icon} {factor.replace('_', ' ').title()}: {status}")
        
        # Generate quality report
        print("\nGenerating quality report...")
        quality_report = assessor.generate_quality_report(cv_result, ocr_result, confidence_assessment)
        
        print(f"✓ Overall quality: {quality_report['overall_quality']}")
        print(f"✓ Quality score: {quality_report['quality_score']:.1f}/100")
        
        if quality_report['issues']:
            print("\nIssues identified:")
            for issue in quality_report['issues']:
                print(f"  ⚠ {issue}")
        
        if quality_report['recommendations']:
            print("\nRecommendations:")
            for rec in quality_report['recommendations']:
                print(f"  💡 {rec}")
        
        return confidence_assessment, quality_report
        
    except Exception as e:
        print(f"✗ Confidence Assessor test failed: {str(e)}")
        return None, None


def test_error_aggregator(cv_result, ocr_result):
    """Test error aggregation"""
    print("\n=== Testing Error Aggregator ===")
    
    try:
        aggregator = ErrorAggregator()
        
        # Aggregate errors
        print("Aggregating errors...")
        error_aggregation = aggregator.aggregate_errors(cv_result, ocr_result)
        
        print(f"✓ Total errors: {error_aggregation['total_errors']}")
        print(f"✓ Total warnings: {error_aggregation['total_warnings']}")
        print(f"✓ Critical errors: {len(error_aggregation['critical_errors'])}")
        print(f"✓ Recoverable errors: {len(error_aggregation['recoverable_errors'])}")
        
        # Display error categories
        print("\nError Categories:")
        for category, errors in error_aggregation['error_categories'].items():
            if errors:
                print(f"  {category}: {len(errors)} errors")
        
        # Display user-friendly messages
        if error_aggregation['user_friendly_messages']:
            print("\nUser-Friendly Messages:")
            for msg in error_aggregation['user_friendly_messages']:
                print(f"  {msg['type'].upper()}: {msg['title']}")
                print(f"    {msg['message']}")
                if msg.get('suggestions'):
                    for suggestion in msg['suggestions']:
                        print(f"    💡 {suggestion}")
        
        return error_aggregation
        
    except Exception as e:
        print(f"✗ Error Aggregator test failed: {str(e)}")
        return None


def test_cache_manager():
    """Test result cache manager"""
    print("\n=== Testing Cache Manager ===")
    
    try:
        # Mock Redis client
        mock_redis = Mock()
        mock_redis.setex.return_value = True
        mock_redis.get.return_value = None
        mock_redis.delete.return_value = True
        
        cache_manager = ResultCacheManager(mock_redis)
        
        # Test progress tracking
        print("Testing progress tracking...")
        from backend.services.drawing_analysis_service import AnalysisProgress, AnalysisStatus
        
        progress = AnalysisProgress(
            status=AnalysisStatus.CV_ANALYSIS,
            progress_percentage=50.0,
            current_stage="Analyzing geometric features",
            estimated_time_remaining=30.0,
            errors=[],
            warnings=[]
        )
        
        success = cache_manager.update_progress(1, progress)
        print(f"✓ Progress update: {success}")
        
        # Test result caching
        print("Testing result caching...")
        from backend.services.drawing_analysis_service import AnalysisOutput
        
        analysis_output = AnalysisOutput(
            design_id=1,
            cv_results={'features': [], 'sections': []},
            ocr_results={'extracted_texts': []},
            confidence_assessment={'overall_confidence': 75.0},
            quality_report={'overall_quality': 'good'},
            processing_time=5.2,
            errors=[],
            warnings=[],
            created_at="2025-01-18 10:30:00"
        )
        
        success = cache_manager.cache_result(1, analysis_output)
        print(f"✓ Result caching: {success}")
        
        return True
        
    except Exception as e:
        print(f"✗ Cache Manager test failed: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Drawing Analysis Core Tests")
    print("=" * 50)
    
    # Test CV engine
    cv_result = test_computer_vision_engine()
    if not cv_result:
        print("❌ CV Engine test failed - stopping tests")
        return
    
    # Test OCR engine
    ocr_result = test_ocr_engine()
    if not ocr_result:
        print("❌ OCR Engine test failed - stopping tests")
        return
    
    # Test confidence assessor
    confidence_assessment, quality_report = test_confidence_assessor(cv_result, ocr_result)
    if not confidence_assessment:
        print("❌ Confidence Assessor test failed")
        return
    
    # Test error aggregator
    error_aggregation = test_error_aggregator(cv_result, ocr_result)
    if not error_aggregation:
        print("❌ Error Aggregator test failed")
        return
    
    # Test cache manager
    cache_success = test_cache_manager()
    if not cache_success:
        print("❌ Cache Manager test failed")
        return
    
    print("\n" + "=" * 50)
    print("🎉 All Drawing Analysis Core Tests Passed!")
    print("\n📊 Summary:")
    print(f"  • CV Analysis: {len(cv_result.features)} features, {len(cv_result.sections)} sections")
    print(f"  • OCR Analysis: {len(ocr_result.extracted_texts)} texts extracted")
    print(f"  • Overall Confidence: {confidence_assessment['overall_confidence']:.1f}%")
    print(f"  • Quality Assessment: {quality_report['overall_quality']}")
    print(f"  • Total Processing Time: {cv_result.processing_time + ocr_result.processing_time:.2f}s")
    
    if error_aggregation['total_errors'] > 0:
        print(f"  • Errors: {error_aggregation['total_errors']} total")
    if error_aggregation['total_warnings'] > 0:
        print(f"  • Warnings: {error_aggregation['total_warnings']} total")
    
    print("\n✅ Drawing Analysis Orchestration Service is ready for integration!")


if __name__ == "__main__":
    main()