# Automated 3D Model & Bill of Materials Generator

A web-based application that analyzes 2D engineering drawings to automatically generate Bills of Materials (BOM), calculate total assembly weight, and create interactive 3D models using computer vision, OCR, and 3D modeling technologies.

## Features

- **Drawing Analysis**: Upload PDF, DXF, and image files for automatic analysis
- **Bill of Materials Generation**: Extract parts lists and generate structured BOMs
- **Weight Calculations**: Calculate individual part and total assembly weights
- **3D Model Generation**: Create interactive 3D models from 2D drawings
- **User Management**: Secure authentication and user dashboards
- **Design Sharing**: Collaborate and share designs with other users
- **Fabrication Tracking**: Monitor production progress and timelines

## Technology Stack

### Frontend
- React 18 with TypeScript
- Material-UI for UI components
- Three.js for 3D visualization
- React Router for navigation

### Backend
- FastAPI (Python) for API services
- PostgreSQL for data storage
- Redis for session management
- Celery for background processing

### Processing Engines
- OpenCV for computer vision
- Tesseract OCR for text recognition
- OpenSCAD for 3D model generation

## Development Setup

### Prerequisites
- Python 3.11+
- Node.js 18+
- <PERSON><PERSON> and Docker Compose

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd automated-3d-bom-generator
   ```

2. **Start development services**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **Set up Python environment**
   ```bash
   ./setup_env.sh
   source venv/bin/activate
   ```

4. **Start backend server**
   ```bash
   cd backend
   uvicorn main:app --reload
   ```

5. **Start frontend development server**
   ```bash
   cd frontend
   npm start
   ```

### Environment Configuration

Copy the example environment file and configure your settings:
```bash
cp backend/.env.example backend/.env
```

Edit the `.env` file with your specific configuration values.

## Project Structure

```
├── backend/                 # FastAPI backend services
│   ├── api/                # API endpoints
│   ├── models/             # Database models
│   ├── services/           # Business logic services
│   ├── main.py             # FastAPI application entry point
│   └── requirements.txt    # Python dependencies
├── frontend/               # React TypeScript frontend
│   ├── src/               # Source code
│   ├── public/            # Static assets
│   └── package.json       # Node.js dependencies
├── processing/            # Processing modules
│   ├── computer_vision/   # OpenCV processing
│   ├── ocr/              # Tesseract OCR
│   └── model_generation/ # 3D model generation
├── database/             # Database initialization
└── docker-compose.dev.yml # Development services
```

## API Documentation

Once the backend is running, visit:
- API Documentation: http://localhost:8000/docs
- Alternative Docs: http://localhost:8000/redoc

## Development Services

The development environment includes:
- **PostgreSQL**: Database (port 5432)
- **Redis**: Session store and task queue (port 6379)
- **MinIO**: File storage (port 9000, console: 9001)

Access MinIO console at http://localhost:9001 with credentials:
- Username: `minioadmin`
- Password: `minioadmin123`

## Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.